# 🚨 URGENT FIX 2 - للمطور التالي

**التاريخ**: 19 يوليو 2025
**المطور السابق**: Augment Agent
**الوقت المقدر للحل**: 2-3 ساعات
**نقطة الاستعادة**: Checkpoint 5

---

## ⚠️ المهام العاجلة المطلوبة

### **🎯 المهمة الأساسية: تحسين واجهة الجداول والفلاتر**

#### **1. إضافة فلاتر متقدمة في صف واحد:**
- **الملف**: `dpcdz/templates/coordination_center/unified_morning_check.html`
- **الموقع**: `div.table-controls` (السطر 264-280)
- **المطلوب إضافته**:

```html
<div class="table-controls">
    <!-- الصف الأول: البحث والفلاتر الأساسية -->
    <div class="filter-row">
        <input type="text" id="personnelSearch" class="form-control search-input"
               placeholder="البحث في الأعوان...">
        <select id="personnelFilter" class="form-control filter-select">
            <option value="">جميع الأعوان</option>
            <option value="present">الحاضرون</option>
            <option value="absent">الغائبون</option>
            <option value="on_mission">في مهمة</option>
            <option value="standby">الاحتياطي</option>
        </select>

        <!-- فلاتر الفرق -->
        <select id="shiftFilter" class="form-control filter-select">
            <option value="">جميع الفرق</option>
            <option value="shift_1">الفرقة الأولى (A)</option>
            <option value="shift_2">الفرقة الثانية (B)</option>
            <option value="shift_3">الفرقة الثالثة (C)</option>
        </select>

        <!-- فلتر نظام العمل -->
        <select id="workSystemFilter" class="form-control filter-select">
            <option value="">جميع الأنظمة</option>
            <option value="24_hours">نظام 24 ساعة</option>
            <option value="8_hours">نظام 8 ساعات</option>
        </select>

        <div class="table-info">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                الجدول قابل للتمرير - حد أقصى 6 صفوف
            </small>
        </div>
    </div>
</div>
```

#### **2. إضافة CSS للفلاتر:**
- **الموقع**: في قسم `<style>` بعد السطر 1980
- **المطلوب إضافته**:

```css
.filter-row {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
}

.search-input {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
}

.filter-select {
    min-width: 150px;
    max-width: 180px;
}
```

#### **3. جعل الأعمدة ثابتة عند التمرير:**

##### **أ. عمود "الاسم الكامل" (العمود الثاني):**
```css
.unified-table th:nth-child(2), /* الاسم */
.unified-table td:nth-child(2) {
    min-width: 140px;
    max-width: 160px;
    text-align: center; /* وسط العمود */
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
    position: sticky;
    left: 110px; /* بعد عمود رقم التسجيل */
    background: #fff;
    z-index: 5;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    border-right: 2px solid #28a745;
}
```

##### **ب. عمود "نوع الوسيلة" (جدول الوسائل):**
```css
#vehiclesTable th:nth-child(2), /* نوع الوسيلة */
#vehiclesTable td:nth-child(2) {
    min-width: 120px;
    max-width: 140px;
    text-align: center; /* وسط العمود */
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
    position: sticky;
    left: 140px; /* بعد عمود الرقم التسلسلي */
    background: #fff;
    z-index: 5;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    border-right: 2px solid #28a745;
}
```

##### **ج. جعل العمود الأول ثابت أيضاً:**
```css
.unified-table th:nth-child(1), /* رقم التسجيل */
.unified-table td:nth-child(1) {
    position: sticky;
    left: 0;
    background: #fff;
    z-index: 6;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    border-right: 2px solid #007bff;
}

#vehiclesTable th:nth-child(1), /* الرقم التسلسلي */
#vehiclesTable td:nth-child(1) {
    position: sticky;
    left: 0;
    background: #fff;
    z-index: 6;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    border-right: 2px solid #007bff;
}
```

#### **4. إضافة JavaScript للفلترة:**
- **الموقع**: قبل `</script>` في نهاية الملف
- **المطلوب إضافته**:

```javascript
// فلترة الأعوان حسب الفرقة ونظام العمل
function filterPersonnel() {
    const searchTerm = document.getElementById('personnelSearch').value.toLowerCase();
    const statusFilter = document.getElementById('personnelFilter').value;
    const shiftFilter = document.getElementById('shiftFilter').value;
    const workSystemFilter = document.getElementById('workSystemFilter').value;

    const rows = document.querySelectorAll('#personnelTable tbody tr');

    rows.forEach(row => {
        const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const status = row.getAttribute('data-status');
        const shiftBadge = row.querySelector('.badge');
        const workSystemBadge = row.querySelector('.badge');

        let showRow = true;

        // فلترة البحث
        if (searchTerm && !name.includes(searchTerm)) {
            showRow = false;
        }

        // فلترة الحالة
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }

        // فلترة الفرقة
        if (shiftFilter && shiftBadge) {
            const shiftText = shiftBadge.textContent;
            if (shiftFilter === 'shift_1' && !shiftText.includes('الأولى')) showRow = false;
            if (shiftFilter === 'shift_2' && !shiftText.includes('الثانية')) showRow = false;
            if (shiftFilter === 'shift_3' && !shiftText.includes('الثالثة')) showRow = false;
        }

        // فلترة نظام العمل
        if (workSystemFilter && workSystemBadge) {
            const workSystemText = workSystemBadge.textContent;
            if (workSystemFilter === '24_hours' && !workSystemText.includes('24')) showRow = false;
            if (workSystemFilter === '8_hours' && !workSystemText.includes('8')) showRow = false;
        }

        row.style.display = showRow ? '' : 'none';
    });
}

// ربط الفلاتر بالأحداث
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('personnelSearch').addEventListener('input', filterPersonnel);
    document.getElementById('personnelFilter').addEventListener('change', filterPersonnel);
    document.getElementById('shiftFilter').addEventListener('change', filterPersonnel);
    document.getElementById('workSystemFilter').addEventListener('change', filterPersonnel);
});
```

---

## 🧪 الاختبار المطلوب

### **اختبار الفلاتر:**
1. افتح: `http://127.0.0.1:8000/coordination-center/unified-morning-check/?unit_id=11`
2. اختبر فلتر الفرق: اختر "الفرقة الأولى (A)"
3. اختبر فلتر نظام العمل: اختر "نظام 24 ساعة"
4. اختبر البحث: ابحث عن اسم عون
5. اختبر الفلترة المتعددة: استخدم عدة فلاتر معاً

### **اختبار الأعمدة الثابتة:**
6. **التمرير الأفقي**: مرر يميناً ويساراً
7. **ثبات الأعمدة**: تحقق من بقاء "الاسم الكامل" و "نوع الوسيلة" مرئيين
8. **الوسط**: تحقق من وضع النص في وسط الأعمدة
9. **التأثيرات البصرية**: تحقق من الظلال والحدود الملونة

---

## 📊 النتيجة المتوقعة

- ✅ فلاتر متقدمة في صف واحد (فرق A,B,C + نظام 8/24 ساعة)
- ✅   أعمدة "الاسم الكامل" و "نوع الوسيلة" ثابتة عند التمرير الى اليمين او اليسار 
- ✅ النص في وسط الأعمدة الثابتة
- ✅ تأثيرات بصرية جميلة (ظلال + حدود ملونة)
- ✅ فلترة ديناميكية متقدمة

---

## ✅ بعد الحل

1. اختبر جميع الفلاتر والأعمدة الثابتة
2. حدث `Memory_check.md` بالنتائج
3. احذف هذا الملف إذا تم الحل بنجاح

**حظ موفق!** 🚀