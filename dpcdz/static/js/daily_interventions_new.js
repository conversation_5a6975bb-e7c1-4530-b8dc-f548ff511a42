/**
 * نظام التدخلات اليومية الجديد والمبسط
 * تم إنشاؤه لحل جميع مشاكل النظام القديم
 * 
 * المشاكل التي يحلها:
 * 1. اختلاط البيانات بين التدخلات
 * 2. عدم تغيير النموذج حسب نوع التدخل
 * 3. فتح النموذج تلقائياً عند تحديث الصفحة
 * 4. خطأ "لم يتم تحديد التدخل"
 * 5. عدم عمل إنهاء المهمة
 */

class DailyInterventionsManager {
    constructor() {
        this.currentInterventionId = null;
        this.currentForm = null;
        this.isFormOpen = false;
        this.interventionData = {};
        
        console.log('🚀 تم تهيئة مدير التدخلات اليومية الجديد');
        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        this.setupEventListeners();
        this.loadInterventionsData();
        this.handleURLParams();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // أزرار فتح النماذج
        document.getElementById('btn-initial-report')?.addEventListener('click', () => {
            this.openForm('initial-report');
        });

        // أزرار الحفظ
        document.getElementById('save-initial-report')?.addEventListener('click', () => {
            this.saveInitialReport();
        });

        document.getElementById('save-reconnaissance')?.addEventListener('click', () => {
            this.saveReconnaissance();
        });

        document.getElementById('save-complete-mission')?.addEventListener('click', () => {
            this.saveCompletion();
        });

        // أزرار الإغلاق
        document.querySelectorAll('.close-form').forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeAllForms();
            });
        });

        // تغيير نوع التدخل
        document.getElementById('intervention-type')?.addEventListener('change', (e) => {
            this.handleInterventionTypeChange(e.target.value);
        });

        // زر التحديث
        document.getElementById('refresh-table')?.addEventListener('click', () => {
            this.loadInterventionsData();
        });
    }

    /**
     * فتح نموذج معين
     */
    openForm(formType, interventionId = null) {
        console.log(`📝 فتح نموذج: ${formType}`, interventionId ? `للتدخل: ${interventionId}` : '');

        // إغلاق جميع النماذج أولاً
        this.closeAllForms();

        // تعيين معرف التدخل إذا تم تمريره
        if (interventionId) {
            this.setCurrentInterventionId(interventionId);
        }

        // تحديد النموذج الحالي
        this.currentForm = formType;
        this.isFormOpen = true;

        // إظهار النموذج المطلوب
        switch (formType) {
            case 'initial-report':
                this.showInitialReportForm();
                break;
            case 'reconnaissance':
                this.showReconnaissanceForm(interventionId);
                break;
            case 'completion':
                this.showCompletionForm(interventionId);
                break;
        }
    }

    /**
     * إظهار نموذج البلاغ الأولي
     */
    showInitialReportForm() {
        // مسح النموذج
        this.clearForm('initialReportForm');
        
        // إظهار النموذج
        this.showFormContainer('initial-report-form', 'البلاغ الأولي', 'تسجيل بلاغ جديد');
        
        // تحميل الوسائل المتاحة
        this.loadAvailableVehicles();
    }

    /**
     * إظهار نموذج التعرف
     */
    async showReconnaissanceForm(interventionId) {
        if (!interventionId) {
            this.showError('لم يتم تحديد التدخل');
            return;
        }

        try {
            // جلب بيانات التدخل
            const response = await fetch(`/api/interventions/get-details/${interventionId}/`);
            const data = await response.json();

            if (data.success) {
                // مسح النموذج
                this.clearForm('reconnaissanceForm');
                
                // ملء النموذج بالبيانات
                this.fillReconnaissanceForm(data.intervention);
                
                // إظهار النموذج
                this.showFormContainer('reconnaissance-form', 'عملية التعرف', 'تحديث بيانات التعرف');
            } else {
                this.showError('خطأ في جلب بيانات التدخل: ' + data.message);
            }
        } catch (error) {
            console.error('خطأ في جلب بيانات التدخل:', error);
            this.showError('حدث خطأ في جلب بيانات التدخل');
        }
    }

    /**
     * إظهار نموذج إنهاء المهمة
     */
    async showCompletionForm(interventionId) {
        if (!interventionId) {
            this.showError('لم يتم تحديد التدخل');
            return;
        }

        try {
            // جلب بيانات التدخل
            const response = await fetch(`/api/interventions/get-details/${interventionId}/`);
            const data = await response.json();

            if (data.success) {
                // مسح النموذج
                this.clearForm('completeMissionForm');
                
                // ملء النموذج بالبيانات
                this.fillCompletionForm(data.intervention);
                
                // إظهار النموذج
                this.showFormContainer('complete-mission-form', 'إنهاء المهمة', 'تسجيل النتائج النهائية');
            } else {
                this.showError('خطأ في جلب بيانات التدخل: ' + data.message);
            }
        } catch (error) {
            console.error('خطأ في جلب بيانات التدخل:', error);
            this.showError('حدث خطأ في جلب بيانات التدخل');
        }
    }

    /**
     * تعيين معرف التدخل الحالي مع حماية إضافية
     */
    setCurrentInterventionId(interventionId) {
        // التحقق من صحة المعرف
        if (!interventionId || isNaN(interventionId)) {
            console.error('❌ معرف التدخل غير صحيح:', interventionId);
            return false;
        }

        this.currentInterventionId = parseInt(interventionId);

        // حفظ في localStorage كنسخة احتياطية
        localStorage.setItem('currentInterventionId', this.currentInterventionId);

        // حفظ في window object أيضاً للتوافق مع الكود القديم
        window.currentInterventionId = this.currentInterventionId;

        console.log('✅ تم تعيين معرف التدخل:', this.currentInterventionId);
        console.log('✅ تم حفظ المعرف في localStorage و window');

        return true;
    }

    /**
     * الحصول على معرف التدخل الحالي مع استرداد من مصادر متعددة
     */
    getCurrentInterventionId() {
        // محاولة الحصول على المعرف من المصادر المختلفة
        let interventionId = this.currentInterventionId;

        if (!interventionId) {
            // محاولة الاسترداد من localStorage
            interventionId = localStorage.getItem('currentInterventionId');
            if (interventionId) {
                this.currentInterventionId = parseInt(interventionId);
                console.log('🔄 تم استرداد معرف التدخل من localStorage:', interventionId);
            }
        }

        if (!interventionId) {
            // محاولة الاسترداد من window object
            interventionId = window.currentInterventionId;
            if (interventionId) {
                this.currentInterventionId = parseInt(interventionId);
                console.log('🔄 تم استرداد معرف التدخل من window:', interventionId);
            }
        }

        return this.currentInterventionId;
    }

    /**
     * مسح معرف التدخل الحالي من جميع المصادر
     */
    clearCurrentInterventionId() {
        this.currentInterventionId = null;
        localStorage.removeItem('currentInterventionId');
        window.currentInterventionId = null;
        console.log('🧹 تم مسح معرف التدخل من جميع المصادر');
    }

    /**
     * التحقق من وجود معرف التدخل الحالي
     */
    hasCurrentInterventionId() {
        const id = this.getCurrentInterventionId();
        return id && !isNaN(id) && id > 0;
    }

    /**
     * إغلاق جميع النماذج
     */
    closeAllForms() {
        console.log('🔒 إغلاق جميع النماذج');
        
        // إخفاء جميع النماذج
        document.getElementById('form-header-top').style.display = 'none';
        document.querySelector('.forms-container').style.display = 'none';
        document.getElementById('initial-report-form').style.display = 'none';
        document.getElementById('reconnaissance-form').style.display = 'none';
        document.getElementById('complete-mission-form').style.display = 'none';

        // إعادة تعيين المتغيرات
        this.currentForm = null;
        this.isFormOpen = false;
        
        // لا نمسح currentInterventionId هنا لأنه قد يكون مطلوباً للعمليات اللاحقة
    }

    /**
     * مسح نموذج معين
     */
    clearForm(formId) {
        console.log(`🧹 مسح النموذج: ${formId}`);
        
        const form = document.getElementById(formId);
        if (!form) return;

        // مسح جميع الحقول
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        // إخفاء جميع الأقسام المتخصصة
        this.hideAllDetailSections();
    }

    /**
     * إخفاء جميع أقسام التفاصيل
     */
    hideAllDetailSections() {
        const sections = [
            'medical-details',
            'accident-details', 
            'fire-details',
            'agricultural-fire-details',
            'building-fire-details',
            'intervention-subtype-section',
            'intervention-details-section'
        ];

        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'none';
            }
        });
    }

    /**
     * إظهار حاوية النموذج
     */
    showFormContainer(formId, title, subtitle) {
        // إظهار الحاوية الرئيسية
        document.getElementById('form-header-top').style.display = 'block';
        document.querySelector('.forms-container').style.display = 'block';
        
        // إظهار النموذج المحدد
        document.getElementById(formId).style.display = 'block';
        
        // تحديث العنوان
        const titleElement = document.querySelector('#form-header-top h3');
        const subtitleElement = document.querySelector('#form-header-top p');
        
        if (titleElement) titleElement.textContent = title;
        if (subtitleElement) subtitleElement.textContent = subtitle;
    }

    /**
     * معالجة تغيير نوع التدخل
     */
    handleInterventionTypeChange(interventionType) {
        console.log('🔄 تغيير نوع التدخل إلى:', interventionType);
        
        // إخفاء جميع الأقسام أولاً
        this.hideAllDetailSections();
        
        // إظهار القسم المناسب
        this.showInterventionDetails(interventionType);
        
        // تحديث الأنواع الفرعية
        this.updateSubtypes(interventionType);
    }

    /**
     * إظهار تفاصيل التدخل حسب النوع
     */
    showInterventionDetails(interventionType) {
        const sectionMap = {
            'medical': 'medical-details',
            'accident': 'accident-details',
            'fire': 'fire-details',
            'agricultural-fire': 'agricultural-fire-details',
            'building-fire': 'building-fire-details'
        };

        const sectionId = sectionMap[interventionType];
        if (sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'block';
                console.log('👁️ إظهار قسم:', sectionId);
            }
        }
    }

    /**
     * تحديث الأنواع الفرعية
     */
    updateSubtypes(interventionType) {
        const subtypeSelect = document.getElementById('intervention-subtype');
        if (!subtypeSelect) return;

        // مسح الخيارات الحالية
        subtypeSelect.innerHTML = '<option value="">اختر نوع التدخل الفرعي</option>';

        const subtypes = this.getSubtypesForType(interventionType);
        
        subtypes.forEach(subtype => {
            const option = document.createElement('option');
            option.value = subtype;
            option.textContent = subtype;
            subtypeSelect.appendChild(option);
        });
    }

    /**
     * الحصول على الأنواع الفرعية لنوع التدخل
     */
    getSubtypesForType(interventionType) {
        const subtypesMap = {
            'medical': ['الاختناق', 'التسممات', 'الحروق', 'الانفجارات', 'إجلاء', 'الغرقى'],
            'accident': ['مصدومة-بالمركبات', 'تصادم-المركبات', 'إنقلاب-المركبات', 'مصدومة-بالقطار', 'حوادث-أخرى'],
            'fire': ['حريق-عام'],
            'agricultural-fire': ['حريق-محاصيل-زراعية'],
            'building-fire': ['حرائق-البنايات-والمؤسسات']
        };

        return subtypesMap[interventionType] || ['أخرى'];
    }
}

    /**
     * حفظ البلاغ الأولي
     */
    async saveInitialReport() {
        console.log('💾 حفظ البلاغ الأولي');

        const form = document.getElementById('initialReportForm');
        if (!form.checkValidity()) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        try {
            const data = this.collectInitialReportData();

            const response = await fetch('/api/interventions/save-initial-report/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(`تم حفظ البلاغ الأولي بنجاح<br>رقم التدخل: ${result.intervention_number}`);
                this.closeAllForms();
                this.loadInterventionsData();
            } else {
                this.showError('حدث خطأ في حفظ البلاغ: ' + result.message);
            }
        } catch (error) {
            console.error('خطأ في حفظ البلاغ:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        }
    }

    /**
     * حفظ بيانات التعرف
     */
    async saveReconnaissance() {
        console.log('💾 حفظ بيانات التعرف');
        console.log('🔍 فحص معرف التدخل الحالي...');

        // التحقق من وجود معرف التدخل مع محاولة الاسترداد
        if (!this.hasCurrentInterventionId()) {
            console.error('❌ لم يتم العثور على معرف التدخل');
            this.showError('لم يتم تحديد التدخل. يرجى إعادة فتح النموذج.');
            return;
        }

        const interventionId = this.getCurrentInterventionId();
        console.log('✅ معرف التدخل المستخدم:', interventionId);

        const form = document.getElementById('reconnaissanceForm');
        if (!form.checkValidity()) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        try {
            const data = this.collectReconnaissanceData();

            const response = await fetch('/api/interventions/update-status/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('تم حفظ بيانات التعرف بنجاح');
                this.closeAllForms();
                this.loadInterventionsData();
            } else {
                this.showError('حدث خطأ في حفظ البيانات: ' + result.message);
            }
        } catch (error) {
            console.error('خطأ في حفظ بيانات التعرف:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        }
    }

    /**
     * حفظ بيانات إنهاء المهمة
     */
    async saveCompletion() {
        console.log('💾 حفظ بيانات إنهاء المهمة');
        console.log('🔍 فحص معرف التدخل الحالي...');

        // التحقق من وجود معرف التدخل مع محاولة الاسترداد
        if (!this.hasCurrentInterventionId()) {
            console.error('❌ لم يتم العثور على معرف التدخل');
            this.showError('لم يتم تحديد التدخل. يرجى إعادة فتح النموذج.');
            return;
        }

        const interventionId = this.getCurrentInterventionId();
        console.log('✅ معرف التدخل المستخدم:', interventionId);

        const form = document.getElementById('completeMissionForm');
        if (!form.checkValidity()) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        try {
            const data = this.collectCompletionData();

            const response = await fetch('/api/interventions/complete/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('تم إنهاء المهمة بنجاح');
                this.closeAllForms();
                this.loadInterventionsData();
            } else {
                this.showError('حدث خطأ في إنهاء المهمة: ' + result.message);
            }
        } catch (error) {
            console.error('خطأ في حفظ بيانات إنهاء المهمة:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        }
    }

    /**
     * جمع بيانات البلاغ الأولي
     */
    collectInitialReportData() {
        return {
            unit_id: document.getElementById('unit-select')?.value || 11,
            departure_time: document.getElementById('departure-time')?.value,
            location: document.getElementById('location')?.value,
            intervention_type: document.getElementById('intervention-type')?.value,
            intervention_subtype: document.getElementById('intervention-subtype')?.value,
            contact_source: document.getElementById('contact-source')?.value,
            contact_type: document.getElementById('contact-type')?.value,
            phone_number: document.getElementById('phone-number')?.value,
            caller_name: document.getElementById('caller-name')?.value,
            initial_notes: document.getElementById('initial-notes')?.value,
            vehicle_ids: this.getSelectedVehicles()
        };
    }

    /**
     * جمع بيانات التعرف
     */
    collectReconnaissanceData() {
        const interventionId = this.getCurrentInterventionId();
        console.log('📋 جمع بيانات التعرف للتدخل:', interventionId);

        return {
            intervention_id: interventionId,
            arrival_time: document.getElementById('arrival-time')?.value,
            injured_count: document.getElementById('injured-count')?.value || 0,
            deaths_count: document.getElementById('deaths-count')?.value || 0,
            material_damage: document.getElementById('material-damage')?.value || '',
            status: 'reconnaissance'
        };
    }

    /**
     * جمع بيانات إنهاء المهمة
     */
    collectCompletionData() {
        const interventionId = this.getCurrentInterventionId();
        console.log('📋 جمع بيانات إنهاء المهمة للتدخل:', interventionId);

        return {
            intervention_id: interventionId,
            end_time: document.getElementById('end-time')?.value,
            final_injured_count: document.getElementById('final-injured-count')?.value || 0,
            final_deaths_count: document.getElementById('final-deaths-count')?.value || 0,
            final_notes: document.getElementById('final-notes')?.value || '',
            status: 'completed'
        };
    }

    /**
     * الحصول على الوسائل المحددة
     */
    getSelectedVehicles() {
        const checkboxes = document.querySelectorAll('input[name="vehicles"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    /**
     * الحصول على CSRF Token
     */
    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        // يمكن تحسين هذا لاحقاً بـ modal أو notification
        alert('✅ ' + message);
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // يمكن تحسين هذا لاحقاً بـ modal أو notification
        alert('❌ ' + message);
    }

    /**
     * تحميل بيانات التدخلات
     */
    async loadInterventionsData() {
        console.log('📊 تحميل بيانات التدخلات');

        try {
            const response = await fetch('/api/get-all-interventions-by-stage/');
            const data = await response.json();

            if (data.success) {
                this.displayInterventions(data.interventions);
            } else {
                console.error('خطأ في تحميل البيانات:', data.message);
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }

    /**
     * عرض التدخلات في الجدول
     */
    displayInterventions(interventions) {
        const tbody = document.getElementById('interventions-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        interventions.forEach(intervention => {
            const row = this.createInterventionRow(intervention);
            tbody.appendChild(row);
        });
    }

    /**
     * إنشاء صف تدخل
     */
    createInterventionRow(intervention) {
        const row = document.createElement('tr');
        row.dataset.interventionId = intervention.id;

        const statusClass = this.getStatusClass(intervention.status);
        const actionButtons = this.createActionButtons(intervention);

        row.innerHTML = `
            <td>${intervention.intervention_number}</td>
            <td>${intervention.date}</td>
            <td>${intervention.departure_time}</td>
            <td>${intervention.intervention_type}</td>
            <td>${intervention.location}</td>
            <td>${intervention.vehicles_display}</td>
            <td><span class="status-badge ${statusClass}">${intervention.status_display}</span></td>
            <td>${actionButtons}</td>
        `;

        return row;
    }

    /**
     * الحصول على فئة CSS للحالة
     */
    getStatusClass(status) {
        const statusMap = {
            'initial': 'status-initial',
            'reconnaissance': 'status-reconnaissance',
            'intervention': 'status-intervention',
            'completed': 'status-completed'
        };
        return statusMap[status] || 'status-default';
    }

    /**
     * إنشاء أزرار الإجراءات
     */
    createActionButtons(intervention) {
        let buttons = '';

        if (intervention.status === 'initial') {
            buttons += `<button class="btn btn-sm btn-success" onclick="interventionsManager.openForm('reconnaissance', ${intervention.id})" title="عملية التعرف">
                <i class="fas fa-search"></i>
            </button>`;
        }

        if (intervention.status === 'reconnaissance' || intervention.status === 'intervention') {
            buttons += `<button class="btn btn-sm btn-primary" onclick="interventionsManager.openForm('completion', ${intervention.id})" title="إنهاء المهمة">
                <i class="fas fa-check"></i>
            </button>`;
        }

        buttons += `<button class="btn btn-sm btn-info" onclick="interventionsManager.viewIntervention(${intervention.id})" title="عرض التفاصيل">
            <i class="fas fa-eye"></i>
        </button>`;

        return buttons;
    }

    /**
     * عرض تفاصيل التدخل
     */
    async viewIntervention(interventionId) {
        console.log('👁️ عرض تفاصيل التدخل:', interventionId);
        // يمكن تطوير هذا لاحقاً لعرض modal بالتفاصيل
        alert('سيتم تطوير عرض التفاصيل قريباً');
    }

    /**
     * تحميل الوسائل المتاحة
     */
    async loadAvailableVehicles() {
        try {
            const response = await fetch('/api/interventions/get-available-vehicles/');
            const data = await response.json();

            if (data.success) {
                this.displayAvailableVehicles(data.vehicles);
            }
        } catch (error) {
            console.error('خطأ في تحميل الوسائل:', error);
        }
    }

    /**
     * عرض الوسائل المتاحة
     */
    displayAvailableVehicles(vehicles) {
        const container = document.getElementById('vehicles-container');
        if (!container) return;

        container.innerHTML = '';

        vehicles.forEach(vehicle => {
            const checkbox = document.createElement('div');
            checkbox.className = 'form-check';
            checkbox.innerHTML = `
                <input class="form-check-input" type="checkbox" name="vehicles" value="${vehicle.id}" id="vehicle-${vehicle.id}">
                <label class="form-check-label" for="vehicle-${vehicle.id}">
                    ${vehicle.name} (${vehicle.type})
                </label>
            `;
            container.appendChild(checkbox);
        });
    }

    /**
     * ملء نموذج التعرف بالبيانات
     */
    fillReconnaissanceForm(intervention) {
        console.log('📝 ملء نموذج التعرف بالبيانات');

        // ملء الحقول الأساسية
        this.setFieldValue('arrival-time', intervention.arrival_time || this.getCurrentTime());
        this.setFieldValue('injured-count', intervention.injured_count || 0);
        this.setFieldValue('deaths-count', intervention.deaths_count || 0);
        this.setFieldValue('material-damage', intervention.material_damage || '');

        // إظهار التفاصيل حسب نوع التدخل
        if (intervention.intervention_type) {
            this.showInterventionDetails(intervention.intervention_type);
        }
    }

    /**
     * ملء نموذج إنهاء المهمة بالبيانات
     */
    fillCompletionForm(intervention) {
        console.log('📝 ملء نموذج إنهاء المهمة بالبيانات');

        // ملء الحقول الأساسية
        this.setFieldValue('end-time', intervention.end_time || this.getCurrentTime());
        this.setFieldValue('final-injured-count', intervention.final_injured_count || intervention.injured_count || 0);
        this.setFieldValue('final-deaths-count', intervention.final_deaths_count || intervention.deaths_count || 0);
        this.setFieldValue('final-notes', intervention.final_notes || '');
    }

    /**
     * تعيين قيمة حقل
     */
    setFieldValue(fieldId, value) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = value;
        }
    }

    /**
     * الحصول على الوقت الحالي
     */
    getCurrentTime() {
        const now = new Date();
        return now.toTimeString().slice(0, 5); // HH:MM
    }

    /**
     * معالجة معاملات URL
     */
    handleURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const action = urlParams.get('action');
        const interventionId = urlParams.get('intervention_id');

        if (action && interventionId && !isNaN(interventionId)) {
            // التحقق من أن هذا ليس تحديث صفحة
            const isPageRefresh = performance.navigation.type === performance.navigation.TYPE_RELOAD;

            if (!isPageRefresh) {
                console.log(`🔗 معالجة إجراء من URL: ${action} للتدخل: ${interventionId}`);

                setTimeout(() => {
                    this.openForm(action, parseInt(interventionId));
                }, 500);
            }

            // إزالة المعاملات من URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }
}

// إنشاء مثيل من المدير عند تحميل الصفحة
let interventionsManager;

document.addEventListener('DOMContentLoaded', function() {
    interventionsManager = new DailyInterventionsManager();
});
