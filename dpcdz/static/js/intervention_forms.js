// JavaScript لإدارة النماذج المتخصصة للتدخلات

// ==================== إدارة عرض النماذج ====================

function showInterventionForm(interventionType) {
    // إخفاء جميع النماذج أولاً
    hideAllInterventionForms();
    
    // عرض النموذج المناسب
    switch(interventionType) {
        case 'medical':
            document.getElementById('medical-evacuation-form').style.display = 'block';
            break;
        case 'accident':
            document.getElementById('traffic-accident-form').style.display = 'block';
            break;
        case 'building-fire':
            document.getElementById('building-fire-form').style.display = 'block';
            break;
        case 'agricultural-fire':
            document.getElementById('agricultural-fire-form').style.display = 'block';
            break;
        default:
            console.log('نوع تدخل غير معروف:', interventionType);
    }
}

function hideAllInterventionForms() {
    const forms = [
        'medical-evacuation-form',
        'traffic-accident-form', 
        'building-fire-form',
        'agricultural-fire-form'
    ];
    
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            form.style.display = 'none';
        }
    });
}

// ==================== نموذج الإجلاء الصحي ====================

// إدارة تغيير طبيعة التدخل
document.addEventListener('DOMContentLoaded', function() {
    const medicalNatureSelect = document.getElementById('medical-intervention-nature');
    if (medicalNatureSelect) {
        medicalNatureSelect.addEventListener('change', function() {
            showMedicalNatureDetails(this.value);
        });
    }
    
    // إدارة طلب الدعم
    const medicalSupportSelect = document.getElementById('medical-support-request');
    if (medicalSupportSelect) {
        medicalSupportSelect.addEventListener('change', function() {
            const specializedSection = document.getElementById('specialized-team-section');
            if (this.value === 'specialized_team') {
                specializedSection.style.display = 'block';
            } else {
                specializedSection.style.display = 'none';
            }
        });
    }
});

function showMedicalNatureDetails(nature) {
    // إخفاء جميع التفاصيل
    const details = document.querySelectorAll('.nature-detail');
    details.forEach(detail => detail.style.display = 'none');
    
    // عرض التفاصيل المناسبة
    if (nature) {
        const targetDetail = document.getElementById(nature + '-details');
        if (targetDetail) {
            targetDetail.style.display = 'block';
        }
    }
}

function addMedicalCasualty(type) {
    const listId = type === 'injured' ? 'medical-injured-list' : 'medical-fatalities-list';
    const list = document.getElementById(listId);
    const index = list.children.length;
    
    const casualtyHtml = `
        <div class="casualty-item" data-index="${index}">
            <div class="casualty-info">
                <input type="text" name="${type}_name_${index}" placeholder="الاسم الكامل" class="form-control" style="margin-bottom: 5px;">
                <div style="display: flex; gap: 10px;">
                    <input type="number" name="${type}_age_${index}" placeholder="السن" class="form-control" style="width: 80px;">
                    <select name="${type}_gender_${index}" class="form-control" style="width: 100px;">
                        <option value="">الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
            </div>
            <div class="casualty-actions">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCasualty(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    list.insertAdjacentHTML('beforeend', casualtyHtml);
}

function removeCasualty(button) {
    button.closest('.casualty-item').remove();
}

function saveMedicalEvacuationDetails() {
    // جمع بيانات النموذج
    const formData = new FormData();
    
    // إضافة البيانات الأساسية
    formData.append('location_type', document.getElementById('medical-location-type').value);
    formData.append('intervention_nature', document.getElementById('medical-intervention-nature').value);
    formData.append('support_request', document.getElementById('medical-support-request').value);
    formData.append('material_damage_notes', document.getElementById('medical-material-damage').value);
    formData.append('total_interventions', document.getElementById('medical-total-interventions').value);
    
    // إضافة تفاصيل حسب النوع
    const nature = document.getElementById('medical-intervention-nature').value;
    if (nature) {
        const typeSelect = document.getElementById(nature.replace('_', '-') + '-type');
        if (typeSelect) {
            formData.append(nature + '_type', typeSelect.value);
        }
    }
    
    // إضافة تفاصيل الفريق المتخصص
    const specializedTeam = document.getElementById('specialized-team-type');
    if (specializedTeam && specializedTeam.value) {
        formData.append('specialized_team_type', specializedTeam.value);
    }
    
    // جمع بيانات الضحايا
    const injuredData = collectCasualtiesData('medical-injured-list', 'injured');
    const fatalitiesData = collectCasualtiesData('medical-fatalities-list', 'fatality');
    
    formData.append('injured_details', JSON.stringify(injuredData));
    formData.append('fatalities_details', JSON.stringify(fatalitiesData));
    
    // إرسال البيانات
    fetch('/api/save-medical-evacuation-details/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم حفظ تفاصيل الإجلاء الصحي بنجاح');
            hideMedicalForm();
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });
}

function hideMedicalForm() {
    document.getElementById('medical-evacuation-form').style.display = 'none';
}

// ==================== نموذج حوادث المرور ====================

document.addEventListener('DOMContentLoaded', function() {
    const trafficAccidentSelect = document.getElementById('traffic-accident-type');
    if (trafficAccidentSelect) {
        trafficAccidentSelect.addEventListener('change', function() {
            showTrafficAccidentDetails(this.value);
        });
    }
});

function showTrafficAccidentDetails(accidentType) {
    // إخفاء جميع التفاصيل
    const details = document.querySelectorAll('.accident-detail');
    details.forEach(detail => detail.style.display = 'none');
    
    // عرض التفاصيل المناسبة
    switch(accidentType) {
        case 'vehicle_collision':
            document.getElementById('collision-details').style.display = 'block';
            break;
        case 'vehicle_crash':
            document.getElementById('crash-details').style.display = 'block';
            break;
        case 'vehicle_rollover':
            document.getElementById('rollover-details').style.display = 'block';
            break;
        case 'train_collision':
            document.getElementById('train-details').style.display = 'block';
            break;
        case 'other_accidents':
            document.getElementById('other-accidents-details').style.display = 'block';
            break;
    }
}

function addTrafficVehicle() {
    const list = document.getElementById('traffic-vehicles-list');
    const index = list.children.length;
    
    const vehicleHtml = `
        <div class="vehicle-item" data-index="${index}">
            <div class="vehicle-info">
                <select name="vehicle_type_${index}" class="form-control" style="margin-bottom: 5px;">
                    <option value="">نوع المركبة</option>
                    <option value="car">سيارة</option>
                    <option value="truck">شاحنة</option>
                    <option value="bus">حافلة</option>
                    <option value="motorcycle">دراجة نارية</option>
                    <option value="other">أخرى</option>
                </select>
                <input type="text" name="vehicle_details_${index}" placeholder="تفاصيل إضافية" class="form-control">
            </div>
            <div class="vehicle-actions">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeVehicle(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    list.insertAdjacentHTML('beforeend', vehicleHtml);
}

function removeVehicle(button) {
    button.closest('.vehicle-item').remove();
}

function addTrafficCasualty(type) {
    const listId = type === 'victim' ? 'traffic-victims-list' : 'traffic-fatalities-list';
    const list = document.getElementById(listId);
    const index = list.children.length;
    
    const casualtyHtml = `
        <div class="casualty-item" data-index="${index}">
            <div class="casualty-info">
                <input type="text" name="${type}_name_${index}" placeholder="الاسم الكامل" class="form-control" style="margin-bottom: 5px;">
                <div style="display: flex; gap: 10px; margin-bottom: 5px;">
                    <input type="number" name="${type}_age_${index}" placeholder="السن" class="form-control" style="width: 80px;">
                    <select name="${type}_gender_${index}" class="form-control" style="width: 100px;">
                        <option value="">الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
                <div class="casualty-status-field">
                    <select name="${type}_status_${index}" class="form-control">
                        <option value="">الحالة</option>
                        <option value="driver">سائق</option>
                        <option value="passenger">راكب</option>
                        <option value="pedestrian">مشاة</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
            </div>
            <div class="casualty-actions">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCasualty(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    list.insertAdjacentHTML('beforeend', casualtyHtml);
}

function saveTrafficAccidentDetails() {
    // جمع بيانات النموذج
    const formData = new FormData();
    
    // إضافة البيانات الأساسية
    formData.append('accident_type', document.getElementById('traffic-accident-type').value);
    formData.append('road_type', document.getElementById('traffic-road-type').value);
    formData.append('material_damage_notes', document.getElementById('traffic-material-damage').value);
    
    // جمع بيانات المركبات
    const vehiclesData = collectVehiclesData('traffic-vehicles-list');
    formData.append('involved_vehicles', JSON.stringify(vehiclesData));
    
    // جمع بيانات الضحايا
    const victimsData = collectCasualtiesData('traffic-victims-list', 'victim');
    const fatalitiesData = collectCasualtiesData('traffic-fatalities-list', 'fatality');
    
    formData.append('victims_details', JSON.stringify(victimsData));
    formData.append('fatalities_details', JSON.stringify(fatalitiesData));
    
    // إرسال البيانات
    fetch('/api/save-traffic-accident-details/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم حفظ تفاصيل حادث المرور بنجاح');
            hideTrafficForm();
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });
}

function hideTrafficForm() {
    document.getElementById('traffic-accident-form').style.display = 'none';
}

// ==================== دوال مساعدة ====================

function collectCasualtiesData(listId, type) {
    const list = document.getElementById(listId);
    const casualties = [];
    
    list.querySelectorAll('.casualty-item').forEach((item, index) => {
        const name = item.querySelector(`input[name="${type}_name_${index}"]`);
        const age = item.querySelector(`input[name="${type}_age_${index}"]`);
        const gender = item.querySelector(`select[name="${type}_gender_${index}"]`);
        const status = item.querySelector(`select[name="${type}_status_${index}"]`);
        
        if (name && name.value.trim()) {
            const casualty = {
                name: name.value.trim(),
                age: age ? parseInt(age.value) || 0 : 0,
                gender: gender ? gender.value : '',
                status: status ? status.value : ''
            };
            casualties.push(casualty);
        }
    });
    
    return casualties;
}

function collectVehiclesData(listId) {
    const list = document.getElementById(listId);
    const vehicles = [];
    
    list.querySelectorAll('.vehicle-item').forEach((item, index) => {
        const type = item.querySelector(`select[name="vehicle_type_${index}"]`);
        const details = item.querySelector(`input[name="vehicle_details_${index}"]`);
        
        if (type && type.value) {
            const vehicle = {
                type: type.value,
                details: details ? details.value.trim() : ''
            };
            vehicles.push(vehicle);
        }
    });
    
    return vehicles;
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

function showSuccessMessage(message) {
    // يمكن تخصيص هذه الدالة لعرض رسائل النجاح
    alert(message);
}

function showErrorMessage(message) {
    // يمكن تخصيص هذه الدالة لعرض رسائل الخطأ
    alert(message);
}

// ==================== نموذج حرائق البنايات ====================

document.addEventListener('DOMContentLoaded', function() {
    // إدارة تغيير طبيعة الحريق
    const buildingFireNatureSelect = document.getElementById('building-fire-nature');
    if (buildingFireNatureSelect) {
        buildingFireNatureSelect.addEventListener('change', function() {
            showBuildingFireNatureDetails(this.value);
        });
    }

    // إدارة تغيير موقع الحريق
    const buildingFireLocationSelect = document.getElementById('building-fire-location');
    if (buildingFireLocationSelect) {
        buildingFireLocationSelect.addEventListener('change', function() {
            const specificDetails = document.getElementById('specific-location-details');
            if (this.value === 'specific_floor' || this.value === 'specific_room') {
                specificDetails.style.display = 'block';
            } else {
                specificDetails.style.display = 'none';
            }
        });
    }
});

function showBuildingFireNatureDetails(nature) {
    // إخفاء جميع التفاصيل
    const details = document.querySelectorAll('.fire-nature-detail');
    details.forEach(detail => detail.style.display = 'none');

    // عرض التفاصيل المناسبة
    switch(nature) {
        case 'residential_building':
            document.getElementById('residential-details').style.display = 'block';
            break;
        case 'classified_institution':
            document.getElementById('institution-details').style.display = 'block';
            break;
        case 'public_place':
            document.getElementById('public-place-details').style.display = 'block';
            break;
        case 'vehicle_fire':
            document.getElementById('vehicle-fire-details').style.display = 'block';
            break;
    }
}

function addBuildingFireCasualty(type) {
    const listId = type === 'injured' ? 'building-fire-injured-list' : 'building-fire-fatalities-list';
    const list = document.getElementById(listId);
    const index = list.children.length;

    const casualtyHtml = `
        <div class="casualty-item" data-index="${index}">
            <div class="casualty-info">
                <input type="text" name="${type}_name_${index}" placeholder="الاسم الكامل" class="form-control" style="margin-bottom: 5px;">
                <div style="display: flex; gap: 10px; margin-bottom: 5px;">
                    <input type="number" name="${type}_age_${index}" placeholder="السن" class="form-control" style="width: 80px;">
                    <select name="${type}_gender_${index}" class="form-control" style="width: 100px;">
                        <option value="">الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
                <div class="casualty-status-field">
                    <select name="${type}_status_${index}" class="form-control">
                        <option value="">التصنيف</option>
                        <option value="resident">ساكن</option>
                        <option value="employee">موظف</option>
                        <option value="visitor">زائر</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
            </div>
            <div class="casualty-actions">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCasualty(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    list.insertAdjacentHTML('beforeend', casualtyHtml);
}

function saveBuildingFireDetails() {
    const formData = new FormData();

    // البيانات الأساسية
    formData.append('fire_nature', document.getElementById('building-fire-nature').value);
    formData.append('fire_location', document.getElementById('building-fire-location').value);
    formData.append('specific_floor', document.getElementById('specific-floor').value);
    formData.append('specific_room', document.getElementById('specific-room').value);

    // تفاصيل حسب النوع
    const nature = document.getElementById('building-fire-nature').value;
    if (nature) {
        const typeSelects = {
            'residential_building': 'residential-type',
            'classified_institution': 'institution-type',
            'public_place': 'public-place-type',
            'vehicle_fire': 'vehicle-fire-type'
        };

        const selectId = typeSelects[nature];
        if (selectId) {
            const select = document.getElementById(selectId);
            if (select) {
                formData.append(selectId.replace('-', '_'), select.value);
            }
        }
    }

    // انتشار الحريق
    formData.append('fire_points_count', document.getElementById('fire-points-count').value);
    formData.append('wind_direction', document.getElementById('wind-direction').value);
    formData.append('wind_speed', document.getElementById('wind-speed').value);
    formData.append('population_threat', document.getElementById('population-threat').checked ? '1' : '0');
    formData.append('population_evacuated', document.getElementById('population-evacuated').checked ? '1' : '0');
    formData.append('assistance_provided', document.getElementById('assistance-provided').value);

    // طلب الدعم
    formData.append('support_request', document.getElementById('building-fire-support').value);
    formData.append('field_situation_notes', document.getElementById('field-situation-notes').value);

    // الإحصائيات النهائية
    formData.append('intervening_agents_count', document.getElementById('intervening-agents-count').value);
    formData.append('affected_families_count', document.getElementById('affected-families-count').value);
    formData.append('damages_description', document.getElementById('damages-description').value);
    formData.append('saved_properties', document.getElementById('saved-properties').value);
    formData.append('final_notes', document.getElementById('building-fire-final-notes').value);

    // جمع بيانات الضحايا
    const injuredData = collectCasualtiesData('building-fire-injured-list', 'injured');
    const fatalitiesData = collectCasualtiesData('building-fire-fatalities-list', 'fatality');

    formData.append('injured_details', JSON.stringify(injuredData));
    formData.append('fatalities_details', JSON.stringify(fatalitiesData));

    // إرسال البيانات
    fetch('/api/save-building-fire-details/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم حفظ تفاصيل حريق البناية بنجاح');
            hideBuildingFireForm();
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });
}

function hideBuildingFireForm() {
    document.getElementById('building-fire-form').style.display = 'none';
}

// ==================== نموذج حريق المحاصيل الزراعية ====================

document.addEventListener('DOMContentLoaded', function() {
    // إدارة تهديد السكان
    const populationThreatCheckbox = document.getElementById('agricultural-population-threat');
    if (populationThreatCheckbox) {
        populationThreatCheckbox.addEventListener('change', function() {
            const evacuationGroup = document.getElementById('evacuation-location-group');
            if (this.checked) {
                evacuationGroup.style.display = 'block';
            } else {
                evacuationGroup.style.display = 'none';
            }
        });
    }

    // إدارة طلب الدعم
    const agriculturalSupportSelect = document.getElementById('agricultural-support-request');
    if (agriculturalSupportSelect) {
        agriculturalSupportSelect.addEventListener('change', function() {
            const specializedSection = document.getElementById('agricultural-specialized-team-section');
            if (this.value === 'specialized_teams') {
                specializedSection.style.display = 'block';
            } else {
                specializedSection.style.display = 'none';
            }
        });
    }
});

function addAgriculturalCasualty(type) {
    const listId = type === 'victim' ? 'agricultural-victims-list' : 'agricultural-fatalities-list';
    const list = document.getElementById(listId);
    const index = list.children.length;

    const casualtyHtml = `
        <div class="casualty-item" data-index="${index}">
            <div class="casualty-info">
                <input type="text" name="${type}_name_${index}" placeholder="الاسم الكامل" class="form-control" style="margin-bottom: 5px;">
                <div style="display: flex; gap: 10px; margin-bottom: 5px;">
                    <input type="number" name="${type}_age_${index}" placeholder="السن" class="form-control" style="width: 80px;">
                    <select name="${type}_gender_${index}" class="form-control" style="width: 100px;">
                        <option value="">الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
                <div class="casualty-status-field">
                    <select name="${type}_status_${index}" class="form-control">
                        <option value="">الحالة</option>
                        <option value="driver">سائق</option>
                        <option value="passenger">راكب</option>
                        <option value="pedestrian">مشاة</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
            </div>
            <div class="casualty-actions">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCasualty(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    list.insertAdjacentHTML('beforeend', casualtyHtml);
}

function saveAgriculturalFireDetails() {
    const formData = new FormData();

    // البيانات الأساسية
    formData.append('fire_type', document.getElementById('agricultural-fire-type').value);
    formData.append('fire_sources_count', document.getElementById('fire-sources-count').value);
    formData.append('wind_direction', document.getElementById('agricultural-wind-direction').value);
    formData.append('wind_speed', document.getElementById('agricultural-wind-speed').value);
    formData.append('population_threat', document.getElementById('agricultural-population-threat').checked ? '1' : '0');
    formData.append('evacuation_location', document.getElementById('evacuation-location').value);

    // الأعوان والضحايا
    formData.append('intervening_agents_count', document.getElementById('agricultural-intervening-agents').value);
    formData.append('affected_families_count', document.getElementById('agricultural-affected-families').value);

    // الجهات الحاضرة
    const presentAuthorities = [];
    document.querySelectorAll('input[name="present_authorities"]:checked').forEach(checkbox => {
        presentAuthorities.push(checkbox.value);
    });
    formData.append('present_authorities', JSON.stringify(presentAuthorities));

    // طلب الدعم
    formData.append('support_request', document.getElementById('agricultural-support-request').value);
    const specializedTeam = document.getElementById('agricultural-specialized-team-type');
    if (specializedTeam && specializedTeam.value) {
        formData.append('specialized_team_type', specializedTeam.value);
    }

    // الخسائر حسب المساحة
    formData.append('standing_wheat_area', document.getElementById('standing-wheat-area').value);
    formData.append('harvest_area', document.getElementById('harvest-area').value);
    formData.append('forest_area', document.getElementById('forest-area').value);
    formData.append('barley_area', document.getElementById('barley-area').value);

    // الخسائر حسب العدد
    formData.append('straw_bales_count', document.getElementById('straw-bales-count').value);
    formData.append('grain_bags_count', document.getElementById('grain-bags-count').value);
    formData.append('fruit_trees_count', document.getElementById('fruit-trees-count').value);
    formData.append('beehives_count', document.getElementById('beehives-count').value);

    // الأملاك المنقذة
    formData.append('saved_area', document.getElementById('saved-area').value);
    formData.append('saved_straw_bales', document.getElementById('saved-straw-bales').value);
    formData.append('saved_equipment', document.getElementById('saved-equipment').value);

    // ملاحظات ختامية
    formData.append('final_notes', document.getElementById('agricultural-final-notes').value);

    // جمع بيانات الضحايا
    const victimsData = collectCasualtiesData('agricultural-victims-list', 'victim');
    const fatalitiesData = collectCasualtiesData('agricultural-fatalities-list', 'fatality');

    formData.append('victims_details', JSON.stringify(victimsData));
    formData.append('fatalities_details', JSON.stringify(fatalitiesData));

    // إرسال البيانات
    fetch('/api/save-agricultural-fire-details/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم حفظ تفاصيل حريق المحاصيل بنجاح');
            hideAgriculturalFireForm();
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });
}

function hideAgriculturalFireForm() {
    document.getElementById('agricultural-fire-form').style.display = 'none';
}

// ==================== دوال ملء النماذج بالبيانات ====================

function fillMedicalForm(data) {
    // ملء نموذج الإجلاء الصحي بالبيانات الموجودة
    if (data.location_type) {
        document.getElementById('medical-location-type').value = data.location_type;
    }
    if (data.intervention_nature) {
        document.getElementById('medical-intervention-nature').value = data.intervention_nature;
        showMedicalNatureDetails(data.intervention_nature);
    }
    if (data.support_request) {
        document.getElementById('medical-support-request').value = data.support_request;
    }
    if (data.material_damage_notes) {
        document.getElementById('medical-material-damage').value = data.material_damage_notes;
    }
    if (data.total_interventions) {
        document.getElementById('medical-total-interventions').value = data.total_interventions;
    }

    // ملء تفاصيل حسب النوع
    if (data.suffocation_type) {
        document.getElementById('suffocation-type').value = data.suffocation_type;
    }
    if (data.poisoning_type) {
        document.getElementById('poisoning-type').value = data.poisoning_type;
    }
    if (data.burn_type) {
        document.getElementById('burn-type').value = data.burn_type;
    }
    if (data.explosion_type) {
        document.getElementById('explosion-type').value = data.explosion_type;
    }
    if (data.drowning_type) {
        document.getElementById('drowning-type').value = data.drowning_type;
    }
    if (data.specialized_team_type) {
        document.getElementById('specialized-team-type').value = data.specialized_team_type;
    }

    // ملء قوائم الضحايا والوفيات
    if (data.injured_details && data.injured_details.length > 0) {
        fillCasualtiesList('medical-injured-list', data.injured_details, 'injured');
    }
    if (data.fatalities_details && data.fatalities_details.length > 0) {
        fillCasualtiesList('medical-fatalities-list', data.fatalities_details, 'fatality');
    }
}

function fillTrafficForm(data) {
    // ملء نموذج حوادث المرور بالبيانات الموجودة
    if (data.accident_type) {
        document.getElementById('traffic-accident-type').value = data.accident_type;
        showTrafficAccidentDetails(data.accident_type);
    }
    if (data.road_type) {
        document.getElementById('traffic-road-type').value = data.road_type;
    }
    if (data.material_damage_notes) {
        document.getElementById('traffic-material-damage').value = data.material_damage_notes;
    }

    // ملء قوائم المركبات
    if (data.involved_vehicles && data.involved_vehicles.length > 0) {
        fillVehiclesList('traffic-vehicles-list', data.involved_vehicles);
    }

    // ملء قوائم الضحايا والوفيات
    if (data.victims_details && data.victims_details.length > 0) {
        fillCasualtiesList('traffic-victims-list', data.victims_details, 'victim');
    }
    if (data.fatalities_details && data.fatalities_details.length > 0) {
        fillCasualtiesList('traffic-fatalities-list', data.fatalities_details, 'fatality');
    }
}

function fillBuildingFireForm(data) {
    // ملء نموذج حرائق البنايات بالبيانات الموجودة
    if (data.fire_nature) {
        document.getElementById('building-fire-nature').value = data.fire_nature;
        showBuildingFireNatureDetails(data.fire_nature);
    }
    if (data.fire_location) {
        document.getElementById('building-fire-location').value = data.fire_location;
    }
    if (data.specific_floor) {
        document.getElementById('specific-floor').value = data.specific_floor;
    }
    if (data.specific_room) {
        document.getElementById('specific-room').value = data.specific_room;
    }
    if (data.fire_points_count) {
        document.getElementById('fire-points-count').value = data.fire_points_count;
    }
    if (data.wind_direction) {
        document.getElementById('wind-direction').value = data.wind_direction;
    }
    if (data.wind_speed) {
        document.getElementById('wind-speed').value = data.wind_speed;
    }
    if (data.population_threat) {
        document.getElementById('population-threat').checked = data.population_threat;
    }
    if (data.population_evacuated) {
        document.getElementById('population-evacuated').checked = data.population_evacuated;
    }
    if (data.assistance_provided) {
        document.getElementById('assistance-provided').value = data.assistance_provided;
    }
    if (data.support_request) {
        document.getElementById('building-fire-support').value = data.support_request;
    }
    if (data.field_situation_notes) {
        document.getElementById('field-situation-notes').value = data.field_situation_notes;
    }
    if (data.intervening_agents_count) {
        document.getElementById('intervening-agents-count').value = data.intervening_agents_count;
    }
    if (data.affected_families_count) {
        document.getElementById('affected-families-count').value = data.affected_families_count;
    }
    if (data.damages_description) {
        document.getElementById('damages-description').value = data.damages_description;
    }
    if (data.saved_properties) {
        document.getElementById('saved-properties').value = data.saved_properties;
    }
    if (data.final_notes) {
        document.getElementById('building-fire-final-notes').value = data.final_notes;
    }

    // ملء تفاصيل حسب النوع
    if (data.residential_type) {
        document.getElementById('residential-type').value = data.residential_type;
    }
    if (data.institution_type) {
        document.getElementById('institution-type').value = data.institution_type;
    }
    if (data.public_place_type) {
        document.getElementById('public-place-type').value = data.public_place_type;
    }
    if (data.vehicle_type) {
        document.getElementById('vehicle-fire-type').value = data.vehicle_type;
    }

    // ملء قوائم الضحايا والوفيات
    if (data.injured_details && data.injured_details.length > 0) {
        fillCasualtiesList('building-fire-injured-list', data.injured_details, 'injured');
    }
    if (data.fatalities_details && data.fatalities_details.length > 0) {
        fillCasualtiesList('building-fire-fatalities-list', data.fatalities_details, 'fatality');
    }
}

function fillAgriculturalFireForm(data) {
    // ملء نموذج حريق المحاصيل بالبيانات الموجودة
    if (data.fire_type) {
        document.getElementById('agricultural-fire-type').value = data.fire_type;
    }
    if (data.fire_sources_count) {
        document.getElementById('fire-sources-count').value = data.fire_sources_count;
    }
    if (data.wind_direction) {
        document.getElementById('agricultural-wind-direction').value = data.wind_direction;
    }
    if (data.wind_speed) {
        document.getElementById('agricultural-wind-speed').value = data.wind_speed;
    }
    if (data.population_threat) {
        document.getElementById('agricultural-population-threat').checked = data.population_threat;
    }
    if (data.evacuation_location) {
        document.getElementById('evacuation-location').value = data.evacuation_location;
    }
    if (data.intervening_agents_count) {
        document.getElementById('agricultural-intervening-agents').value = data.intervening_agents_count;
    }
    if (data.affected_families_count) {
        document.getElementById('agricultural-affected-families').value = data.affected_families_count;
    }
    if (data.support_request) {
        document.getElementById('agricultural-support-request').value = data.support_request;
    }
    if (data.specialized_team_type) {
        document.getElementById('agricultural-specialized-team-type').value = data.specialized_team_type;
    }

    // ملء الخسائر حسب المساحة
    if (data.standing_wheat_area) {
        document.getElementById('standing-wheat-area').value = data.standing_wheat_area;
    }
    if (data.harvest_area) {
        document.getElementById('harvest-area').value = data.harvest_area;
    }
    if (data.forest_area) {
        document.getElementById('forest-area').value = data.forest_area;
    }
    if (data.barley_area) {
        document.getElementById('barley-area').value = data.barley_area;
    }

    // ملء الخسائر حسب العدد
    if (data.straw_bales_count) {
        document.getElementById('straw-bales-count').value = data.straw_bales_count;
    }
    if (data.grain_bags_count) {
        document.getElementById('grain-bags-count').value = data.grain_bags_count;
    }
    if (data.fruit_trees_count) {
        document.getElementById('fruit-trees-count').value = data.fruit_trees_count;
    }
    if (data.beehives_count) {
        document.getElementById('beehives-count').value = data.beehives_count;
    }

    // ملء الأملاك المنقذة
    if (data.saved_area) {
        document.getElementById('saved-area').value = data.saved_area;
    }
    if (data.saved_straw_bales) {
        document.getElementById('saved-straw-bales').value = data.saved_straw_bales;
    }
    if (data.saved_equipment) {
        document.getElementById('saved-equipment').value = data.saved_equipment;
    }
    if (data.final_notes) {
        document.getElementById('agricultural-final-notes').value = data.final_notes;
    }

    // ملء الجهات الحاضرة
    if (data.present_authorities && data.present_authorities.length > 0) {
        data.present_authorities.forEach(authority => {
            const checkbox = document.querySelector(`input[name="present_authorities"][value="${authority}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // ملء قوائم الضحايا والوفيات
    if (data.victims_details && data.victims_details.length > 0) {
        fillCasualtiesList('agricultural-victims-list', data.victims_details, 'victim');
    }
    if (data.fatalities_details && data.fatalities_details.length > 0) {
        fillCasualtiesList('agricultural-fatalities-list', data.fatalities_details, 'fatality');
    }
}

// ==================== دوال مساعدة لملء القوائم ====================

function fillCasualtiesList(listId, casualties, type) {
    const list = document.getElementById(listId);
    if (!list) return;

    // مسح القائمة الحالية
    list.innerHTML = '';

    // إضافة كل ضحية
    casualties.forEach((casualty, index) => {
        const casualtyHtml = `
            <div class="casualty-item" data-index="${index}">
                <div class="casualty-info">
                    <input type="text" name="${type}_name_${index}" placeholder="الاسم الكامل" class="form-control" style="margin-bottom: 5px;" value="${casualty.name || ''}">
                    <div style="display: flex; gap: 10px; margin-bottom: 5px;">
                        <input type="number" name="${type}_age_${index}" placeholder="السن" class="form-control" style="width: 80px;" value="${casualty.age || ''}">
                        <select name="${type}_gender_${index}" class="form-control" style="width: 100px;">
                            <option value="">الجنس</option>
                            <option value="male" ${casualty.gender === 'male' ? 'selected' : ''}>ذكر</option>
                            <option value="female" ${casualty.gender === 'female' ? 'selected' : ''}>أنثى</option>
                        </select>
                    </div>
                    ${casualty.status ? `
                    <div class="casualty-status-field">
                        <select name="${type}_status_${index}" class="form-control">
                            <option value="">الحالة</option>
                            <option value="driver" ${casualty.status === 'driver' ? 'selected' : ''}>سائق</option>
                            <option value="passenger" ${casualty.status === 'passenger' ? 'selected' : ''}>راكب</option>
                            <option value="pedestrian" ${casualty.status === 'pedestrian' ? 'selected' : ''}>مشاة</option>
                            <option value="resident" ${casualty.status === 'resident' ? 'selected' : ''}>ساكن</option>
                            <option value="employee" ${casualty.status === 'employee' ? 'selected' : ''}>موظف</option>
                            <option value="visitor" ${casualty.status === 'visitor' ? 'selected' : ''}>زائر</option>
                            <option value="other" ${casualty.status === 'other' ? 'selected' : ''}>أخرى</option>
                        </select>
                    </div>
                    ` : ''}
                </div>
                <div class="casualty-actions">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeCasualty(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        list.insertAdjacentHTML('beforeend', casualtyHtml);
    });
}

function fillVehiclesList(listId, vehicles) {
    const list = document.getElementById(listId);
    if (!list) return;

    // مسح القائمة الحالية
    list.innerHTML = '';

    // إضافة كل مركبة
    vehicles.forEach((vehicle, index) => {
        const vehicleHtml = `
            <div class="vehicle-item" data-index="${index}">
                <div class="vehicle-info">
                    <select name="vehicle_type_${index}" class="form-control" style="margin-bottom: 5px;">
                        <option value="">نوع المركبة</option>
                        <option value="car" ${vehicle.type === 'car' ? 'selected' : ''}>سيارة</option>
                        <option value="truck" ${vehicle.type === 'truck' ? 'selected' : ''}>شاحنة</option>
                        <option value="bus" ${vehicle.type === 'bus' ? 'selected' : ''}>حافلة</option>
                        <option value="motorcycle" ${vehicle.type === 'motorcycle' ? 'selected' : ''}>دراجة نارية</option>
                        <option value="other" ${vehicle.type === 'other' ? 'selected' : ''}>أخرى</option>
                    </select>
                    <input type="text" name="vehicle_details_${index}" placeholder="تفاصيل إضافية" class="form-control" value="${vehicle.details || ''}">
                </div>
                <div class="vehicle-actions">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeVehicle(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        list.insertAdjacentHTML('beforeend', vehicleHtml);
    });
}

// تحديث دوال الحفظ لتتضمن معرف التدخل
function saveMedicalEvacuationDetails() {
    const form = document.getElementById('medical-evacuation-form');
    const interventionId = form.getAttribute('data-intervention-id');

    if (!interventionId) {
        showErrorMessage('معرف التدخل غير موجود');
        return;
    }

    // جمع بيانات النموذج
    const formData = new FormData();
    formData.append('intervention_id', interventionId);

    // إضافة باقي البيانات كما هو موجود في الدالة الأصلية
    formData.append('location_type', document.getElementById('medical-location-type').value);
    formData.append('intervention_nature', document.getElementById('medical-intervention-nature').value);
    formData.append('support_request', document.getElementById('medical-support-request').value);
    formData.append('material_damage_notes', document.getElementById('medical-material-damage').value);
    formData.append('total_interventions', document.getElementById('medical-total-interventions').value);

    // إضافة تفاصيل حسب النوع
    const nature = document.getElementById('medical-intervention-nature').value;
    if (nature) {
        const typeSelect = document.getElementById(nature.replace('_', '-') + '-type');
        if (typeSelect) {
            formData.append(nature + '_type', typeSelect.value);
        }
    }

    // إضافة تفاصيل الفريق المتخصص
    const specializedTeam = document.getElementById('specialized-team-type');
    if (specializedTeam && specializedTeam.value) {
        formData.append('specialized_team_type', specializedTeam.value);
    }

    // جمع بيانات الضحايا
    const injuredData = collectCasualtiesData('medical-injured-list', 'injured');
    const fatalitiesData = collectCasualtiesData('medical-fatalities-list', 'fatality');

    formData.append('injured_details', JSON.stringify(injuredData));
    formData.append('fatalities_details', JSON.stringify(fatalitiesData));

    // إرسال البيانات
    fetch('/api/save-medical-evacuation-details/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('تم حفظ تفاصيل الإجلاء الصحي بنجاح');
            hideMedicalForm();
            // إعادة تحميل البيانات
            loadInterventions();
        } else {
            showErrorMessage('حدث خطأ في حفظ البيانات: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });
}
