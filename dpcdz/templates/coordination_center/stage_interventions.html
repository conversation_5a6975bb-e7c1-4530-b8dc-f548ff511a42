{% extends 'base.html' %}
{% load static %}

{% block title %}🚀 {{ stage_title }} - نظام متطور{% endblock %}

{% block extra_css %}
<!-- استيراد خط Cairo العربي -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* 🎨 تصميم عصري متطور لصفحة المراحل */
:root {
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #ec4899;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --dark: #1f2937;
    --light: #f9fafb;
    --glass: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
    --radius: 1rem;
    --radius-lg: 1.5rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', 'Amiri', 'Noto Sans Arabic', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeOut 2s ease-in-out 1.5s forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        pointer-events: none;
    }
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.loading-text h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.loading-text p {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 2rem;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.spinner-ring {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    animation: bounce 1.4s ease-in-out infinite both;
}

.spinner-ring:nth-child(1) { animation-delay: -0.32s; }
.spinner-ring:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Modern Container */
.modern-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
}

/* Hero Section */
.hero-section {
    position: relative;
    padding: 4rem 0;
    text-align: center;
    color: white;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideInDown 1s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    animation: slideInUp 1s ease-out 0.2s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.title-line {
    display: block;
}

.gradient-text {
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto 3rem;
    animation: slideInUp 1s ease-out 0.4s both;
}

.hero-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    animation: slideInUp 1s ease-out 0.6s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-divider {
    width: 1px;
    height: 3rem;
    background: rgba(255, 255, 255, 0.3);
}

/* Controls Section */
.controls-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin-bottom: 3rem;
    animation: slideInUp 1s ease-out 0.8s both;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    color: white;
}

.controls-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.controls-actions {
    display: flex;
    gap: 1rem;
}

.controls-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.controls-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
}

.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.control-item label {
    display: block;
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-item .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    color: white;
    font-weight: 500;
    transition: var(--transition);
}

.control-item .form-control:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    color: white;
}

.control-item .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Data Section */
.data-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    animation: slideInUp 1s ease-out 1s both;
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.data-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.data-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.data-count {
    background: var(--primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
}

.table-container {
    overflow-x: auto;
    border-radius: var(--radius);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--radius);
    overflow: hidden;
}

.modern-table thead {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
}

.modern-table th {
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: #f9fafb;
    transform: scale(1.01);
}

.number-badge {
    background: var(--primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.datetime-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.datetime-info .date {
    font-weight: 600;
    color: var(--dark);
}

.datetime-info .time {
    font-size: 0.8rem;
    color: #6b7280;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--dark);
}

.location-info i {
    color: var(--danger);
}

.type-badge {
    background: var(--warning);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.8rem;
}

.unit-name {
    color: var(--dark);
    font-weight: 600;
}

.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.priority-high {
    background: var(--danger);
    color: white;
}

.priority-medium {
    background: var(--warning);
    color: white;
}

.priority-low {
    background: var(--success);
    color: white;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--primary);
    color: white;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.view-btn {
    background: var(--primary);
    color: white;
}

.edit-btn {
    background: var(--warning);
    color: white;
}

.delete-btn {
    background: var(--danger);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.no-data {
    text-align: center;
    padding: 3rem;
}

.no-data-content {
    color: #6b7280;
}

.no-data-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-data-content p {
    opacity: 0.7;
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab-main {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ec4899, #be185d);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: var(--transition);
    animation: pulse 2s ease-in-out infinite;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.fab-main.active {
    transform: rotate(45deg);
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fab-menu {
    position: absolute;
    bottom: 80px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition);
}

.fab-menu.active {
    opacity: 1;
    pointer-events: all;
}

.fab-item {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark);
    text-decoration: none;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    position: relative;
    transform: scale(0);
    animation: scaleIn 0.3s ease-out forwards;
}

.fab-item:nth-child(1) { animation-delay: 0.1s; }
.fab-item:nth-child(2) { animation-delay: 0.2s; }
.fab-item:nth-child(3) { animation-delay: 0.3s; }
.fab-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes scaleIn {
    to {
        transform: scale(1);
    }
}

.fab-item:hover {
    transform: scale(1.1);
    background: white;
    color: var(--primary);
}

.fab-item::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dark);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition);
}

.fab-item:hover::before {
    opacity: 1;
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    pointer-events: all;
}

.back-to-top:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-container {
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .controls-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .modern-container {
        padding: 1rem;
    }

    .hero-section {
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-divider {
        width: 3rem;
        height: 1px;
    }

    .controls-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .data-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .modern-table {
        font-size: 0.9rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 0.75rem 0.5rem;
    }

    .fab-container {
        bottom: 1rem;
        right: 1rem;
    }

    .back-to-top {
        bottom: 1rem;
        left: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .controls-section,
    .data-section {
        padding: 1rem;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Loading Screen -->
<div id="loading-screen" class="loading-screen">
    <div class="loading-content">
        <div class="loading-logo">
            <i class="{{ stage_icon }}"></i>
        </div>
        <div class="loading-text">
            <h2>{{ stage_title }}</h2>
            <p>جاري تحميل البيانات...</p>
        </div>
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="modern-container">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>

        <div class="hero-content">
            <div class="hero-badge">
                <i class="{{ stage_icon }}"></i>
                <span>{{ stage_title }}</span>
            </div>

            <h1 class="hero-title">
                <span class="title-line">إدارة</span>
                <span class="title-line gradient-text">{{ stage_title }}</span>
            </h1>

            <p class="hero-description">
                عرض وإدارة التدخلات في مرحلة {{ stage_title }}
                <br>
                نظام متطور لإدارة ومتابعة التدخلات بكفاءة عالية
            </p>

            <div class="hero-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ interventions.count|default:0 }}</div>
                    <div class="stat-label">إجمالي التدخلات</div>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                    <div class="stat-number">{{ today|date:"d" }}</div>
                    <div class="stat-label">{{ today|date:"F Y" }}</div>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">خدمة مستمرة</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Controls Section -->
    <section class="controls-section">
        <div class="controls-header">
            <h3>
                <i class="fas fa-filter"></i>
                أدوات التحكم والفلترة
            </h3>
            <div class="controls-actions">
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث البيانات
                </button>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>

        <div class="controls-grid">
            <div class="control-item">
                <label for="date-filter">
                    <i class="fas fa-calendar"></i>
                    تاريخ التدخل
                </label>
                <input type="date" id="date-filter" class="form-control" value="{{ today|date:'Y-m-d' }}">
            </div>

            <div class="control-item">
                <label for="unit-filter">
                    <i class="fas fa-building"></i>
                    الوحدة
                </label>
                <select id="unit-filter" class="form-control">
                    <option value="">جميع الوحدات</option>
                    {% for unit in units %}
                    <option value="{{ unit.id }}">{{ unit.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="control-item">
                <label for="priority-filter">
                    <i class="fas fa-exclamation-triangle"></i>
                    الأولوية
                </label>
                <select id="priority-filter" class="form-control">
                    <option value="">جميع الأولويات</option>
                    <option value="high">عالية</option>
                    <option value="medium">متوسطة</option>
                    <option value="low">منخفضة</option>
                </select>
            </div>

            <div class="control-item">
                <label for="search-filter">
                    <i class="fas fa-search"></i>
                    البحث السريع
                </label>
                <input type="text" id="search-filter" class="form-control" placeholder="ابحث في التدخلات...">
            </div>
        </div>
    </section>

    <!-- Data Table Section -->
    <section class="data-section">
        <div class="data-header">
            <h3>
                <i class="fas fa-table"></i>
                قائمة التدخلات
            </h3>
            <div class="data-info">
                <span class="data-count">{{ interventions.count }} تدخل</span>
                <span class="data-updated">آخر تحديث: {{ today|date:"Y-m-d" }}</span>
            </div>
        </div>

        <div class="table-container">
            <table class="modern-table" id="interventions-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag"></i> الرقم</th>
                        <th><i class="fas fa-clock"></i> التاريخ والوقت</th>
                        <th><i class="fas fa-map-marker-alt"></i> الموقع</th>
                        <th><i class="fas fa-fire"></i> نوع التدخل</th>
                        <th><i class="fas fa-building"></i> الوحدة</th>
                        <th><i class="fas fa-flag"></i> الأولوية</th>
                        <th><i class="fas fa-info-circle"></i> الحالة</th>
                        <th><i class="fas fa-cogs"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for intervention in interventions %}
                    <tr class="table-row" data-intervention-id="{{ intervention.id }}">
                        <td class="intervention-number">
                            <span class="number-badge">{{ intervention.id }}</span>
                        </td>
                        <td class="intervention-datetime">
                            <div class="datetime-info">
                                <span class="date">{{ intervention.date|date:"Y-m-d" }}</span>
                                <span class="time">{{ intervention.time|time:"H:i" }}</span>
                            </div>
                        </td>
                        <td class="intervention-location">
                            <div class="location-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ intervention.location|default:"غير محدد" }}</span>
                            </div>
                        </td>
                        <td class="intervention-type">
                            <span class="type-badge">{{ intervention.intervention_type|default:"غير محدد" }}</span>
                        </td>
                        <td class="intervention-unit">
                            <span class="unit-name">{{ intervention.unit.name|default:"غير محدد" }}</span>
                        </td>
                        <td class="intervention-priority">
                            <span class="priority-badge priority-{{ intervention.priority|default:'medium' }}">
                                {% if intervention.priority == 'high' %}
                                    <i class="fas fa-exclamation-triangle"></i> عالية
                                {% elif intervention.priority == 'low' %}
                                    <i class="fas fa-info-circle"></i> منخفضة
                                {% else %}
                                    <i class="fas fa-minus-circle"></i> متوسطة
                                {% endif %}
                            </span>
                        </td>
                        <td class="intervention-status">
                            <span class="status-badge status-{{ stage_key }}">
                                <i class="{{ stage_icon }}"></i>
                                {{ stage_title }}
                            </span>
                        </td>
                        <td class="intervention-actions">
                            <div class="action-buttons">
                                <button class="action-btn view-btn" onclick="viewIntervention({{ intervention.id }})" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit-btn" onclick="editIntervention({{ intervention.id }})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete-btn" onclick="deleteIntervention({{ intervention.id }})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="no-data">
                            <div class="no-data-content">
                                <i class="fas fa-inbox"></i>
                                <h4>لا توجد تدخلات</h4>
                                <p>لا توجد تدخلات في هذه المرحلة حالياً</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>
</div>

<!-- Floating Action Button -->
<div class="fab-container">
    <div class="fab-main" id="fab-main">
        <i class="fas fa-plus"></i>
    </div>
    <div class="fab-menu" id="fab-menu">
        <a href="{% url 'coordination_center' %}" class="fab-item" data-tooltip="مركز التنسيق">
            <i class="fas fa-home"></i>
        </a>
        <a href="{% url 'all_interventions' %}" class="fab-item" data-tooltip="جميع التدخلات">
            <i class="fas fa-list-alt"></i>
        </a>
        <a href="{% url 'daily_interventions' %}" class="fab-item" data-tooltip="التدخلات اليومية">
            <i class="fas fa-ambulance"></i>
        </a>
        <a href="{% url 'unified_morning_check' %}" class="fab-item" data-tooltip="التعداد الصباحي">
            <i class="fas fa-clipboard-list"></i>
        </a>
    </div>
</div>

<!-- Back to Top -->
<button id="back-to-top" class="back-to-top">
    <i class="fas fa-arrow-up"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
// 🚀 JavaScript متطور للتفاعل والرسوم المتحركة
document.addEventListener('DOMContentLoaded', function() {
    // إزالة شاشة التحميل
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }, 2000);

    // تفعيل الأزرار العائمة
    initFloatingActionButton();

    // تفعيل زر العودة للأعلى
    initBackToTop();

    // تفعيل الفلاتر
    initFilters();

    // تفعيل تأثيرات الجدول
    initTableAnimations();

    // إظهار رسالة ترحيب
    setTimeout(() => {
        showNotification('🎯 تم تحميل بيانات {{ stage_title }} بنجاح', 'success');
    }, 2500);

    console.log('🚀 تم تحميل صفحة {{ stage_title }} بنجاح');
});

// تفعيل الأزرار العائمة
function initFloatingActionButton() {
    const fabMain = document.getElementById('fab-main');
    const fabMenu = document.getElementById('fab-menu');

    if (fabMain && fabMenu) {
        fabMain.addEventListener('click', function() {
            fabMain.classList.toggle('active');
            fabMenu.classList.toggle('active');
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!fabMain.contains(e.target) && !fabMenu.contains(e.target)) {
                fabMain.classList.remove('active');
                fabMenu.classList.remove('active');
            }
        });
    }
}

// تفعيل زر العودة للأعلى
function initBackToTop() {
    const backToTop = document.getElementById('back-to-top');

    if (backToTop) {
        // إظهار/إخفاء الزر حسب التمرير
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        // التمرير للأعلى عند النقر
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // تأثير بصري
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    }
}

// تفعيل الفلاتر
function initFilters() {
    const dateFilter = document.getElementById('date-filter');
    const unitFilter = document.getElementById('unit-filter');
    const priorityFilter = document.getElementById('priority-filter');
    const searchFilter = document.getElementById('search-filter');

    // فلتر التاريخ
    if (dateFilter) {
        dateFilter.addEventListener('change', function() {
            filterTable();
            showNotification('تم تطبيق فلتر التاريخ', 'info');
        });
    }

    // فلتر الوحدة
    if (unitFilter) {
        unitFilter.addEventListener('change', function() {
            filterTable();
            showNotification('تم تطبيق فلتر الوحدة', 'info');
        });
    }

    // فلتر الأولوية
    if (priorityFilter) {
        priorityFilter.addEventListener('change', function() {
            filterTable();
            showNotification('تم تطبيق فلتر الأولوية', 'info');
        });
    }

    // البحث السريع
    if (searchFilter) {
        searchFilter.addEventListener('input', function() {
            filterTable();
        });
    }
}

// فلترة الجدول
function filterTable() {
    const table = document.getElementById('interventions-table');
    const rows = table.querySelectorAll('tbody tr');
    const dateFilter = document.getElementById('date-filter').value;
    const unitFilter = document.getElementById('unit-filter').value;
    const priorityFilter = document.getElementById('priority-filter').value;
    const searchFilter = document.getElementById('search-filter').value.toLowerCase();

    let visibleCount = 0;

    rows.forEach(row => {
        if (row.querySelector('.no-data')) return;

        let show = true;

        // فلتر التاريخ
        if (dateFilter) {
            const rowDate = row.querySelector('.date').textContent;
            if (rowDate !== dateFilter) show = false;
        }

        // فلتر الوحدة
        if (unitFilter) {
            const rowUnit = row.querySelector('.unit-name').textContent;
            if (!rowUnit.includes(unitFilter)) show = false;
        }

        // فلتر الأولوية
        if (priorityFilter) {
            const priorityBadge = row.querySelector('.priority-badge');
            if (!priorityBadge.classList.contains(`priority-${priorityFilter}`)) show = false;
        }

        // البحث السريع
        if (searchFilter) {
            const rowText = row.textContent.toLowerCase();
            if (!rowText.includes(searchFilter)) show = false;
        }

        // إظهار/إخفاء الصف
        if (show) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // تحديث عداد النتائج
    const dataCount = document.querySelector('.data-count');
    if (dataCount) {
        dataCount.textContent = `${visibleCount} تدخل`;
    }
}

// تفعيل تأثيرات الجدول
function initTableAnimations() {
    const rows = document.querySelectorAll('.table-row');

    rows.forEach((row, index) => {
        // تأثير الدخول المتدرج
        row.style.animationDelay = `${index * 0.1}s`;
        row.style.animation = 'slideInUp 0.6s ease-out forwards';

        // تأثيرات التفاعل
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

// دوال الإجراءات
function viewIntervention(id) {
    showNotification(`عرض تفاصيل التدخل رقم ${id}`, 'info');
    // يمكن إضافة منطق عرض التفاصيل هنا
}

function editIntervention(id) {
    showNotification(`تعديل التدخل رقم ${id}`, 'warning');
    // يمكن إضافة منطق التعديل هنا
}

function deleteIntervention(id) {
    if (confirm('هل أنت متأكد من حذف هذا التدخل؟')) {
        showNotification(`تم حذف التدخل رقم ${id}`, 'success');
        // يمكن إضافة منطق الحذف هنا
    }
}

function refreshData() {
    showNotification('جاري تحديث البيانات...', 'info');
    // محاكاة تحديث البيانات
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function exportData() {
    showNotification('جاري تصدير البيانات...', 'info');
    // يمكن إضافة منطق التصدير هنا
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
        notification.remove();
    });

    // إنشاء الإشعار الجديد
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icons = {
        'success': 'check-circle',
        'error': 'times-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };

    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };

    notification.innerHTML = `
        <div style="
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border-left: 4px solid ${colors[type]};
            display: flex;
            align-items: center;
            gap: 0.75rem;
            max-width: 400px;
            z-index: 9999;
            animation: slideInRight 0.5s ease-out;
        ">
            <i class="fas fa-${icons[type]}" style="color: ${colors[type]}; font-size: 1.2rem;"></i>
            <span style="color: #1f2937; font-weight: 600;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                margin-left: auto;
                border-radius: 0.25rem;
                transition: all 0.2s;
            ">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.5s ease-in forwards';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }
    }, 5000);
}

// تأثيرات CSS إضافية
const additionalStyles = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutRight {
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}