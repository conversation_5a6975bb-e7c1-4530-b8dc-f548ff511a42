{% extends 'base.html' %}
{% load static %}

{% block title %}التدخلات اليومية - النظام الجديد{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daily_interventions.css' %}">
<style>
    /* أنماط مبسطة للنظام الجديد */
    .forms-container {
        display: none;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
        padding: 20px;
    }

    .form-section {
        display: none;
        margin-top: 20px;
    }

    .form-section.active {
        display: block;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }

    .status-initial { background-color: #ffc107; color: #000; }
    .status-reconnaissance { background-color: #17a2b8; color: #fff; }
    .status-intervention { background-color: #28a745; color: #fff; }
    .status-completed { background-color: #6c757d; color: #fff; }

    .btn-group-actions {
        display: flex;
        gap: 5px;
    }

    .detail-section {
        display: none;
        margin-top: 15px;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        background-color: #f8f9fa;
    }

    .detail-section.show {
        display: block;
    }

    #form-header-top {
        display: none;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }

    .close-form {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        float: right;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}
    
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-ambulance"></i> التدخلات اليومية</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" id="btn-initial-report">
                        <i class="fas fa-plus"></i> بلاغ أولي
                    </button>
                    <button type="button" class="btn btn-secondary" id="refresh-table">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Header -->
    <div id="form-header-top">
        <button type="button" class="close-form">&times;</button>
        <h3>عنوان النموذج</h3>
        <p>وصف النموذج</p>
    </div>

    <!-- Forms Container -->
    <div class="forms-container">
        <!-- Initial Report Form -->
        <div id="initial-report-form" class="form-section">
            <form id="initialReportForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="departure-time">ساعة الخروج</label>
                            <input type="time" class="form-control" id="departure-time" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="location">مكان الحادث</label>
                            <input type="text" class="form-control" id="location" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="intervention-type">نوع التدخل</label>
                            <select class="form-control" id="intervention-type" required>
                                <option value="">اختر نوع التدخل</option>
                                <option value="medical">إجلاء صحي</option>
                                <option value="accident">حادث مرور</option>
                                <option value="fire">حريق</option>
                                <option value="agricultural-fire">حريق محاصيل زراعية</option>
                                <option value="building-fire">حرائق البنايات والمؤسسات</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="intervention-subtype">نوع التدخل الفرعي</label>
                            <select class="form-control" id="intervention-subtype">
                                <option value="">اختر نوع التدخل الفرعي</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="contact-source">الجهة المتصلة</label>
                            <select class="form-control" id="contact-source" required>
                                <option value="">اختر الجهة المتصلة</option>
                                <option value="مواطن">مواطن</option>
                                <option value="الشرطة">الشرطة</option>
                                <option value="الدرك">الدرك الوطني</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="contact-type">نوع الاتصال</label>
                            <select class="form-control" id="contact-type" required>
                                <option value="">اختر نوع الاتصال</option>
                                <option value="هاتفي">هاتفي</option>
                                <option value="راديو">راديو</option>
                                <option value="مباشر">مباشر</option>
                                <option value="وحدة تطلب الدعم">وحدة تطلب الدعم</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone-number">رقم الهاتف (اختياري)</label>
                            <input type="tel" class="form-control" id="phone-number">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="caller-name">اسم المتصل (اختياري)</label>
                            <input type="text" class="form-control" id="caller-name">
                        </div>
                    </div>
                </div>

                <!-- Vehicles Section -->
                <div class="form-group">
                    <label>الوسائل المرسلة</label>
                    <div id="vehicles-container" class="mt-2">
                        <!-- سيتم تحميل الوسائل ديناميكياً -->
                    </div>
                </div>

                <!-- Intervention Details Sections -->
                <div id="medical-details" class="detail-section">
                    <h5>تفاصيل الإجلاء الصحي</h5>
                    <!-- سيتم إضافة الحقول المتخصصة هنا -->
                </div>

                <div id="accident-details" class="detail-section">
                    <h5>تفاصيل حادث المرور</h5>
                    <!-- سيتم إضافة الحقول المتخصصة هنا -->
                </div>

                <div id="fire-details" class="detail-section">
                    <h5>تفاصيل الحريق</h5>
                    <!-- سيتم إضافة الحقول المتخصصة هنا -->
                </div>

                <div id="agricultural-fire-details" class="detail-section">
                    <h5>تفاصيل حريق المحاصيل الزراعية</h5>
                    <!-- سيتم إضافة الحقول المتخصصة هنا -->
                </div>

                <div id="building-fire-details" class="detail-section">
                    <h5>تفاصيل حرائق البنايات والمؤسسات</h5>
                    <!-- سيتم إضافة الحقول المتخصصة هنا -->
                </div>

                <div class="form-group">
                    <label for="initial-notes">ملاحظات إضافية (اختياري)</label>
                    <textarea class="form-control" id="initial-notes" rows="3"></textarea>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-success" id="save-initial-report">
                        <i class="fas fa-save"></i> حفظ البلاغ الأولي
                    </button>
                </div>
            </form>
        </div>

        <!-- Reconnaissance Form -->
        <div id="reconnaissance-form" class="form-section">
            <form id="reconnaissanceForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="arrival-time">ساعة الوصول</label>
                            <input type="time" class="form-control" id="arrival-time" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="injured-count">عدد المصابين</label>
                            <input type="number" class="form-control" id="injured-count" min="0" value="0">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="deaths-count">عدد الوفيات</label>
                            <input type="number" class="form-control" id="deaths-count" min="0" value="0">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="material-damage">ملاحظات عن الخسائر المادية</label>
                    <textarea class="form-control" id="material-damage" rows="4"></textarea>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-success" id="save-reconnaissance">
                        <i class="fas fa-save"></i> حفظ بيانات التعرف
                    </button>
                </div>
            </form>
        </div>

        <!-- Completion Form -->
        <div id="complete-mission-form" class="form-section">
            <form id="completeMissionForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="end-time">ساعة انتهاء المهمة</label>
                            <input type="time" class="form-control" id="end-time" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="final-injured-count">العدد النهائي للمصابين</label>
                            <input type="number" class="form-control" id="final-injured-count" min="0" value="0">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="final-deaths-count">العدد النهائي للوفيات</label>
                            <input type="number" class="form-control" id="final-deaths-count" min="0" value="0">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="final-notes">الملاحظات الختامية</label>
                    <textarea class="form-control" id="final-notes" rows="4"></textarea>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-success" id="save-complete-mission">
                        <i class="fas fa-check"></i> إنهاء المهمة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Interventions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> جدول التدخلات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>رقم التدخل</th>
                                    <th>التاريخ</th>
                                    <th>ساعة الخروج</th>
                                    <th>نوع التدخل</th>
                                    <th>المكان</th>
                                    <th>الوسائل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="interventions-tbody">
                                <!-- سيتم تحميل البيانات ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/daily_interventions_new.js' %}"></script>
{% endblock %}
