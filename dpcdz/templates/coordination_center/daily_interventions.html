{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - التدخلات اليومية</title>
    <!-- نظام CSS الموحد الجديد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    {% csrf_token %}
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle clickable" id="sidebar-toggle">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <div class="page-header">
                <div class="page-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h2>التدخلات اليومية</h2>
                <p class="page-description">مركز التنسيق للوحدة - إدارة التدخلات من البلاغ الأولي حتى إنهاء المهمة</p>
            </div>

            <!-- الأزرار الرئيسية للتدخل -->
            <div class="intervention-actions">
                <button class="action-btn initial-report-btn" id="initial-report-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-bullhorn"></i>
                        <h3>بلاغ أولي</h3>
                    </div>
                </button>

                <button class="action-btn reconnaissance-btn" id="reconnaissance-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-search"></i>
                        <h3>عملية التعرف</h3>
                    </div>
                </button>

                <button class="action-btn complete-mission-btn" id="complete-mission-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-check-circle"></i>
                        <h3>إنهاء المهمة</h3>
                    </div>
                </button>
            </div>

            <!-- عنوان النموذج المعروض -->
            <div class="form-header-top" id="form-header-top" style="display: none;">
                <div class="header-title-row">
                    <i class="fas fa-bullhorn" id="header-icon"></i>
                    <h2 id="header-title">بلاغ أولي</h2>
                </div>
                <p id="header-subtitle">تحديث معلومات التدخل بعد وصول الفريق</p>
            </div>

            <!-- نماذج التدخل الثلاثة (مخفية في البداية) -->
            <div class="forms-container" style="display: none;">
                <!-- بلاغ أولي -->
                <div class="intervention-form-card" id="initial-report-form" style="display: none;">
                    <form id="initialReportForm" class="intervention-form">
                        <!-- معلومات التدخل الأساسية -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-info-circle"></i> معلومات التدخل الأساسية</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-clock"></i> ساعة ودقيقة الخروج</label>
                                    <input type="time" class="form-control" id="departure-time" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-clipboard-list"></i> نوع التدخل</label>
                                    <select class="form-control" id="intervention-type" required>
                                        <option value="">اختر نوع التدخل</option>
                                        <option value="medical"><i class="fas fa-ambulance"></i> إجلاء صحي</option>
                                        <option value="accident"><i class="fas fa-car-crash"></i> حادث مرور</option>
                                        <option value="agricultural-fire"><i class="fas fa-seedling"></i> حريق محاصيل زراعية</option>
                                        <option value="building-fire"><i class="fas fa-building"></i> حرائق البنايات والمؤسسات</option>
                                        <option value="other"><i class="fas fa-tools"></i> عمليات مختلفة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-map-marker-alt"></i> مكان الحادث</label>
                                    <input type="text" class="form-control" id="location" placeholder="أدخل الموقع بالتفصيل" required>
                                </div>
                            </div>
                            <!-- الوسائل المرسلة - نظام ديناميكي محسن -->
                            <div class="form-section">
                                <h6 class="section-title"><i class="fas fa-truck"></i> الوسائل المرسلة</h6>
                                <div id="vehicles-checklist" class="vehicles-checklist">
                                    <div class="vehicles-grid" id="vehicles-grid">
                                        <div class="loading-message">
                                            <i class="fas fa-spinner fa-spin"></i> جاري تحميل الوسائل المتاحة...
                                        </div>
                                    </div>
                                    <div class="vehicles-summary" id="vehicles-summary">
                                        <span class="text-muted">لم يتم اختيار أي وسيلة</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-user-tie"></i> الجهة المتصلة</label>
                                    <select class="form-control" id="report-source" required>
                                        <option value="">اختر الجهة المتصلة</option>
                                        <option value="citizen">مواطن</option>
                                        <option value="police">الشرطة</option>
                                        <option value="gendarmerie">الدرك الوطني</option>
                                        <option value="army">الجيش الوطني الشعبي</option>
                                        <option value="forest">مصالح الغابات</option>
                                        <option value="customs">الجمارك</option>
                                        <option value="local-authorities">السلطات المحلية</option>
                                        <option value="add-new">إضافة جهة متصل جديدة (للإدمن فقط)</option>
                                    </select>
                                    <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="addNewContactSource()" style="display: none;" id="add-contact-btn">
                                        <i class="fas fa-plus"></i> إضافة جهة جديدة
                                    </button>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-phone"></i> نوع الاتصال</label>
                                    <select class="form-control" id="contact-type">
                                        <option value="phone">هاتفي</option>
                                        <option value="radio">راديو</option>
                                        <option value="direct">مباشر</option>
                                        <option value="unit-support">وحدة تطلب الدعم</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-phone-alt"></i> رقم الهاتف (اختياري)</label>
                                    <input type="tel" class="form-control" id="phone-number" placeholder="0555123456">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-user"></i> اسم المتصل (اختياري)</label>
                                    <input type="text" class="form-control" id="caller-name" placeholder="اسم المتصل">
                                </div>
                                <div class="form-group full-width">
                                    <label class="form-label"><i class="fas fa-sticky-note"></i> ملاحظة إضافية (اختياري)</label>
                                    <textarea class="form-control" id="initial-notes" rows="3" placeholder="أدخل أي ملاحظات إضافية حول البلاغ..."></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-success" id="save-initial-report">
                                <i class="fas fa-save"></i> حفظ البلاغ الأولي
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-initial-report">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- عملية التعرف -->
                <div class="intervention-form-card" id="reconnaissance-form" style="display: none;">
                    <form id="reconnaissanceForm" class="intervention-form">
                        <!-- حقل مخفي لحفظ ID التدخل -->
                        <input type="hidden" id="current-intervention-id" value="">

                        <!-- معلومات الوصول -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-clock"></i> معلومات الوصول</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-clock"></i> ساعة الوصول</label>
                                    <input type="time" class="form-control" id="arrival-time" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-map-marker-alt"></i> موقع الحادث المحدد</label>
                                    <select class="form-control" id="incident-location" required>
                                        <option value="">اختر موقع الحادث</option>
                                        <option value="inside-house">داخل المنزل</option>
                                        <option value="outside-house">خارج المنزل</option>
                                        <option value="public-place">مكان عام</option>
                                        <option value="workplace">مكان العمل</option>
                                        <option value="road">على الطريق</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- إحصاء الضحايا -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h6>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label class="form-label"><i class="fas fa-building"></i> ملاحظة عن الخسائر المادية (اختياري)</label>
                                    <textarea class="form-control" id="material-damage" rows="3" placeholder="اكتب تفاصيل الخسائر المادية المشاهدة..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار نوع التدخل الفرعي -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-list-alt"></i> نوع التدخل الفرعي</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-clipboard-check"></i> نوع التدخل الفرعي</label>
                                    <select class="form-control" id="intervention-subtype" required>
                                        <option value="">اختر نوع التدخل أولاً</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل التدخل حسب النوع -->
                        <div class="form-section" id="intervention-details">
                            <h6 class="section-title"><i class="fas fa-list-alt"></i> تفاصيل التدخل</h6>

                            <!-- للإجلاء الصحي -->
                            <div id="medical-details" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-lungs"></i> طبيعة التدخل</label>
                                        <select class="form-control" id="medical-nature">
                                            <option value="">اختر طبيعة التدخل</option>
                                            <option value="respiratory">مشاكل تنفسية</option>
                                            <option value="cardiac">مشاكل قلبية</option>
                                            <option value="trauma">إصابات خارجية</option>
                                            <option value="burns">حروق</option>
                                            <option value="poisoning">تسمم</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addNewMedicalType()" style="display: none;" id="add-medical-btn">
                                            <i class="fas fa-plus"></i> إضافة نوع جديد (للإدمن فقط)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- لحوادث المرور -->
                            <div id="accident-details" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-car-crash"></i> طبيعة الحادث</label>
                                        <select class="form-control" id="accident-nature">
                                            <option value="">اختر طبيعة الحادث</option>
                                            <option value="collision">تصادم</option>
                                            <option value="rollover">انقلاب</option>
                                            <option value="pedestrian">دهس مشاة</option>
                                            <option value="fire">حريق مركبة</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addNewAccidentType()" style="display: none;" id="add-accident-btn">
                                            <i class="fas fa-plus"></i> إضافة نوع جديد (للإدمن فقط)
                                        </button>
                                    </div>
                                </div>
                                <!-- تم إزالة حقل نوع الطريق من نموذج التعرف حسب المطلوب -->
                            </div>

                            <!-- تم حذف النموذج المختلط للحرائق - سيتم استخدام النماذج المتخصصة المنفصلة -->

                            <!-- نموذج حريق المحاصيل الزراعية - تم تحسينه بـ checkboxes متعددة -->
                            <div id="agricultural-fire-details" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label class="form-label"><i class="fas fa-seedling"></i> نوع المحصول المحترق (يمكن اختيار أكثر من نوع)</label>
                                        <div class="checkbox-group" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-top: 10px;">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-wheat" value="standing_wheat" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                قمح واقف
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-harvest" value="harvest" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                حصيدة
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-barley" value="barley" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                شعير
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-straw" value="straw_bales" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                حزم تبن
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-forest" value="forest_bushes" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                غابة / أحراش
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-bags" value="grain_bags" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                أكياس شعير / قمح
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-trees" value="fruit_trees" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                أشجار مثمرة
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="crop-type-beehives" value="beehives" onchange="updateCropLossFields()">
                                                <span class="checkmark"></span>
                                                خلايا نحل
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-fire"></i> عدد البؤر (الموقد)</label>
                                        <input type="number" class="form-control" id="crop-fire-points" min="1" placeholder="1">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-wind"></i> اتجاه الرياح</label>
                                        <select class="form-control" id="crop-wind-direction">
                                            <option value="">اختر الاتجاه</option>
                                            <option value="شمالي">شمالي</option>
                                            <option value="جنوبي">جنوبي</option>
                                            <option value="شرقي">شرقي</option>
                                            <option value="غربي">غربي</option>
                                            <option value="شمالي شرقي">شمالي شرقي</option>
                                            <option value="شمالي غربي">شمالي غربي</option>
                                            <option value="جنوبي شرقي">جنوبي شرقي</option>
                                            <option value="جنوبي غربي">جنوبي غربي</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-tachometer-alt"></i> سرعة الرياح (كم/سا)</label>
                                        <input type="number" class="form-control" id="crop-wind-speed" min="0" step="0.1" placeholder="0">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-exclamation-triangle"></i> تهديد للسكان</label>
                                        <select class="form-control" id="crop-population-threat">
                                            <option value="لا">لا</option>
                                            <option value="نعم">نعم</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-home"></i> مكان إجلاء السكان</label>
                                        <input type="text" class="form-control" id="crop-evacuation-location" placeholder="اسم المكان">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-users"></i> عدد العائلات المتأثرة</label>
                                        <input type="number" class="form-control" id="crop-affected-families" min="0" placeholder="0">
                                    </div>
                                </div>
                            </div>

                            <!-- تم حذف النموذج المختلط لحرائق البنايات - سيتم استخدام النموذج المتخصص المنفصل -->
                        </div>

                        <!-- طلب الدعم -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-hands-helping"></i> طلب الدعم</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-question-circle"></i> هل تحتاج دعم إضافي؟</label>
                                    <select class="form-control" id="support-needed">
                                        <option value="no">شكراً، الوضع تحت السيطرة</option>
                                        <option value="vehicle">نعم وسيلة إضافية</option>
                                        <option value="neighboring-unit">نعم وحدة مجاورة</option>
                                        <option value="specialized-team">نعم فريق متخصص</option>
                                        <option value="major-disaster">تصعيد إلى كارثة كبرى</option>
                                    </select>
                                </div>
                                <div class="form-group" id="specialized-team-options" style="display: none;">
                                    <label class="form-label"><i class="fas fa-users-cog"></i> نوع الفريق المتخصص</label>
                                    <select class="form-control" id="specialized-team-type">
                                        <option value="divers">فرقة الغطاسين</option>
                                        <option value="rough-terrain">فرق التدخل في الأماكن الوعرة</option>
                                        <option value="cynotechnical">فرقة السينوتقنية</option>
                                    </select>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addNewSpecializedTeam()" style="display: none;" id="add-team-btn">
                                        <i class="fas fa-plus"></i> إضافة فرقة جديدة (للإدمن فقط)
                                    </button>
                                </div>
                                <div class="form-group" id="neighboring-unit-alert" style="display: none;">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>تنبيه:</strong> سيتم إرسال إشارة إنذار صوتي ومرئي إلى مركز التنسيق الولائي لتحديد الوحدة والوسيلة اللازمة للتدخل.
                                    </div>
                                    <button type="button" class="btn btn-warning" onclick="sendWilayaAlert()">
                                        <i class="fas fa-bell"></i> إرسال إشارة الإنذار
                                    </button>
                                </div>
                                <div class="form-group" id="major-disaster-alert" style="display: none;">
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                        <strong>تصعيد إلى كارثة كبرى:</strong> سيتم تحويل هذا التدخل إلى صفحة الكوارث الكبرى.
                                    </div>
                                    <button type="button" class="btn btn-danger" onclick="escalateToMajorDisaster()">
                                        <i class="fas fa-arrow-up"></i> تصعيد إلى كارثة كبرى
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات ختامية -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-sticky-note"></i> ملاحظات ختامية</h6>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label class="form-label"><i class="fas fa-comment"></i> ملاحظات ختامية</label>
                                    <textarea class="form-control" id="final-notes" rows="3" placeholder="أي ملاحظات إضافية حول عملية التعرف..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-warning" id="save-reconnaissance">
                                <i class="fas fa-save"></i> حفظ عملية التعرف
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-reconnaissance">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إنهاء المهمة -->
                <div class="intervention-form-card" id="complete-mission-form" style="display: none;">
                    <form id="completeMissionForm" class="intervention-form">
                        <!-- معلومات التوقيت -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-clock"></i> معلومات التوقيت</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-clock"></i> ساعة نهاية التدخل</label>
                                    <input type="time" class="form-control" id="end-time" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-stopwatch"></i> مدة التدخل الإجمالية</label>
                                    <input type="text" class="form-control" id="total-duration" readonly placeholder="سيتم حسابها تلقائياً">
                                </div>
                            </div>
                        </div>



                        <!-- الإحصائيات النهائية للمسعفين -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-ambulance"></i> إحصائيات المسعفين</h6>
                            <div id="injured-persons-container">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-calculator"></i> عدد المسعفين</label>
                                        <input type="number" class="form-control" id="final-injured-count" min="0" placeholder="0" onchange="generateInjuredFields()">
                                    </div>
                                </div>
                                <div id="injured-details" style="display: none;">
                                    <!-- سيتم إنشاء الحقول ديناميكياً -->
                                </div>
                            </div>
                        </div>

                        <!-- الإحصائيات النهائية للوفيات -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-skull-crossbones"></i> إحصائيات الوفيات</h6>
                            <div id="deaths-container">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-calculator"></i> عدد الوفيات</label>
                                        <input type="number" class="form-control" id="final-deaths-count" min="0" placeholder="0" onchange="generateDeathFields()">
                                    </div>
                                </div>
                                <div id="deaths-details" style="display: none;">
                                    <!-- سيتم إنشاء الحقول ديناميكياً -->
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل خاصة بحوادث المرور -->
                        <div class="form-section" id="traffic-specific-section" style="display: none;">
                            <h6 class="section-title"><i class="fas fa-road"></i> تفاصيل الطريق</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-route"></i> نوع الطريق</label>
                                    <select class="form-control" id="road-type">
                                        <option value="">اختر نوع الطريق</option>
                                        <option value="highway">الطريق السيار</option>
                                        <option value="national">الطريق الوطني</option>
                                        <option value="wilaya">الطريق الولائي</option>
                                        <option value="municipal">الطريق البلدي</option>
                                        <option value="other">طرق أخرى</option>
                                    </select>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addNewRoadType()" style="display: none;" id="add-road-btn">
                                        <i class="fas fa-plus"></i> إضافة نوع طريق جديد (للإدمن فقط)
                                    </button>
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-car"></i> السيارات مزودة</label>
                                    <select class="form-control" id="vehicle-fuel-type">
                                        <option value="">اختر نوع الوقود</option>
                                        <option value="gasoline">الوقود (البنزين)</option>
                                        <option value="diesel">الديزل</option>
                                        <option value="gas">الغاز</option>
                                        <option value="electric">الكهرباء</option>
                                        <option value="hybrid">هجين (وقود + كهرباء)</option>
                                        <option value="gas-gasoline">غاز + بنزين</option>
                                        <option value="unknown">غير محدد</option>
                                    </select>
                                </div>
                            </div>

                            <!-- إضافة ضحايا إضافية -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-user-plus"></i> إضافة ضحايا إضافية</label>
                                    <button type="button" class="btn btn-success" onclick="addVictim()">
                                        <i class="fas fa-plus"></i> إضافة ضحية جديدة
                                    </button>
                                </div>
                            </div>

                            <!-- حاوية الضحايا الإضافية -->
                            <div id="victims-container">
                                <!-- سيتم إضافة الضحايا هنا ديناميكياً -->
                            </div>
                        </div>

                        <!-- إحصائيات التدخل -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-chart-line"></i> إحصائيات التدخل</h6>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-truck"></i> عدد التدخلات</label>
                                    <input type="number" class="form-control" id="total-interventions" readonly placeholder="الوسائل الأساسية + وسائل الدعم">
                                    <small class="form-text text-muted">يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم</small>
                                </div>
                            </div>
                        </div>

                        <!-- خاص بالحرائق -->
                        <div class="form-section" id="fire-specific-section" style="display: none;">
                            <h6 class="section-title"><i class="fas fa-fire"></i> تفاصيل خاصة بالحرائق</h6>

                            <!-- قسم الخسائر -->
                            <div class="fire-subsection">
                                <h6 class="subsection-title"><i class="fas fa-exclamation-triangle"></i> الخسائر المسجلة</h6>

                                <!-- خسائر حسب المساحة -->
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-seedling"></i> قمح واقف (هكتار)</label>
                                        <input type="number" class="form-control" id="wheat-standing" min="0" step="0.1" placeholder="0.0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-cut"></i> حصيدة (هكتار)</label>
                                        <input type="number" class="form-control" id="harvest" min="0" step="0.1" placeholder="0.0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-leaf"></i> شعير (هكتار)</label>
                                        <input type="number" class="form-control" id="barley" min="0" step="0.1" placeholder="0.0">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-tree"></i> غابة/أحراش (هكتار)</label>
                                        <input type="number" class="form-control" id="forest" min="0" step="0.1" placeholder="0.0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-home"></i> مباني (عدد)</label>
                                        <input type="number" class="form-control" id="buildings-damaged" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-users"></i> عدد العائلات المتضررة</label>
                                        <input type="number" class="form-control" id="affected-families" min="0" placeholder="0">
                                    </div>
                                </div>

                                <!-- خسائر حسب العدد -->
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-boxes"></i> حزم تبن (عدد)</label>
                                        <input type="number" class="form-control" id="straw-bales" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-shopping-bag"></i> أكياس قمح/شعير (عدد)</label>
                                        <input type="number" class="form-control" id="grain-bags" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-apple-alt"></i> أشجار مثمرة (عدد)</label>
                                        <input type="number" class="form-control" id="fruit-trees" min="0" placeholder="0">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-bug"></i> خلايا نحل (عدد)</label>
                                        <input type="number" class="form-control" id="beehives" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group full-width">
                                        <label class="form-label"><i class="fas fa-clipboard-list"></i> وصف الخسائر المادية</label>
                                        <textarea class="form-control" id="material-damage-description" rows="2" placeholder="وصف تفصيلي للخسائر المادية الأخرى..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الأملاك المنقذة -->
                            <div class="fire-subsection">
                                <h6 class="subsection-title"><i class="fas fa-shield-alt"></i> الأملاك المنقذة</h6>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-seedling"></i> مساحة منقذة (هكتار)</label>
                                        <input type="number" class="form-control" id="saved-area" min="0" step="0.1" placeholder="0.0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-boxes"></i> حزم تبن منقذة (عدد)</label>
                                        <input type="number" class="form-control" id="saved-bales" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-home"></i> مباني محمية (عدد)</label>
                                        <input type="number" class="form-control" id="protected-buildings" min="0" placeholder="0">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label class="form-label"><i class="fas fa-tools"></i> ممتلكات وآلات منقذة</label>
                                        <textarea class="form-control" id="saved-equipment" rows="2" placeholder="وصف المعدات والآلات والممتلكات التي تم إنقاذها..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خاص بحريق المحاصيل الزراعية - تم إعادة البناء من الصفر -->
                        <div class="form-section" id="agricultural-fire-specific-section" style="display: none;">
                            <h6 class="section-title"><i class="fas fa-seedling"></i> تفاصيل حريق المحاصيل الزراعية</h6>

                            <!-- الخسائر بالمساحة -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-seedling"></i> قمح واقف (هكتار)</label>
                                    <input type="number" class="form-control" id="crop-wheat-area" min="0" step="0.1" placeholder="0.0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-cut"></i> حصيدة (هكتار)</label>
                                    <input type="number" class="form-control" id="crop-harvest-area" min="0" step="0.1" placeholder="0.0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-leaf"></i> شعير (هكتار)</label>
                                    <input type="number" class="form-control" id="crop-barley-area" min="0" step="0.1" placeholder="0.0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-tree"></i> غابة/أحراش (هكتار)</label>
                                    <input type="number" class="form-control" id="crop-forest-area" min="0" step="0.1" placeholder="0.0">
                                </div>
                            </div>

                            <!-- الخسائر بالعدد -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-boxes"></i> حزم تبن (عدد)</label>
                                    <input type="number" class="form-control" id="crop-straw-bales" min="0" placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-shopping-bag"></i> أكياس قمح/شعير (عدد)</label>
                                    <input type="number" class="form-control" id="crop-grain-bags" min="0" placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-apple-alt"></i> أشجار مثمرة (عدد)</label>
                                    <input type="number" class="form-control" id="crop-fruit-trees" min="0" placeholder="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-bug"></i> خلايا نحل (عدد)</label>
                                    <input type="number" class="form-control" id="crop-beehives" min="0" placeholder="0">
                                </div>
                            </div>

                            <!-- الأملاك المنقذة -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-shield-alt"></i> مساحة منقذة (هكتار)</label>
                                    <input type="number" class="form-control" id="crop-saved-area" min="0" step="0.1" placeholder="0.0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label"><i class="fas fa-boxes"></i> حزم التبن المنقذة (عدد)</label>
                                    <input type="number" class="form-control" id="crop-saved-bales" min="0" placeholder="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label class="form-label"><i class="fas fa-tools"></i> ممتلكات أو آلات تم إنقاذها</label>
                                    <textarea class="form-control" id="crop-saved-equipment" rows="2" placeholder="وصف الممتلكات والآلات..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- خاص بحرائق البنايات والمؤسسات -->
                        <div class="form-section" id="building-fire-specific-section" style="display: none;">
                            <h6 class="section-title"><i class="fas fa-building"></i> تفاصيل خاصة بحرائق البنايات والمؤسسات</h6>

                            <!-- قسم الإحصائيات -->
                            <div class="fire-subsection">
                                <h6 class="subsection-title"><i class="fas fa-chart-bar"></i> الإحصائيات النهائية</h6>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-users"></i> عدد العائلات المتضررة</label>
                                        <input type="number" class="form-control" id="building-affected-families" min="0" placeholder="0">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label"><i class="fas fa-user-tie"></i> عدد الأعوان المتدخلين</label>
                                        <input type="number" class="form-control" id="building-intervening-agents" min="0" placeholder="0">
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الخسائر -->
                            <div class="fire-subsection">
                                <h6 class="subsection-title"><i class="fas fa-exclamation-triangle"></i> الخسائر</h6>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label class="form-label"><i class="fas fa-clipboard-list"></i> وصف الخسائر</label>
                                        <textarea class="form-control" id="building-damage-description" rows="3" placeholder="مثال: احتراق كلي لمحل تجاري يحتوي على مواد تجميل – احتراق جزئي لطابق علوي – تلف تجهيزات إلكترونية – احتراق مخزون مواد بلاستيكية..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الأملاك المنقذة -->
                            <div class="fire-subsection">
                                <h6 class="subsection-title"><i class="fas fa-shield-alt"></i> الأملاك المنقذة</h6>
                                <div class="form-row">
                                    <div class="form-group full-width">
                                        <label class="form-label"><i class="fas fa-hands-helping"></i> وصف الأملاك المنقذة</label>
                                        <textarea class="form-control" id="building-saved-property" rows="3" placeholder="مثال: إنقاذ 4 أسطوانات غاز – منع امتداد الحريق إلى شقق الطابق الثالث – حماية محطة كهرباء مجاورة..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات الختامية -->
                        <div class="form-section">
                            <h6 class="section-title"><i class="fas fa-file-alt"></i> التقرير الختامي</h6>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label class="form-label">📝 ملاحظات ختامية</label>
                                    <textarea class="form-control" id="final-notes" rows="4" placeholder="اكتب ملخصاً عن التدخل والنتائج المحققة..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-success" id="save-complete-mission">
                                <i class="fas fa-check-double"></i> إنهاء المهمة
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-complete-mission">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول التدخلات اليومية -->
            <div class="interventions-table-section">
                <div class="table-header">
                    <h3><i class="fas fa-list"></i> جدول التدخلات اليومية</h3>
                    <div class="table-controls">
                        <button class="btn btn-success" id="refresh-table">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-info" id="export-table">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="interventions-table" id="interventions-table">
                        <thead>
                            <tr>
                                <th>معرف التدخل</th>
                                <th>توقيت الخروج</th>
                                <th>نوع التدخل</th>
                                <th>الجهة المتصلة</th>
                                <th>نوع الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>الوسائل المرسلة</th>
                                <th>موقع الحادث</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="interventions-table-body">
                            <!-- البيانات ستُحمل ديناميكياً عبر JavaScript -->
                            <tr>
                                <td colspan="10" class="text-center">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'coordination_center' %}" class="floating-btn coordination-btn" title="مركز التنسيق">
                    <i class="fas fa-building"></i>
                </a>
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>






    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل البيانات عند تحميل الصفحة
            loadInterventionsData();

            // Button handlers to show forms
            document.getElementById('initial-report-btn').addEventListener('click', function() {
                showForm('initial-report-form', 'بلاغ أولي', 'تسجيل بلاغ جديد من مواطن أو جهة أمنية', 'fas fa-bullhorn', '#dc3545');
                // تحميل الوسائل المتاحة عند فتح نموذج البلاغ الأولي
                loadAvailableVehicles();
            });

            document.getElementById('reconnaissance-btn').addEventListener('click', function() {
                showModernAlert('info', 'كيفية الوصول لنموذج التعرف', 'للوصول إلى نموذج عملية التعرف:\n\n1. أنشئ بلاغ أولي أولاً\n2. استخدم زر "عملية التعرف" من الجدول\n\nهذا يضمن ربط النموذج بالتدخل الصحيح وحفظ البيانات بشكل صحيح.', 8000);
            });

            document.getElementById('complete-mission-btn').addEventListener('click', function() {
                showModernAlert('info', 'كيفية الوصول لنموذج الإنهاء', 'للوصول إلى نموذج إنهاء المهمة:\n\n1. أكمل عملية التعرف أولاً\n2. استخدم زر "انهاء المهمة" من الجدول\n\nهذا يضمن ربط النموذج بالتدخل الصحيح وحفظ البيانات بشكل صحيح.', 8000);
            });

            // Cancel button handlers
            document.getElementById('cancel-initial-report').addEventListener('click', function() {
                hideAllForms();
            });

            document.getElementById('cancel-reconnaissance').addEventListener('click', function() {
                hideAllForms();
            });

            document.getElementById('cancel-complete-mission').addEventListener('click', function() {
                hideAllForms();
            });

            // Save handlers for the three forms
            document.getElementById('save-initial-report').addEventListener('click', async function() {
                const form = document.getElementById('initialReportForm');
                if (form.checkValidity()) {
                    try {
                        // جمع بيانات النموذج
                        const formData = new FormData();
                        formData.append('intervention_type', document.getElementById('intervention-type').value);
                        formData.append('departure_time', document.getElementById('departure-time').value);
                        formData.append('location', document.getElementById('location').value);
                        formData.append('contact_source', document.getElementById('report-source').value);
                        formData.append('contact_type', document.getElementById('contact-type').value);
                        formData.append('phone_number', document.getElementById('phone-number').value);
                        formData.append('caller_name', document.getElementById('caller-name').value);
                        formData.append('initial_notes', document.getElementById('initial-notes').value);

                        // إضافة الوسائل المختارة
                        const selectedVehicles = getSelectedVehicles();
                        selectedVehicles.forEach(vehicle => {
                            formData.append('vehicle_ids', vehicle.id);
                        });

                        // إرسال البيانات للخادم
                        const response = await fetch('/api/interventions/save-initial-report/', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showModernAlert('success', 'تم إنشاء البلاغ!', 'تم حفظ البلاغ الأولي بنجاح - رقم التدخل: ' + result.intervention_number);
                            form.reset();
                            hideAllForms();
                            // تحديث الجدول
                            refreshInterventionsTable();
                        } else {
                            showModernAlert('error', 'خطأ في الحفظ', 'حدث خطأ: ' + result.message);
                        }
                    } catch (error) {
                        console.error('خطأ في حفظ البلاغ:', error);
                        showModernAlert('error', 'خطأ في الاتصال', 'حدث خطأ في حفظ البلاغ');
                    }
                } else {
                    showModernAlert('warning', 'بيانات ناقصة', 'يرجى ملء جميع الحقول المطلوبة');
                }
            });

            document.getElementById('save-reconnaissance').addEventListener('click', async function() {
                const form = document.getElementById('reconnaissanceForm');

                console.log('Current intervention ID:', window.currentInterventionId); // للتشخيص

                if (!window.currentInterventionId) {
                    // محاولة الحصول على ID من حقل مخفي في النموذج
                    const hiddenIdField = document.getElementById('current-intervention-id');
                    if (hiddenIdField && hiddenIdField.value) {
                        window.currentInterventionId = hiddenIdField.value;
                        console.log('تم العثور على intervention ID من الحقل المخفي:', window.currentInterventionId);
                    } else {
                        // محاولة الحصول على ID من الجدول إذا كان هناك صف واحد فقط
                        const visibleRows = document.querySelectorAll('tr[data-intervention-id]');
                        if (visibleRows.length === 1) {
                            window.currentInterventionId = visibleRows[0].getAttribute('data-intervention-id');
                            console.log('تم العثور على intervention ID من الجدول:', window.currentInterventionId);
                        } else {
                            showModernAlert('warning', 'لم يتم تحديد التدخل', 'يرجى إغلاق النموذج والضغط على زر التعرف من الجدول');
                            return;
                        }
                    }
                }

                if (form.checkValidity()) {
                    try {
                        // جمع بيانات النموذج الأساسية
                        const formData = {
                            intervention_id: window.currentInterventionId,
                            status: 'intervention', // تحديث الحالة إلى "عملية تدخل" مباشرة
                            arrival_time: document.getElementById('arrival-time').value,
                            incident_location: document.getElementById('incident-location').value || '',
                            material_damage: document.getElementById('material-damage').value || '',
                            intervention_subtype: document.getElementById('intervention-subtype').value || '',
                            patient_condition: document.getElementById('medical-nature')?.value || '',
                            final_notes: document.getElementById('final-notes').value || ''
                        };

                        // إرسال البيانات الأساسية للخادم
                        const response = await fetch('/api/interventions/update-status/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCsrfToken()
                            },
                            body: JSON.stringify(formData)
                        });

                        const result = await response.json();

                        if (result.success) {
                            // حفظ التفاصيل المتخصصة حسب نوع التدخل
                            let interventionType = getCurrentInterventionType();

                            // إذا لم نحصل على النوع، جرب الحصول عليه من الجدول
                            if (interventionType === 'unknown' && window.currentInterventionId) {
                                const row = document.querySelector(`tr[data-intervention-id="${window.currentInterventionId}"]`);
                                if (row) {
                                    const typeCell = row.cells[2]; // عمود نوع التدخل
                                    const typeText = typeCell.textContent.trim();
                                    console.log('نوع التدخل من الجدول:', typeText);

                                    if (typeText.includes('حريق محاصيل') || typeText.includes('زراعية')) {
                                        interventionType = 'agricultural-fire';
                                    } else if (typeText.includes('حريق') && (typeText.includes('بناية') || typeText.includes('مؤسسة'))) {
                                        interventionType = 'building-fire';
                                    } else if (typeText.includes('إجلاء') || typeText.includes('صحي')) {
                                        interventionType = 'medical';
                                    } else if (typeText.includes('حادث') || typeText.includes('مرور')) {
                                        interventionType = 'accident';
                                    }
                                }
                            }

                            console.log('نوع التدخل النهائي:', interventionType);

                            let detailsSaved = false;

                            if (interventionType === 'agricultural-fire') {
                                detailsSaved = await saveAgriculturalFireDetails();
                            } else if (interventionType === 'building-fire') {
                                detailsSaved = await saveBuildingFireDetails();
                            } else if (interventionType === 'medical') {
                                detailsSaved = await saveMedicalEvacuationDetails();
                            } else if (interventionType === 'accident') {
                                detailsSaved = await saveTrafficAccidentDetails();
                            } else {
                                // للأنواع الأخرى، اعتبر أن الحفظ نجح
                                detailsSaved = true;
                            }

                            if (detailsSaved) {
                                showModernAlert('success', 'تم الحفظ بنجاح!', 'تم حفظ عملية التعرف بنجاح - الحالة: عملية تدخل');
                                // تحديث البيانات ديناميكياً
                                refreshInterventionsTable();
                                hideAllForms();
                            } else {
                                showModernAlert('warning', 'حفظ جزئي', 'تم حفظ البيانات الأساسية، لكن حدث خطأ في حفظ التفاصيل المتخصصة');
                            }
                        } else {
                            showModernAlert('error', 'خطأ في الحفظ', 'حدث خطأ في حفظ البيانات: ' + result.message);
                        }
                    } catch (error) {
                        console.error('خطأ في حفظ عملية التعرف:', error);
                        showModernAlert('error', 'خطأ في الاتصال', 'حدث خطأ في الاتصال بالخادم');
                    }
                } else {
                    showModernAlert('warning', 'بيانات ناقصة', 'يرجى ملء جميع الحقول المطلوبة');
                }
            });

            document.getElementById('save-complete-mission').addEventListener('click', async function() {
                const form = document.getElementById('completeMissionForm');

                console.log('Current intervention ID:', window.currentInterventionId); // للتشخيص

                if (!window.currentInterventionId) {
                    // محاولة الحصول على ID من حقل مخفي في النموذج
                    const hiddenIdField = document.getElementById('current-intervention-id');
                    if (hiddenIdField && hiddenIdField.value) {
                        window.currentInterventionId = hiddenIdField.value;
                        console.log('تم العثور على intervention ID من الحقل المخفي:', window.currentInterventionId);
                    } else {
                        // محاولة الحصول على ID من الجدول إذا كان هناك صف واحد فقط
                        const visibleRows = document.querySelectorAll('tr[data-intervention-id]');
                        if (visibleRows.length === 1) {
                            window.currentInterventionId = visibleRows[0].getAttribute('data-intervention-id');
                            console.log('تم العثور على intervention ID من الجدول:', window.currentInterventionId);
                        } else {
                            showModernAlert('warning', 'لم يتم تحديد التدخل', 'يرجى إغلاق النموذج والضغط على زر انهاء المهمة من الجدول');
                            return;
                        }
                    }
                }

                if (form.checkValidity()) {
                    try {
                        // جمع بيانات النموذج
                        const formData = {
                            intervention_id: window.currentInterventionId,
                            status: 'completed',
                            end_time: document.getElementById('end-time').value,
                            final_injured_count: document.getElementById('final-injured-count').value || 0,
                            final_deaths_count: document.getElementById('final-deaths-count').value || 0,
                            final_notes: document.getElementById('final-notes').value || ''
                        };

                        // جمع بيانات المسعفين
                        const injuredData = [];
                        const injuredCount = parseInt(document.getElementById('final-injured-count').value) || 0;
                        for (let i = 1; i <= injuredCount; i++) {
                            const name = document.getElementById(`injured-name-${i}`)?.value || '';
                            const age = document.getElementById(`injured-age-${i}`)?.value || '';
                            const gender = document.getElementById(`injured-gender-${i}`)?.value || '';
                            if (name) {
                                injuredData.push({ name, age, gender });
                            }
                        }
                        formData.injured_details = injuredData;

                        // جمع بيانات الوفيات
                        const deathsData = [];
                        const deathsCount = parseInt(document.getElementById('final-deaths-count').value) || 0;
                        for (let i = 1; i <= deathsCount; i++) {
                            const name = document.getElementById(`death-name-${i}`)?.value || '';
                            const age = document.getElementById(`death-age-${i}`)?.value || '';
                            const gender = document.getElementById(`death-gender-${i}`)?.value || '';
                            if (name) {
                                deathsData.push({ name, age, gender });
                            }
                        }
                        formData.fatalities_details = deathsData;

                        // إرسال البيانات للخادم
                        const response = await fetch('/api/interventions/update-status/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCsrfToken()
                            },
                            body: JSON.stringify(formData)
                        });

                        const result = await response.json();

                        if (result.success) {
                            // حفظ التفاصيل المتخصصة حسب نوع التدخل
                            let interventionType = getCurrentInterventionType();

                            // إذا لم نحصل على النوع، جرب الحصول عليه من الجدول
                            if (interventionType === 'unknown' && window.currentInterventionId) {
                                const row = document.querySelector(`tr[data-intervention-id="${window.currentInterventionId}"]`);
                                if (row) {
                                    const typeCell = row.cells[2]; // عمود نوع التدخل
                                    const typeText = typeCell.textContent.trim();
                                    console.log('نوع التدخل من الجدول لإنهاء المهمة:', typeText);

                                    if (typeText.includes('حريق محاصيل') || typeText.includes('زراعية')) {
                                        interventionType = 'agricultural-fire';
                                    } else if (typeText.includes('حريق') && (typeText.includes('بناية') || typeText.includes('مؤسسة'))) {
                                        interventionType = 'building-fire';
                                    } else if (typeText.includes('إجلاء') || typeText.includes('صحي')) {
                                        interventionType = 'medical';
                                    } else if (typeText.includes('حادث') || typeText.includes('مرور')) {
                                        interventionType = 'accident';
                                    }
                                }
                            }

                            console.log('نوع التدخل النهائي لإنهاء المهمة:', interventionType);

                            let detailsSaved = false;

                            if (interventionType === 'agricultural-fire') {
                                detailsSaved = await saveAgriculturalFireDetails();
                            } else if (interventionType === 'building-fire') {
                                detailsSaved = await saveBuildingFireDetails();
                            } else if (interventionType === 'medical') {
                                detailsSaved = await saveMedicalEvacuationDetails();
                            } else if (interventionType === 'accident') {
                                detailsSaved = await saveTrafficAccidentDetails();
                            } else {
                                // للأنواع الأخرى، اعتبر أن الحفظ نجح
                                detailsSaved = true;
                            }

                            if (detailsSaved) {
                                showModernAlert('success', 'تم إنهاء المهمة!', 'تم إنهاء المهمة بنجاح - الحالة: عملية منتهية');
                                // تحديث البيانات ديناميكياً
                                refreshInterventionsTable();
                                form.reset();
                                hideAllForms();
                            } else {
                                showModernAlert('warning', 'حفظ جزئي', 'تم حفظ البيانات الأساسية، لكن حدث خطأ في حفظ التفاصيل المتخصصة');
                            }
                        } else {
                            showModernAlert('error', 'خطأ في الحفظ', 'حدث خطأ في حفظ البيانات: ' + result.message);
                        }
                    } catch (error) {
                        console.error('خطأ في حفظ إنهاء المهمة:', error);
                        showModernAlert('error', 'خطأ في الاتصال', 'حدث خطأ في الاتصال بالخادم');
                    }
                } else {
                    showModernAlert('warning', 'بيانات ناقصة', 'يرجى ملء جميع الحقول المطلوبة');
                }
            });

            // Show intervention-specific fields based on type
            document.getElementById('intervention-type').addEventListener('change', function() {
                const interventionType = this.value;
                const subtypeSelect = document.getElementById('intervention-subtype');
                const medicalDetails = document.getElementById('medical-details');
                const accidentDetails = document.getElementById('accident-details');
                const trafficSection = document.getElementById('traffic-specific-section');

                // Hide all sections first
                medicalDetails.style.display = 'none';
                accidentDetails.style.display = 'none';
                if (trafficSection) trafficSection.style.display = 'none';

                // إظهار/إخفاء النماذج المتخصصة - مؤقت حتى يتم إصلاح النماذج المنفصلة
                const agriculturalFireDetails = document.getElementById('agricultural-fire-details');
                if (agriculturalFireDetails) agriculturalFireDetails.style.display = 'none';

                // Show/hide subtype section based on intervention type
                const subtypeSection = document.getElementById('intervention-subtype').closest('.form-section');
                if (subtypeSection) {
                    if (interventionType === 'agricultural-fire' || interventionType === 'building-fire') {
                        subtypeSection.style.display = 'none';
                        // إزالة required عندما يكون مخفياً
                        if (subtypeSelect) subtypeSelect.removeAttribute('required');
                    } else {
                        subtypeSection.style.display = 'block';
                        // إضافة required عندما يكون مرئياً
                        if (subtypeSelect) subtypeSelect.setAttribute('required', 'required');
                    }
                }

                // Clear subtype options and set appropriate placeholder
                let placeholderText = 'اختر نوع التدخل';
                if (interventionType === 'medical') {
                    placeholderText = 'نوع الاجلاء';
                } else if (interventionType === 'accident') {
                    placeholderText = 'اختر نوع الحادث';
                }
                subtypeSelect.innerHTML = `<option value="">${placeholderText}</option>`;

                // Populate subtype options based on intervention type
                if (interventionType === 'medical') {
                    const medicalTypes = [
                        'الاختناق (Choking)',
                        'التسممات (Poisoning)',
                        'الحروق (Burns)',
                        'الانفجارات (Explosions)',
                        'إجلاء المرضى (Patient Evacuation)',
                        'الغرقى (Drowning)'
                    ];

                    medicalTypes.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = type;
                        subtypeSelect.appendChild(option);
                    });

                    medicalDetails.style.display = 'block';
                } else if (interventionType === 'accident') {
                    const accidentTypes = [
                        'ضحايا مصدومة بالمركبات',
                        'ضحايا تصادم المركبات',
                        'ضحايا إنقلاب المركبات',
                        'ضحايا مصدومة بالقطار',
                        'ضحايا حوادث أخرى'
                    ];

                    accidentTypes.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = type;
                        subtypeSelect.appendChild(option);
                    });

                    accidentDetails.style.display = 'block';
                    if (trafficSection) trafficSection.style.display = 'block';
                } else if (interventionType === 'agricultural-fire') {
                    // Hide subtype section for agricultural fire
                    const subtypeSection = document.getElementById('intervention-subtype').closest('.form-section');
                    const subtypeSelect = document.getElementById('intervention-subtype');
                    if (subtypeSection) {
                        subtypeSection.style.display = 'none';
                        // إزالة required عندما يكون مخفياً وتعيين قيمة افتراضية
                        if (subtypeSelect) {
                            subtypeSelect.removeAttribute('required');
                            subtypeSelect.value = 'agricultural-fire-default';
                        }
                    }

                    // إظهار نموذج حريق المحاصيل - مؤقت حتى يتم إصلاح النماذج المنفصلة
                    const agriculturalFireDetails = document.getElementById('agricultural-fire-details');
                    if (agriculturalFireDetails) {
                        agriculturalFireDetails.style.display = 'block';
                    }
                } else if (interventionType === 'building-fire') {
                    // Hide subtype section for building fire
                    const subtypeSection = document.getElementById('intervention-subtype').closest('.form-section');
                    const subtypeSelect = document.getElementById('intervention-subtype');
                    if (subtypeSection) {
                        subtypeSection.style.display = 'none';
                        // إزالة required عندما يكون مخفياً وتعيين قيمة افتراضية
                        if (subtypeSelect) {
                            subtypeSelect.removeAttribute('required');
                            subtypeSelect.value = 'building-fire-default';
                        }
                    }

                    // تم حذف الكود المختلط لحرائق البنايات - سيتم استخدام النموذج المتخصص المنفصل
                }
            });

            // Handle subtype selection
            document.getElementById('intervention-subtype').addEventListener('change', function() {
                const subtype = this.value;
                const medicalNature = document.getElementById('medical-nature');
                const accidentNature = document.getElementById('accident-nature');

                if (subtype.includes('choking') || subtype.includes('الاختناق')) {
                    updateMedicalNatureOptions('choking');
                } else if (subtype.includes('poisoning') || subtype.includes('التسممات')) {
                    updateMedicalNatureOptions('poisoning');
                } else if (subtype.includes('burns') || subtype.includes('الحروق')) {
                    updateMedicalNatureOptions('burns');
                } else if (subtype.includes('explosions') || subtype.includes('الانفجارات')) {
                    updateMedicalNatureOptions('explosions');
                } else if (subtype.includes('evacuation') || subtype.includes('إجلاء')) {
                    updateMedicalNatureOptions('evacuation');
                } else if (subtype.includes('drowning') || subtype.includes('الغرقى')) {
                    updateMedicalNatureOptions('drowning');
                } else if (subtype.includes('مصدومة-بالمركبات')) {
                    updateAccidentNatureOptions('hit-by-vehicle');
                } else if (subtype.includes('تصادم-المركبات')) {
                    updateAccidentNatureOptions('collision');
                } else if (subtype.includes('إنقلاب-المركبات')) {
                    updateAccidentNatureOptions('rollover');
                } else if (subtype.includes('مصدومة-بالقطار')) {
                    updateAccidentNatureOptions('train-hit');
                } else if (subtype.includes('حوادث-أخرى')) {
                    updateAccidentNatureOptions('other-accidents');
                } else if (subtype.includes('حريق-محاصيل-زراعية')) {
                    updateFireNatureOptions('agricultural-crops');
                } else if (subtype.includes('حرائق-البنايات-والمؤسسات')) {
                    updateFireNatureOptions('buildings-institutions');
                }
            });

            // Show specialized team options when selected
            document.getElementById('support-needed').addEventListener('change', function() {
                const specializedOptions = document.getElementById('specialized-team-options');
                const neighboringAlert = document.getElementById('neighboring-unit-alert');
                const majorDisasterAlert = document.getElementById('major-disaster-alert');

                // Hide all options first
                specializedOptions.style.display = 'none';
                neighboringAlert.style.display = 'none';
                majorDisasterAlert.style.display = 'none';

                // Show relevant option
                if (this.value === 'specialized-team') {
                    specializedOptions.style.display = 'block';
                } else if (this.value === 'neighboring-unit') {
                    neighboringAlert.style.display = 'block';
                } else if (this.value === 'major-disaster') {
                    majorDisasterAlert.style.display = 'block';
                }
            });

            // Handle contact source selection
            document.getElementById('report-source').addEventListener('change', function() {
                const addContactBtn = document.getElementById('add-contact-btn');
                if (this.value === 'add-new') {
                    addContactBtn.style.display = 'block';
                    addNewContactSource();
                } else {
                    addContactBtn.style.display = 'none';
                }
            });

            // Table controls
            document.getElementById('refresh-table').addEventListener('click', function() {
                location.reload();
            });

            document.getElementById('export-table').addEventListener('click', function() {
                alert('سيتم تصدير الجدول قريباً');
            });

            // Back to top functionality
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });

        // Function to show specific form
        function showForm(formId, title, subtitle, iconClass, color) {
            // Hide all forms first
            hideAllForms();

            // Update and show the header
            const headerTop = document.getElementById('form-header-top');
            const headerIcon = document.getElementById('header-icon');
            const headerTitle = document.getElementById('header-title');
            const headerSubtitle = document.getElementById('header-subtitle');

            headerIcon.className = iconClass;
            headerTitle.textContent = title;

            // Show subtitle if provided
            if (subtitle && subtitle.trim() !== '') {
                headerSubtitle.textContent = subtitle;
                headerSubtitle.style.display = 'block';
            } else {
                headerSubtitle.style.display = 'none';
            }

            headerTop.style.background = `linear-gradient(135deg, ${color}, ${adjustColor(color, -20)})`;
            headerTop.style.display = 'block';

            // Show the forms container
            document.querySelector('.forms-container').style.display = 'block';

            // Show the specific form
            document.getElementById(formId).style.display = 'block';

            // Special handling for forms based on intervention type
            const currentInterventionType = getCurrentInterventionType();

            if (formId === 'complete-mission-form') {
                // Show/hide specific sections in completion form based on intervention type
                const fireSection = document.getElementById('fire-specific-section');
                const agriculturalFireSection = document.getElementById('agricultural-fire-specific-section');
                const buildingFireSection = document.getElementById('building-fire-specific-section');

                // Hide all sections first
                if (fireSection) fireSection.style.display = 'none';
                if (agriculturalFireSection) agriculturalFireSection.style.display = 'none';
                if (buildingFireSection) buildingFireSection.style.display = 'none';

                // Show relevant section
                if (currentInterventionType === 'agricultural-fire' && agriculturalFireSection) {
                    agriculturalFireSection.style.display = 'block';
                } else if (currentInterventionType === 'building-fire' && buildingFireSection) {
                    buildingFireSection.style.display = 'block';
                }
            } else if (formId === 'reconnaissance-form') {
                console.log('🔍 عرض نموذج التعرف...');
                console.log('📋 نوع التدخل الحالي:', currentInterventionType);

                // إظهار/إخفاء النماذج المتخصصة في مرحلة التعرف
                const medicalDetails = document.getElementById('medical-details');
                const accidentDetails = document.getElementById('accident-details');
                const agriculturalFireDetails = document.getElementById('agricultural-fire-details');

                console.log('🔍 عناصر النماذج المتخصصة:');
                console.log('  - medicalDetails:', !!medicalDetails);
                console.log('  - accidentDetails:', !!accidentDetails);
                console.log('  - agriculturalFireDetails:', !!agriculturalFireDetails);

                // Hide all sections first
                if (medicalDetails) medicalDetails.style.display = 'none';
                if (accidentDetails) accidentDetails.style.display = 'none';
                if (agriculturalFireDetails) agriculturalFireDetails.style.display = 'none';

                // Show relevant section based on intervention type
                if (currentInterventionType === 'medical' && medicalDetails) {
                    console.log('✅ إظهار نموذج الإجلاء الصحي');
                    medicalDetails.style.display = 'block';
                } else if (currentInterventionType === 'accident' && accidentDetails) {
                    console.log('✅ إظهار نموذج حادث المرور');
                    accidentDetails.style.display = 'block';
                } else if (currentInterventionType === 'agricultural-fire' && agriculturalFireDetails) {
                    console.log('✅ إظهار نموذج حريق المحاصيل');
                    agriculturalFireDetails.style.display = 'block';
                } else {
                    console.log('❌ لم يتم إظهار أي نموذج متخصص');
                    console.log('   - نوع التدخل:', currentInterventionType);
                    console.log('   - هل يساوي agricultural-fire؟', currentInterventionType === 'agricultural-fire');
                }
            }

            // Scroll to the header
            headerTop.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Function to hide all forms
        function hideAllForms() {
            document.getElementById('form-header-top').style.display = 'none';
            document.querySelector('.forms-container').style.display = 'none';
            document.getElementById('initial-report-form').style.display = 'none';
            document.getElementById('reconnaissance-form').style.display = 'none';
            document.getElementById('complete-mission-form').style.display = 'none';
            // Clear current intervention ID and type
            window.currentInterventionId = null;
            window.currentInterventionTypeForForm = null;
        }

        // Get current intervention type
        function getCurrentInterventionType() {
            console.log('🔍 getCurrentInterventionType called');
            // أولاً، تحقق من المتغير المؤقت المحفوظ من البيانات المحملة
            if (window.currentInterventionTypeForForm) {
                console.log('📋 نوع التدخل من المتغير المؤقت:', window.currentInterventionTypeForForm);

                // تحويل نوع التدخل من العربية إلى الإنجليزية للتوافق مع الكود
                const typeMapping = {
                    'حريق محاصيل زراعية': 'agricultural-fire',
                    'حرائق البنايات والمؤسسات': 'building-fire',
                    'إجلاء صحي': 'medical',
                    'حادث مرور': 'accident',
                    'agricultural-fire': 'agricultural-fire',
                    'building-fire': 'building-fire',
                    'medical': 'medical',
                    'accident': 'accident'
                };

                const mappedType = typeMapping[window.currentInterventionTypeForForm] || window.currentInterventionTypeForForm;
                console.log('🔄 نوع التدخل بعد التحويل:', mappedType);
                return mappedType;
            }

            // Try to get from current form data
            const interventionTypeSelect = document.getElementById('intervention-type');
            if (interventionTypeSelect && interventionTypeSelect.value) {
                return interventionTypeSelect.value;
            }

            // Try to get from saved intervention data
            if (window.currentInterventionId) {
                const savedData = localStorage.getItem(`intervention_${window.currentInterventionId}`);
                if (savedData) {
                    const data = JSON.parse(savedData);
                    return data.type || 'unknown';
                }
            }

            // Default fallback
            return 'unknown';
        }

        // Handle threat to residents selection for fire interventions
        document.addEventListener('DOMContentLoaded', function() {
            const threatSelect = document.getElementById('threat-to-residents');
            const evacuationDetails = document.getElementById('evacuation-details');

            if (threatSelect && evacuationDetails) {
                threatSelect.addEventListener('change', function() {
                    if (this.value === 'yes') {
                        evacuationDetails.style.display = 'flex';
                    } else {
                        evacuationDetails.style.display = 'none';
                    }
                });
            }
        });

        // Helper function to adjust color brightness
        function adjustColor(color, amount) {
            const usePound = color[0] === '#';
            const col = usePound ? color.slice(1) : color;
            const num = parseInt(col, 16);
            let r = (num >> 16) + amount;
            let g = (num >> 8 & 0x00FF) + amount;
            let b = (num & 0x0000FF) + amount;
            r = r > 255 ? 255 : r < 0 ? 0 : r;
            g = g > 255 ? 255 : g < 0 ? 0 : g;
            b = b > 255 ? 255 : b < 0 ? 0 : b;
            return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
        }

        // دوال للنماذج المتخصصة المنفصلة
        function showSpecializedForm(interventionType, interventionId) {
            // إخفاء جميع النماذج أولاً
            hideAllSpecializedForms();

            // تعيين ID التدخل الحالي
            window.currentInterventionId = interventionId;

            // عرض النموذج المناسب
            switch(interventionType) {
                case 'agricultural-fire':
                    document.getElementById('agricultural-fire-form').style.display = 'block';
                    break;
                case 'building-fire':
                    document.getElementById('building-fire-form').style.display = 'block';
                    break;
                case 'medical':
                    document.getElementById('medical-evacuation-form').style.display = 'block';
                    break;
                case 'accident':
                    document.getElementById('traffic-accident-form').style.display = 'block';
                    break;
            }
        }

        function hideAllSpecializedForms() {
            const forms = [
                'agricultural-fire-form',
                'building-fire-form',
                'medical-evacuation-form',
                'traffic-accident-form'
            ];

            forms.forEach(formId => {
                const form = document.getElementById(formId);
                if (form) {
                    form.style.display = 'none';
                }
            });
        }

        // دوال إخفاء النماذج المتخصصة
        function hideAgriculturalFireForm() {
            document.getElementById('agricultural-fire-form').style.display = 'none';
        }

        function hideBuildingFireForm() {
            document.getElementById('building-fire-form').style.display = 'none';
        }

        function hideMedicalEvacuationForm() {
            document.getElementById('medical-evacuation-form').style.display = 'none';
        }

        function hideTrafficAccidentForm() {
            document.getElementById('traffic-accident-form').style.display = 'none';
        }

        // Functions for table action buttons
        async function updateToReconnaissance(interventionId) {
            // Set current intervention ID for tracking
            window.currentInterventionId = interventionId;
            console.log('تم تعيين intervention ID:', interventionId);

            // جلب نوع التدخل من الصف في الجدول
            const row = document.querySelector(`tr[data-intervention-id="${interventionId}"]`);
            const interventionType = row ? row.cells[2].textContent.trim() : '';

            console.log('نوع التدخل من الجدول:', interventionType);

            // تحديد عنوان النموذج حسب نوع التدخل
            let formTitle = 'عملية التعرف';
            if (interventionType.includes('إجلاء صحي')) {
                formTitle = 'عملية التعرف - إجلاء صحي';
            } else if (interventionType.includes('حادث مرور')) {
                formTitle = 'عملية التعرف - حادث مرور';
            } else if (interventionType.includes('حرائق البنايات والمؤسسات')) {
                formTitle = 'عملية التعرف - حريق بنايات';
            } else if (interventionType.includes('حريق محاصيل زراعية')) {
                formTitle = 'عملية التعرف - حريق محاصيل';
            } else if (interventionType.includes('حريق')) {
                formTitle = 'عملية التعرف - حريق';
            }

            // تحميل بيانات التدخل من الخادم أولاً
            try {
                const response = await fetch(`/api/interventions/get-details/${interventionId}/`);
                const data = await response.json();

                if (data.success) {
                    // استخدام النموذج العادي لجميع الأنواع مؤقتاً حتى يتم إصلاح النماذج المتخصصة
                    // TODO: تفعيل النماذج المتخصصة المنفصلة لاحقاً
                    // تحديد نوع التدخل في النموذج الرئيسي حسب البيانات المحفوظة أولاً
                    const interventionTypeSelect = document.getElementById('intervention-type');
                    if (interventionTypeSelect && data.intervention.intervention_type) {
                        interventionTypeSelect.value = data.intervention.intervention_type;

                        // تفعيل الحقول المناسبة حسب النوع
                        const event = new Event('change');
                        interventionTypeSelect.dispatchEvent(event);

                        // انتظار قليل ثم ملء النوع الفرعي إذا كان متوفراً
                        setTimeout(() => {
                            const subtypeSelect = document.getElementById('intervention-subtype');
                            if (subtypeSelect && data.intervention.intervention_subtype) {
                                subtypeSelect.value = data.intervention.intervention_subtype;
                            }
                        }, 100);
                    }

                    // حفظ نوع التدخل مؤقتاً للاستخدام في showForm
                    window.currentInterventionTypeForForm = data.intervention.intervention_type;
                    console.log('🔥 updateToReconnaissance: حفظ نوع التدخل:', window.currentInterventionTypeForForm);

                    // فتح نموذج التعرف مع العنوان المناسب
                    showForm('reconnaissance-form', formTitle, 'تحديث معلومات التدخل بعد وصول الفريق', 'fas fa-search', '#ffc107');

                    // تعيين ID التدخل في الحقل المخفي
                    const hiddenIdField = document.getElementById('current-intervention-id');
                    if (hiddenIdField) {
                        hiddenIdField.value = interventionId;
                    }

                    // ملء النموذج بالبيانات المحفوظة
                    populateReconnaissanceForm(data.intervention);
                } else {
                    console.error('فشل في تحميل بيانات التدخل:', data.message);
                    // فتح النموذج حتى لو فشل التحميل
                    showForm('reconnaissance-form', formTitle, 'تحديث معلومات التدخل بعد وصول الفريق', 'fas fa-search', '#ffc107');
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات التدخل:', error);
                // فتح النموذج حتى لو حدث خطأ
                showForm('reconnaissance-form', formTitle, 'تحديث معلومات التدخل بعد وصول الفريق', 'fas fa-search', '#ffc107');
            }
        }

        async function updateToComplete(interventionId) {
            // Set current intervention ID for tracking
            window.currentInterventionId = interventionId;

            // جلب نوع التدخل من الصف في الجدول
            const row = document.querySelector(`tr[data-intervention-id="${interventionId}"]`);
            const interventionType = row ? row.cells[2].textContent.trim() : '';

            console.log('نوع التدخل من الجدول:', interventionType);

            // تحديد عنوان النموذج حسب نوع التدخل
            let formTitle = 'انهاء المهمة';
            if (interventionType.includes('إجلاء صحي')) {
                formTitle = 'انهاء المهمة - إجلاء صحي';
            } else if (interventionType.includes('حادث مرور')) {
                formTitle = 'انهاء المهمة - حادث مرور';
            } else if (interventionType.includes('حرائق البنايات والمؤسسات')) {
                formTitle = 'انهاء المهمة - حريق بنايات';
            } else if (interventionType.includes('حريق محاصيل زراعية')) {
                formTitle = 'انهاء المهمة - حريق محاصيل';
            } else if (interventionType.includes('حريق')) {
                formTitle = 'انهاء المهمة - حريق';
            }

            // تحميل بيانات التدخل من الخادم أولاً
            try {
                const response = await fetch(`/api/interventions/get-details/${interventionId}/`);
                const data = await response.json();

                if (data.success) {
                    // تحديد نوع التدخل في النموذج الرئيسي حسب البيانات المحفوظة أولاً
                    const interventionTypeSelect = document.getElementById('intervention-type');
                    if (interventionTypeSelect && data.intervention.intervention_type) {
                        interventionTypeSelect.value = data.intervention.intervention_type;

                        // تفعيل الحقول المناسبة حسب النوع
                        const event = new Event('change');
                        interventionTypeSelect.dispatchEvent(event);
                    }

                    // حفظ نوع التدخل مؤقتاً للاستخدام في showForm
                    window.currentInterventionTypeForForm = data.intervention.intervention_type;

                    // فتح نموذج إنهاء المهمة مع العنوان المناسب
                    showForm('complete-mission-form', formTitle, 'تسجيل النتائج النهائية وإغلاق التدخل', 'fas fa-check-circle', '#28a745');

                    // ملء النموذج بالبيانات المحفوظة
                    populateCompletionForm(data.intervention);
                } else {
                    console.error('فشل في تحميل بيانات التدخل:', data.message);
                    // فتح النموذج حتى لو فشل التحميل
                    showForm('complete-mission-form', formTitle, 'تسجيل النتائج النهائية وإغلاق التدخل', 'fas fa-check-circle', '#28a745');
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات التدخل:', error);
                // فتح النموذج حتى لو حدث خطأ
                showForm('complete-mission-form', formTitle, 'تسجيل النتائج النهائية وإغلاق التدخل', 'fas fa-check-circle', '#28a745');
            }
        }

        // دالة لتحديث الحالة إلى "عملية تدخل"
        async function updateToIntervention(interventionId) {
            if (confirm('هل تريد بدء عملية التدخل؟')) {
                try {
                    const response = await fetch('/api/interventions/update-status/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken()
                        },
                        body: JSON.stringify({
                            intervention_id: interventionId,
                            status: 'intervention'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert('تم بدء عملية التدخل بنجاح');
                        // تحديث الجدول محلياً
                        const row = document.querySelector(`tr[data-intervention-id="${interventionId}"]`);
                        if (row) {
                            updateActionButtons(row, 'intervention');
                            const statusCell = row.cells[6];
                            statusCell.innerHTML = `<span class="status intervention">عملية تدخل</span>`;
                            row.setAttribute('data-status', 'intervention');
                        }
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                } catch (error) {
                    console.error('خطأ في تحديث الحالة:', error);
                    alert('حدث خطأ في الاتصال بالخادم');
                }
            }
        }

        // دالة لملء نموذج التعرف بالبيانات المحفوظة
        function populateReconnaissanceForm(intervention) {
            // ملء ساعة الوصول
            const arrivalTimeInput = document.getElementById('arrival-time');
            if (arrivalTimeInput && intervention.arrival_time) {
                arrivalTimeInput.value = intervention.arrival_time;
            }

            // ملء نوع الموقع
            const locationTypeSelect = document.getElementById('location-type');
            if (locationTypeSelect && intervention.location_type) {
                locationTypeSelect.value = intervention.location_type;
            }

            // ملء عدد المسعفين
            const injuredCountInput = document.getElementById('injured-count');
            if (injuredCountInput && intervention.injured_count !== undefined) {
                injuredCountInput.value = intervention.injured_count;
            }

            // ملء عدد الوفيات
            const deathsCountInput = document.getElementById('deaths-count');
            if (deathsCountInput && intervention.deaths_count !== undefined) {
                deathsCountInput.value = intervention.deaths_count;
            }

            // ملء ملاحظات الخسائر المادية
            const materialDamageTextarea = document.getElementById('material-damage');
            if (materialDamageTextarea && intervention.material_damage) {
                materialDamageTextarea.value = intervention.material_damage;
            }
        }

        // دالة لملء نموذج إنهاء المهمة بالبيانات المحفوظة
        function populateCompletionForm(intervention) {
            // ملء ساعة انتهاء التدخل
            const endTimeInput = document.getElementById('end-time');
            if (endTimeInput && intervention.end_time) {
                endTimeInput.value = intervention.end_time;
            }

            // ملء الإحصائيات النهائية
            const finalInjuredCountInput = document.getElementById('final-injured-count');
            if (finalInjuredCountInput && intervention.injured_count !== undefined) {
                finalInjuredCountInput.value = intervention.injured_count;
            }

            const finalDeathsCountInput = document.getElementById('final-deaths-count');
            if (finalDeathsCountInput && intervention.deaths_count !== undefined) {
                finalDeathsCountInput.value = intervention.deaths_count;
            }

            // ملء الملاحظات الختامية
            const finalNotesTextarea = document.getElementById('final-notes');
            if (finalNotesTextarea && intervention.final_notes) {
                finalNotesTextarea.value = intervention.final_notes;
            }

            // ملء تفاصيل الخسائر
            const lossesTextarea = document.getElementById('losses-description');
            if (lossesTextarea && intervention.losses_description) {
                lossesTextarea.value = intervention.losses_description;
            }

            // ملء الأملاك المنقذة
            const savedPropertiesTextarea = document.getElementById('saved-properties');
            if (savedPropertiesTextarea && intervention.saved_properties) {
                savedPropertiesTextarea.value = intervention.saved_properties;
            }
        }

        // Function to refresh the interventions table after adding new intervention
        function refreshInterventionsTable() {
            // تحديث البيانات ديناميكياً بدلاً من إعادة تحميل الصفحة
            loadInterventionsData();
        }

        function updateInterventionStatus(status) {
            // Update the specific intervention's status
            const interventionId = window.currentInterventionId;
            if (interventionId) {
                const row = document.querySelector(`tr[data-intervention-id="${interventionId}"]`);
                if (row) {
                    const statusCell = row.cells[8]; // عمود الحالة هو العمود رقم 8

                    let statusText = '';
                    let statusClass = '';

                    switch(status) {
                        case 'intervention':
                            statusText = 'عملية تدخل';
                            statusClass = 'intervention';
                            row.setAttribute('data-status', 'intervention');
                            break;
                        case 'completed':
                            statusText = 'عملية منتهية';
                            statusClass = 'completed';
                            row.setAttribute('data-status', 'completed');
                            break;
                        case 'reconnaissance':
                            statusText = 'قيد التعرف';
                            statusClass = 'reconnaissance';
                            row.setAttribute('data-status', 'reconnaissance');
                            break;
                        default:
                            statusText = 'بلاغ أولي';
                            statusClass = 'initial-report';
                            row.setAttribute('data-status', 'initial_report');
                    }

                    statusCell.innerHTML = `<span class="status ${statusClass}">${statusText}</span>`;

                    // Update action buttons based on new status
                    updateActionButtons(row, status);
                }
            } else {
                // For new interventions, update the first row
                const table = document.getElementById('interventions-table').getElementsByTagName('tbody')[0];
                if (table.rows.length > 0) {
                    const lastRow = table.rows[0];
                    const statusCell = lastRow.cells[8]; // عمود الحالة هو العمود رقم 8

                    let statusText = '';
                    let statusClass = '';

                    switch(status) {
                        case 'intervention':
                            statusText = 'عملية تدخل';
                            statusClass = 'intervention';
                            break;
                        case 'completed':
                            statusText = 'عملية منتهية';
                            statusClass = 'completed';
                            break;
                        case 'reconnaissance':
                            statusText = 'قيد التعرف';
                            statusClass = 'reconnaissance';
                            break;
                        default:
                            statusText = 'بلاغ أولي';
                            statusClass = 'initial-report';
                    }

                    statusCell.innerHTML = `<span class="status ${statusClass}">${statusText}</span>`;
                }
            }
        }

        function updateActionButtons(row, status) {
            const actionCell = row.cells[9]; // عمود الإجراءات هو العمود رقم 9
            const interventionId = row.getAttribute('data-intervention-id');

            if (status === 'completed') {
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-info" onclick="viewIntervention(${interventionId})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i> عرض التفاصيل
                    </button>
                `;
            } else if (status === 'intervention') {
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-success" onclick="updateToComplete(${interventionId})" title="إنهاء المهمة">
                        <i class="fas fa-check"></i> انهاء المهمة
                    </button>
                `;
            } else if (status === 'reconnaissance') {
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-success" onclick="updateToComplete(${interventionId})" title="إنهاء المهمة">
                        <i class="fas fa-check"></i> انهاء المهمة
                    </button>
                `;
            } else if (status === 'initial_report') {
                actionCell.innerHTML = `
                    <button class="btn btn-sm btn-warning" onclick="updateToReconnaissance(${interventionId})" title="عملية التعرف">
                        <i class="fas fa-search"></i> عملية التعرف
                    </button>
                `;
            }
        }

        // Helper function to get intervention type text
        function getInterventionTypeText(type) {
            const types = {
                'medical': 'إجلاء صحي',
                'accident': 'حادث مرور',
                'agricultural-fire': 'حريق محاصيل زراعية',
                'building-fire': 'حرائق البنايات والمؤسسات',
                'other': 'عمليات مختلفة'
            };
            return types[type] || type;
        }

        // Intervention management functions
        function editIntervention(id) {
            alert(`تحرير التدخل رقم ${id}`);
        }

        function escalateIntervention(id) {
            if (confirm('هل تريد تصعيد هذا التدخل إلى كارثة كبرى؟')) {
                alert(`تم تصعيد التدخل رقم ${id} إلى الكوارث الكبرى`);
                // Here you would redirect to major disasters page
                // window.location.href = "{% url 'major_disasters' %}";
            }
        }

        // Generate dynamic fields for injured persons
        function generateInjuredFields() {
            const count = parseInt(document.getElementById('final-injured-count').value) || 0;
            const container = document.getElementById('injured-details');

            if (count > 0) {
                container.style.display = 'block';
                container.innerHTML = '';

                for (let i = 1; i <= count; i++) {
                    const fieldGroup = document.createElement('div');
                    fieldGroup.className = 'form-row';
                    fieldGroup.innerHTML = `
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-user"></i> اسم المصاب ${i}</label>
                            <input type="text" class="form-control" id="injured-name-${i}" placeholder="الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-birthday-cake"></i> السن</label>
                            <input type="number" class="form-control" id="injured-age-${i}" min="0" max="120" placeholder="السن">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-venus-mars"></i> الجنس</label>
                            <select class="form-control" id="injured-gender-${i}">
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                        <div class="form-group" id="injured-status-${i}" style="display: none;">
                            <label class="form-label"><i class="fas fa-car"></i> الحالة</label>
                            <select class="form-control" id="injured-vehicle-status-${i}">
                                <option value="driver">سائق</option>
                                <option value="passenger">راكب</option>
                                <option value="pedestrian">مشاة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    `;
                    container.appendChild(fieldGroup);
                }

                // Show vehicle status fields for traffic accidents
                const interventionType = document.getElementById('intervention-type').value;
                if (interventionType === 'accident') {
                    for (let i = 1; i <= count; i++) {
                        document.getElementById(`injured-status-${i}`).style.display = 'block';
                    }
                }
            } else {
                container.style.display = 'none';
                container.innerHTML = '';
            }
        }

        // Generate dynamic fields for deaths
        function generateDeathFields() {
            const count = parseInt(document.getElementById('final-deaths-count').value) || 0;
            const container = document.getElementById('deaths-details');

            if (count > 0) {
                container.style.display = 'block';
                container.innerHTML = '';

                for (let i = 1; i <= count; i++) {
                    const fieldGroup = document.createElement('div');
                    fieldGroup.className = 'form-row';
                    fieldGroup.innerHTML = `
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-user"></i> اسم المتوفى ${i}</label>
                            <input type="text" class="form-control" id="death-name-${i}" placeholder="الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-birthday-cake"></i> السن</label>
                            <input type="number" class="form-control" id="death-age-${i}" min="0" max="120" placeholder="السن">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-venus-mars"></i> الجنس</label>
                            <select class="form-control" id="death-gender-${i}">
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                        <div class="form-group" id="death-status-${i}" style="display: none;">
                            <label class="form-label"><i class="fas fa-car"></i> الحالة</label>
                            <select class="form-control" id="death-vehicle-status-${i}">
                                <option value="driver">سائق</option>
                                <option value="passenger">راكب</option>
                                <option value="pedestrian">مشاة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    `;
                    container.appendChild(fieldGroup);
                }

                // Show vehicle status fields for traffic accidents
                const interventionType = document.getElementById('intervention-type').value;
                if (interventionType === 'accident') {
                    for (let i = 1; i <= count; i++) {
                        document.getElementById(`death-status-${i}`).style.display = 'block';
                    }
                }
            } else {
                container.style.display = 'none';
                container.innerHTML = '';
            }
        }

        // Update medical nature options based on subtype
        function updateMedicalNatureOptions(subtype) {
            const select = document.getElementById('medical-nature');
            select.innerHTML = '<option value="">اختر طبيعة التدخل</option>';

            let options = [];

            switch(subtype) {
                case 'choking':
                    options = [
                        'بالغاز الطبيعي أو البوتان',
                        'بغاز أحادي أكسيد الكربون',
                        'بإنسداد المجاري التنفسية',
                        'بالأماكن المغلقة'
                    ];
                    break;
                case 'poisoning':
                    options = [
                        'بمواد غذائية',
                        'بالأدوية',
                        'بمواد التنظيف',
                        'بلسعات/عضات حيوانات',
                        'أخرى'
                    ];
                    break;
                case 'burns':
                    options = [
                        'ألسنة النار',
                        'مواد سائلة ساخنة',
                        'مواد كيميائية/مشعة',
                        'صعقات كهربائية'
                    ];
                    break;
                case 'explosions':
                    options = [
                        'غاز البوتان/البروبان',
                        'الغاز الطبيعي',
                        'الأجهزة الكهرومنزلية',
                        'أجهزة التدفئة'
                    ];
                    break;
                case 'evacuation':
                    options = [
                        'إجلاء الجرحى',
                        'إجلاء فاقد للوعي',
                        'إجلاء الإختناقات',
                        'إجلاء التسممات',
                        'إجلاء الحروق',
                        'إجلاء الإنفجارات',
                        'إجلاء السقوط',
                        'إجلاء الشنق',
                        'إجلاء المرضى'
                    ];
                    break;
                case 'drowning':
                    options = [
                        'الغرق في المسطحات المائية',
                        'الغرق في السدود',
                        'الغرق في الأودية',
                        'الغرق في الشواطئ',
                        'الغرق في أماكن أخرى'
                    ];
                    break;
            }

            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }

        // Update accident nature options based on subtype
        function updateAccidentNatureOptions(subtype) {
            const select = document.getElementById('accident-nature');
            select.innerHTML = '<option value="">اختر طبيعة الحادث</option>';

            let options = [];

            switch(subtype) {
                case 'hit-by-vehicle':
                    options = [
                        'سيارة',
                        'شاحنة',
                        'حافلة',
                        'دراجة نارية',
                        'أخرى'
                    ];
                    break;
                case 'collision':
                    options = [
                        'سيارة بسيارة',
                        'سيارة بشاحنة',
                        'سيارة بحافلة',
                        'سيارة بدراجة نارية',
                        'أخرى'
                    ];
                    break;
                case 'rollover':
                    options = [
                        'سيارة',
                        'شاحنة',
                        'حافلة',
                        'دراجة نارية',
                        'أخرى'
                    ];
                    break;
                case 'train-hit':
                    options = [
                        'سيارة',
                        'شاحنة',
                        'حافلة',
                        'شخص',
                        'أخرى'
                    ];
                    break;
                case 'other-accidents':
                    options = [
                        'سقوط من مركبة',
                        'حريق مركبة',
                        'إنفجار مركبة',
                        'أخرى'
                    ];
                    break;
            }

            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }

        // Update fire nature options based on subtype
        function updateFireNatureOptions(subtype) {
            const select = document.getElementById('fire-nature');
            select.innerHTML = '<option value="">اختر طبيعة الحريق</option>';

            let options = [];

            switch(subtype) {
                case 'agricultural-crops':
                    options = [
                        'قمح واقف',
                        'حصيدة',
                        'شعير',
                        'حزم تبن',
                        'غابة / أحراش',
                        'أكياس شعير / قمح',
                        'أشجار مثمرة',
                        'خلايا نحل'
                    ];
                    break;
                case 'buildings-institutions':
                    options = [
                        'حريق بناية مخصصة للسكن',
                        'حريق مؤسسة مصنفة',
                        'حريق مكان مستقبل للجمهور',
                        'حريق مركبة',
                        'حريق محل أو سوق'
                    ];
                    break;
            }

            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }

        // Functions for adding new types (Admin only)
        function addNewMedicalType() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newType = prompt('أدخل نوع التدخل الطبي الجديد:');
            if (newType && newType.trim()) {
                // Add to current select
                const select = document.getElementById('medical-nature');
                const option = document.createElement('option');
                option.value = newType.trim();
                option.textContent = newType.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة النوع الجديد: ' + newType.trim());
            }
        }

        function addNewAccidentType() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newType = prompt('أدخل نوع الحادث الجديد:');
            if (newType && newType.trim()) {
                // Add to current select
                const select = document.getElementById('accident-nature');
                const option = document.createElement('option');
                option.value = newType.trim();
                option.textContent = newType.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة النوع الجديد: ' + newType.trim());
            }
        }

        function addNewFireType() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newType = prompt('أدخل نوع الحريق الجديد:');
            if (newType && newType.trim()) {
                // Add to current select
                const select = document.getElementById('fire-nature');
                const option = document.createElement('option');
                option.value = newType.trim();
                option.textContent = newType.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة النوع الجديد: ' + newType.trim());
            }
        }

        // Add new crop type function
        function addNewCropType() {
            const newCropInput = document.getElementById('new-crop-input');
            const newCropValue = newCropInput.value.trim();

            if (!newCropValue) {
                alert('يرجى إدخال نوع المحصول');
                return;
            }

            // Check if crop type already exists
            const existingCheckboxes = document.querySelectorAll('input[name="crop-types"]');
            for (let checkbox of existingCheckboxes) {
                if (checkbox.nextElementSibling.textContent === newCropValue) {
                    alert('هذا النوع موجود بالفعل');
                    return;
                }
            }

            // Add new checkbox
            const cropContainer = document.getElementById('crop-types-container');
            const checkboxCount = existingCheckboxes.length;

            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'form-check form-check-inline crop-checkbox';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input';
            checkbox.id = `crop-${checkboxCount}`;
            checkbox.value = newCropValue.toLowerCase().replace(/\s+/g, '-');
            checkbox.name = 'crop-types';
            checkbox.checked = true; // Auto-select the new crop type

            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.htmlFor = `crop-${checkboxCount}`;
            label.textContent = newCropValue;

            // Add delete button for custom crops
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger ms-2';
            deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
            deleteBtn.title = 'حذف';
            deleteBtn.onclick = function() {
                if (confirm('هل تريد حذف هذا النوع؟')) {
                    checkboxDiv.remove();
                }
            };

            checkboxDiv.appendChild(checkbox);
            checkboxDiv.appendChild(label);
            checkboxDiv.appendChild(deleteBtn);
            cropContainer.appendChild(checkboxDiv);

            // Clear input
            newCropInput.value = '';

            // Show success message
            alert('تم إضافة نوع المحصول الجديد: ' + newCropValue);
        }

        // Handle Enter key in new crop input
        document.addEventListener('DOMContentLoaded', function() {
            const newCropInput = document.getElementById('new-crop-input');
            if (newCropInput) {
                newCropInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        addNewCropType();
                    }
                });
            }
        });

        function addNewContactSource() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newSource = prompt('أدخل الجهة المتصلة الجديدة:');
            if (newSource && newSource.trim()) {
                // Add to current select
                const select = document.getElementById('report-source');
                const option = document.createElement('option');
                option.value = newSource.trim().toLowerCase().replace(/\s+/g, '-');
                option.textContent = newSource.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة الجهة الجديدة: ' + newSource.trim());
            }
        }

        function addNewSpecializedTeam() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newTeam = prompt('أدخل اسم الفرقة المتخصصة الجديدة:');
            if (newTeam && newTeam.trim()) {
                // Add to specialized teams options
                const select = document.getElementById('specialized-team-type');
                const option = document.createElement('option');
                option.value = newTeam.trim().toLowerCase().replace(/\s+/g, '-');
                option.textContent = newTeam.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة الفرقة الجديدة: ' + newTeam.trim());
            }
        }

        // Check if user is admin (placeholder function)
        function isUserAdmin() {
            // This should check actual user permissions from backend
            // For now, return true for demonstration
            return true; // Replace with actual admin check
        }

        // Send alert to wilaya coordination center
        function sendWilayaAlert() {
            if (confirm('هل تريد إرسال إشارة إنذار إلى مركز التنسيق الولائي؟')) {
                // Here you would send actual alert to backend
                alert('🚨 تم إرسال إشارة إنذار صوتي ومرئي إلى مركز التنسيق الولائي');

                // Visual and audio alert simulation
                document.body.style.backgroundColor = '#ffcccc';
                setTimeout(() => {
                    document.body.style.backgroundColor = '';
                }, 1000);

                // Play alert sound (if available)
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.play();
                } catch(e) {
                    console.log('Audio alert not available');
                }
            }
        }

        // دالة للحصول على CSRF token
        function getCsrfToken() {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                return csrfToken.value;
            }
            // البحث في الكوكيز كبديل
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        }

        // دالة تحميل البيانات من الخادم
        async function loadInterventionsData() {
            try {
                const response = await fetch('/api/interventions/get-all/');
                const data = await response.json();

                if (data.success) {
                    displayInterventions(data.interventions);
                } else {
                    console.error('فشل في تحميل البيانات:', data.error);
                    showInterventionsError('فشل في تحميل البيانات');
                }
            } catch (error) {
                console.error('خطأ في الاتصال:', error);
                showInterventionsError('حدث خطأ في الاتصال بالخادم');
            }
        }

        // دالة عرض التدخلات في الجدول
        function displayInterventions(interventions) {
            const tableBody = document.getElementById('interventions-table-body');

            if (interventions.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="text-center">لا توجد تدخلات لهذا اليوم</td></tr>';
                return;
            }

            let html = '';
            interventions.forEach(intervention => {
                html += createInterventionRow(intervention);
            });

            tableBody.innerHTML = html;
        }

        // دالة إنشاء صف التدخل
        function createInterventionRow(intervention) {
            const statusInfo = getStatusInfo(intervention.status);
            const interventionTypeDisplay = translateInterventionType(intervention.intervention_type);
            const contactSourceDisplay = translateContactSource(intervention.contact_source);
            const contactTypeDisplay = translateContactType(intervention.contact_type);

            return `
                <tr data-intervention-id="${intervention.id}" data-status="${intervention.status}">
                    <td>${intervention.intervention_number || intervention.id}</td>
                    <td>${intervention.departure_time || '-'}</td>
                    <td>${interventionTypeDisplay}</td>
                    <td>${contactSourceDisplay}</td>
                    <td>${contactTypeDisplay}</td>
                    <td>${intervention.phone_number || '-'}</td>
                    <td>${intervention.vehicle_count || 0} وسيلة</td>
                    <td>${intervention.location || '-'}</td>
                    <td><span class="status ${statusInfo.class}">${statusInfo.text}</span></td>
                    <td>${getActionButtons(intervention)}</td>
                </tr>
            `;
        }

        // دالة ترجمة نوع التدخل
        function translateInterventionType(type) {
            const translations = {
                'medical': 'إجلاء صحي',
                'accident': 'حادث مرور',
                'building-fire': 'حريق بنايات',
                'agricultural-fire': 'حريق محاصيل'
            };
            return translations[type] || type;
        }

        // دالة ترجمة الجهة المتصلة
        function translateContactSource(source) {
            const translations = {
                'citizen': 'مواطن',
                'police': 'الشرطة',
                'gendarmerie': 'الدرك الوطني',
                'hospital': 'مستشفى',
                'army': 'الجيش',
                'civil_protection': 'الحماية المدنية'
            };
            return translations[source] || source || '-';
        }

        // دالة ترجمة نوع الاتصال
        function translateContactType(type) {
            const translations = {
                'phone': 'هاتف',
                'mobile': 'هاتف نقال',
                'radio': 'لاسلكي',
                'direct': 'مباشر'
            };
            return translations[type] || type || '-';
        }

        // دالة الحصول على معلومات الحالة
        function getStatusInfo(status) {
            const statusMap = {
                'initial_report': { class: 'initial-report', text: 'بلاغ أولي' },
                'reconnaissance': { class: 'reconnaissance', text: 'قيد التعرف' },
                'intervention': { class: 'intervention', text: 'عملية تدخل' },
                'completed': { class: 'completed', text: 'منتهية' },
                'escalated': { class: 'escalated', text: 'مصعدة' }
            };
            return statusMap[status] || { class: 'unknown', text: status };
        }

        // دالة الحصول على أزرار الإجراءات
        function getActionButtons(intervention) {
            let buttons = '';

            if (intervention.status === 'initial_report') {
                buttons += `<button class="btn btn-sm btn-warning me-1" onclick="updateToReconnaissance(${intervention.id})" title="عملية التعرف">
                    <i class="fas fa-search"></i> عملية التعرف
                </button>`;
            } else if (intervention.status === 'reconnaissance' || intervention.status === 'intervention') {
                buttons += `<button class="btn btn-sm btn-success me-1" onclick="updateToComplete(${intervention.id})" title="إنهاء المهمة">
                    <i class="fas fa-check"></i> انهاء المهمة
                </button>`;
            } else if (intervention.status === 'completed') {
                buttons += `<span class="text-success"><i class="fas fa-check-circle"></i> مكتمل</span>`;
            }

            buttons += `<a href="/coordination-center/intervention-details/?id=${intervention.id}" class="btn btn-sm btn-outline-primary ms-1">
                <i class="fas fa-eye"></i> عرض
            </a>`;

            return buttons;
        }

        // دالة عرض خطأ في تحميل التدخلات
        function showInterventionsError(message) {
            const tableBody = document.getElementById('interventions-table-body');
            tableBody.innerHTML = `<tr><td colspan="10" class="text-center text-danger">${message}</td></tr>`;
        }

        // Escalate to major disaster
        function escalateToMajorDisaster() {
            if (confirm('هل تريد تصعيد هذا التدخل إلى كارثة كبرى؟\nسيتم تحويلك إلى صفحة الكوارث الكبرى.')) {
                // Save current intervention data
                const interventionData = {
                    type: document.getElementById('intervention-type').value,
                    subtype: document.getElementById('intervention-subtype').value,
                    location: document.getElementById('location').value,
                    time: new Date().toLocaleString('ar-DZ'),
                    status: 'escalated-to-major-disaster'
                };

                // Store in localStorage for transfer
                localStorage.setItem('escalatedIntervention', JSON.stringify(interventionData));

                // Redirect to major disasters page
                window.location.href = '/major-disasters/';
            }
        }

        // Add victim function for traffic accidents
        function addVictim() {
            const container = document.getElementById('victims-container');
            const victimCount = container.children.length + 1;

            const victimDiv = document.createElement('div');
            victimDiv.className = 'victim-entry border p-3 mb-3';
            victimDiv.innerHTML = `
                <h6>الضحية ${victimCount}</h6>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        <label class="form-label">الاسم واللقب</label>
                        <input type="text" class="form-control" placeholder="الاسم الكامل">
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">السن</label>
                        <input type="number" class="form-control" min="0" max="120">
                    </div>
                    <div class="form-group col-md-2">
                        <label class="form-label">الجنس</label>
                        <select class="form-control">
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-control">
                            <option value="driver">سائق</option>
                            <option value="passenger">راكب</option>
                            <option value="pedestrian">مشاة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeVictim(this)">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(victimDiv);
        }

        // Remove victim function
        function removeVictim(button) {
            button.closest('.victim-entry').remove();

            // Renumber remaining victims
            const victims = document.querySelectorAll('.victim-entry h6');
            victims.forEach((victim, index) => {
                victim.textContent = `الضحية ${index + 1}`;
            });
        }

        // Helper function to get selected crop types from checkboxes
        function getSelectedCropTypes() {
            const checkboxes = document.querySelectorAll('input[name="crop-types"]:checked');
            const selectedTypes = [];

            checkboxes.forEach(checkbox => {
                selectedTypes.push(checkbox.nextElementSibling.textContent);
            });

            return selectedTypes;
        }

        // Collect completion form data including fire-specific data
        function collectCompletionFormData() {
            const data = {
                endTime: document.getElementById('end-time')?.value || '',
                totalDuration: document.getElementById('total-duration')?.value || '',
                finalInjuredCount: document.getElementById('final-injured-count')?.value || 0,
                finalDeathsCount: document.getElementById('final-deaths-count')?.value || 0,
                totalInterventions: document.getElementById('total-interventions')?.value || 0,
                finalNotes: document.getElementById('final-notes')?.value || ''
            };

            // Collect agricultural fire data if section is visible
            const agriculturalFireSection = document.getElementById('agricultural-fire-specific-section');
            if (agriculturalFireSection && agriculturalFireSection.style.display !== 'none') {
                data.agriculturalFireData = {
                    // Selected crop types (from checkboxes)
                    selectedCropTypes: getSelectedCropTypes(),

                    // Damaged areas
                    wheatStanding: parseFloat(document.getElementById('agri-wheat-standing')?.value || 0),
                    harvest: parseFloat(document.getElementById('agri-harvest')?.value || 0),
                    barley: parseFloat(document.getElementById('agri-barley')?.value || 0),
                    forest: parseFloat(document.getElementById('agri-forest')?.value || 0),

                    // Damaged items by count
                    strawBales: parseInt(document.getElementById('agri-straw-bales')?.value || 0),
                    grainBags: parseInt(document.getElementById('agri-grain-bags')?.value || 0),
                    fruitTrees: parseInt(document.getElementById('agri-fruit-trees')?.value || 0),
                    beehives: parseInt(document.getElementById('agri-beehives')?.value || 0),

                    // Saved properties
                    savedArea: parseFloat(document.getElementById('agri-saved-area')?.value || 0),
                    savedBales: parseInt(document.getElementById('agri-saved-bales')?.value || 0),
                    savedEquipment: document.getElementById('agri-saved-equipment')?.value || ''
                };

                // Calculate total burned area
                data.agriculturalFireData.totalBurnedArea = data.agriculturalFireData.wheatStanding +
                                                           data.agriculturalFireData.harvest +
                                                           data.agriculturalFireData.barley +
                                                           data.agriculturalFireData.forest;
            }

            // Collect building fire data if section is visible
            const buildingFireSection = document.getElementById('building-fire-specific-section');
            if (buildingFireSection && buildingFireSection.style.display !== 'none') {
                data.buildingFireData = {
                    affectedFamilies: parseInt(document.getElementById('building-affected-families')?.value || 0),
                    interveningAgents: parseInt(document.getElementById('building-intervening-agents')?.value || 0),
                    damageDescription: document.getElementById('building-damage-description')?.value || '',
                    savedProperty: document.getElementById('building-saved-property')?.value || ''
                };
            }



            return data;
        }

        // Add new road type function
        function addNewRoadType() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newRoadType = prompt('أدخل نوع الطريق الجديد:');
            if (newRoadType && newRoadType.trim()) {
                // Add to current select
                const select = document.getElementById('road-type');
                const option = document.createElement('option');
                option.value = newRoadType.trim().toLowerCase().replace(/\s+/g, '-');
                option.textContent = newRoadType.trim();
                select.appendChild(option);

                // Here you would normally send to backend to save
                alert('تم إضافة نوع الطريق الجديد: ' + newRoadType.trim());
            }
        }

        // Add new victim status type function
        function addNewVictimStatus() {
            // Check if user is admin
            if (!isUserAdmin()) {
                alert('هذه الصلاحية متاحة للإدمن فقط');
                return;
            }

            const newStatus = prompt('أدخل حالة الضحية الجديدة:');
            if (newStatus && newStatus.trim()) {
                // Add to all victim status selects
                const selects = document.querySelectorAll('select[id*="vehicle-status"], .victim-entry select:last-child');
                selects.forEach(select => {
                    const option = document.createElement('option');
                    option.value = newStatus.trim().toLowerCase().replace(/\s+/g, '-');
                    option.textContent = newStatus.trim();
                    select.appendChild(option);
                });

                // Here you would normally send to backend to save
                alert('تم إضافة حالة الضحية الجديدة: ' + newStatus.trim());
            }
        }

        function completeIntervention(id) {
            const modal = new bootstrap.Modal(document.getElementById('completeMissionModal'));
            modal.show();
        }

        function viewIntervention(id) {
            // توجيه للصفحة الجديدة مع تمرير معرف التدخل
            window.location.href = `/coordination-center/intervention-details/?id=${id}`;
        }

        function printReport(id) {
            alert(`طباعة تقرير التدخل رقم ${id}`);
        }

        // ========================================
        // نظام تحميل الوسائل المتاحة
        // ========================================

        /**
         * تحميل الوسائل المتاحة من API
         */
        async function loadAvailableVehicles() {
            console.log('🚛 تحميل الوسائل المتاحة...');

            try {
                // الحصول على معرف الوحدة والتاريخ
                const unitId = getCurrentUnitId();
                const currentDate = new Date().toISOString().split('T')[0];

                const response = await fetch(`/api/interventions/get-available-vehicles/?unit_id=${unitId}&date=${currentDate}`);
                const data = await response.json();

                if (data.success) {
                    displayAvailableVehicles(data.vehicles);
                } else {
                    showVehiclesError(data.message || 'فشل في تحميل الوسائل');
                }
            } catch (error) {
                console.error('خطأ في تحميل الوسائل:', error);
                showVehiclesError('حدث خطأ في الاتصال بالخادم');
            }
        }

        /**
         * عرض الوسائل المتاحة في واجهة Checklist
         */
        function displayAvailableVehicles(vehicles) {
            const vehiclesGrid = document.getElementById('vehicles-grid');
            if (!vehiclesGrid) return;

            vehiclesGrid.innerHTML = '';

            if (!vehicles || vehicles.length === 0) {
                vehiclesGrid.innerHTML = `
                    <div class="loading-message">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        لا توجد وسائل متاحة حالياً
                    </div>
                `;
                return;
            }

            vehicles.forEach(vehicle => {
                const vehicleElement = createVehicleChecklistItem(vehicle);
                vehiclesGrid.appendChild(vehicleElement);
            });

            updateVehiclesSummary();
        }

        /**
         * إنشاء عنصر وسيلة في Checklist
         */
        function createVehicleChecklistItem(vehicle) {
            const vehicleDiv = document.createElement('div');
            vehicleDiv.className = 'vehicle-checkbox-label';

            vehicleDiv.innerHTML = `
                <input type="checkbox"
                       class="vehicle-checkbox"
                       name="vehicles"
                       value="${vehicle.id}"
                       id="vehicle-${vehicle.id}"
                       onchange="updateVehiclesSummary()">
                <div class="vehicle-info-simple">
                    <div class="vehicle-name-simple">${vehicle.equipment_type}</div>
                    <div class="vehicle-details-simple">
                        ${vehicle.serial_number} | راديو: ${vehicle.radio_number} | ${vehicle.crew_count} أعوان
                    </div>
                </div>
            `;

            // إضافة تأثير التحديد
            const checkbox = vehicleDiv.querySelector('.vehicle-checkbox');
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    vehicleDiv.classList.add('selected');
                } else {
                    vehicleDiv.classList.remove('selected');
                }
            });

            return vehicleDiv;
        }

        /**
         * تحديث ملخص الوسائل المختارة
         */
        function updateVehiclesSummary() {
            const selectedVehicles = document.querySelectorAll('.vehicle-checkbox:checked');
            const summaryElement = document.getElementById('vehicles-summary');

            if (!summaryElement) return;

            if (selectedVehicles.length === 0) {
                summaryElement.innerHTML = '<span class="text-muted">لم يتم اختيار أي وسيلة</span>';
            } else {
                summaryElement.innerHTML = `
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i>
                        تم اختيار ${selectedVehicles.length} وسيلة
                    </span>
                `;
            }
        }

        /**
         * عرض رسالة خطأ في تحميل الوسائل
         */
        function showVehiclesError(message) {
            const vehiclesGrid = document.getElementById('vehicles-grid');
            if (!vehiclesGrid) return;

            vehiclesGrid.innerHTML = `
                <div class="loading-message">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    ${message}
                </div>
            `;
        }

        /**
         * الحصول على معرف الوحدة الحالية
         */
        function getCurrentUnitId() {
            // يمكن تحسين هذا لاحقاً لجلب معرف الوحدة من المستخدم
            return 11; // معرف افتراضي للاختبار
        }

        /**
         * الحصول على الوسائل المختارة
         */
        function getSelectedVehicles() {
            const selectedCheckboxes = document.querySelectorAll('.vehicle-checkbox:checked');
            return Array.from(selectedCheckboxes).map(cb => ({
                id: cb.value,
                name: cb.dataset.vehicleName || cb.value
            }));
        }

    </script>

    <style>
        /* Page header styles */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #17a2b8, #007bff);
            border-radius: 10px;
            color: white;
        }

        .page-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .page-description {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        /* Form header at top */
        .form-header-top {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px 30px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header-title-row {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .header-title-row i {
            font-size: 28px;
            opacity: 0.9;
        }

        .form-header-top h2 {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            border: none;
            color: white;
        }

        .form-header-top p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Intervention action buttons */
        .intervention-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            border: none;
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            min-height: 70px;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .initial-report-btn {
            border-left: 5px solid #dc3545;
        }

        .initial-report-btn:hover {
            background: #f8d7da;
        }

        .reconnaissance-btn {
            border-left: 5px solid #ffc107;
        }

        .reconnaissance-btn:hover {
            background: #fff3cd;
        }

        .complete-mission-btn {
            border-left: 5px solid #28a745;
        }

        .complete-mission-btn:hover {
            background: #d4edda;
        }

        .btn-content-inline {
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: center;
        }

        .btn-content-inline i {
            font-size: 24px;
        }

        .initial-report-btn .btn-content-inline i {
            color: #dc3545;
        }

        .reconnaissance-btn .btn-content-inline i {
            color: #ffc107;
        }

        .complete-mission-btn .btn-content-inline i {
            color: #28a745;
        }

        .btn-content-inline h3 {
            margin: 0;
            color: #333;
            font-size: 1.1rem;
            font-weight: bold;
        }

        /* نظام الوسائل المرسلة المحسن */
        .vehicles-checklist {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }

        .vehicles-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 10px;
        }

        .vehicle-checkbox-label {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            max-width: 350px;
        }

        .vehicle-checkbox-label:hover {
            border-color: #adb5bd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .vehicle-checkbox-label.selected {
            border-color: #28a745;
            background-color: #f8f9fa;
        }

        .vehicle-info-simple {
            margin-right: 10px;
            flex-grow: 1;
        }

        .vehicle-name-simple {
            font-weight: bold;
            color: #333;
        }

        .vehicle-details-simple {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .vehicles-summary {
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
            text-align: center;
        }

        .loading-message {
            width: 100%;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        /* Interventions table section */
        .interventions-table-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .table-header h3 {
            color: #333;
            margin: 0;
        }

        .table-controls {
            display: flex;
            gap: 10px;
        }

        .interventions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .interventions-table th,
        .interventions-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .interventions-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .interventions-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Status badges */
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status.initial-report {
            background: #6c757d;
            color: white;
        }

        .status.reconnaissance {
            background: #ffc107;
            color: #333;
        }

        .status.intervention {
            background: #17a2b8;
            color: white;
        }

        .status.completed {
            background: #28a745;
            color: white;
        }

        .status.escalated {
            background: #dc3545;
            color: white;
        }

        .status.major-disaster {
            background: #dc3545;
            color: white;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .coordination-btn {
            background-color: #28a745;
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        /* Modal enhancements - تم نقل modal-xl إلى النظام الموحد */

        .modal-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-bottom: none;
            border-radius: 10px 10px 0 0;
        }

        .modal-header .modal-title {
            font-weight: bold;
            font-size: 1.3rem;
        }

        .modal-header .modal-title i {
            margin-left: 10px;
            font-size: 1.2rem;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        .modal-footer {
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
            border-radius: 0 0 10px 10px;
        }

        .modal-content {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Form sections */
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        /* تنسيق الـ checkboxes للمحاصيل */
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            transition: all 0.3s ease;
            background: white;
            font-size: 14px;
        }

        .checkbox-label:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .checkbox-label:has(input:checked) {
            border-color: #007bff;
            background: #e7f3ff;
            font-weight: 500;
        }

        .section-title {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-left: 8px;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 10px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
            outline: none;
        }

        .form-control:hover {
            border-color: #80bdff;
        }

        /* Form check styling */
        .form-check {
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 2px solid #e9ecef;
            margin-bottom: 15px;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .form-check-label {
            font-weight: 500;
            color: #495057;
        }

        /* Button styling */
        .modal-footer .btn {
            padding: 10px 20px;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .modal-footer .btn i {
            margin-left: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-1px);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            border: none;
            color: #333;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e0a800, #d39e00);
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            border: none;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #1e7e34, #155724);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            border: none;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #545b62, #3d4142);
            transform: translateY(-1px);
        }

        /* Forms Container Layout */
        .forms-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 40px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        .intervention-form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .intervention-form-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #007bff;
        }

        .form-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
        }

        .form-header .form-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .form-header h3 {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .form-header p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        .intervention-form {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            outline: none;
        }

        .form-actions {
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #f8f9fa;
            margin-top: 30px;
        }

        .form-actions .btn {
            padding: 12px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 25px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .forms-container {
                max-width: 95%;
                gap: 25px;
            }

            .form-header {
                padding: 20px 25px;
            }

            .intervention-form {
                padding: 25px;
            }
        }

        @media (max-width: 768px) {
            .forms-container {
                gap: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .form-header {
                padding: 20px;
            }

            .form-header .form-icon {
                font-size: 36px;
                margin-bottom: 10px;
            }

            .form-header h3 {
                font-size: 1.4rem;
            }

            .form-header p {
                font-size: 0.9rem;
            }

            .intervention-form {
                padding: 20px;
            }

            .form-control {
                padding: 10px 12px;
                font-size: 13px;
            }

            .form-actions .btn {
                padding: 10px 25px;
                font-size: 14px;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
            }

            .table-controls {
                justify-content: center;
            }

            .interventions-table {
                font-size: 12px;
            }

            .interventions-table th,
            .interventions-table td {
                padding: 8px 4px;
            }
        }

        @media (max-width: 576px) {
            .row > [class*="col-"] {
                margin-bottom: 15px;
            }

            .modal-footer {
                flex-direction: column;
                gap: 10px;
            }

            .modal-footer .btn {
                width: 100%;
            }
        }

        /* Fire intervention specific styles */
        .fire-subsection {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .subsection-title {
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #dee2e6;
        }

        .subsection-title i {
            color: #dc3545;
            margin-left: 8px;
        }

        /* تم حذف CSS للنماذج المختلطة - سيتم استخدام النماذج المتخصصة المنفصلة */

        /* Enhanced form styling for fire interventions */
        #fire-specific-section .form-control {
            border-left: 3px solid #dc3545;
        }

        #fire-specific-section .form-control:focus {
            border-left-color: #c82333;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        /* Crop types checkboxes styling */
        .crop-types-container {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            max-height: 300px;
            overflow-y: auto;
        }

        .crop-checkbox {
            margin: 8px 15px 8px 0;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background-color: white;
            transition: all 0.3s ease;
        }

        .crop-checkbox:hover {
            background-color: #e8f5e8;
            border-color: #28a745;
        }

        .crop-checkbox input[type="checkbox"]:checked + label {
            color: #28a745;
            font-weight: bold;
        }

        .crop-checkbox input[type="checkbox"]:checked {
            background-color: #28a745;
            border-color: #28a745;
        }

        .add-new-crop {
            border-top: 2px dashed #28a745;
            padding-top: 15px;
        }

        .add-new-crop .input-group {
            max-width: 400px;
        }

        /* Help text styling */
        .form-text.text-muted {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        /* تمييز الصف المحدد */
        .highlighted-row {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
            animation: highlight-pulse 2s ease-in-out;
        }

        @keyframes highlight-pulse {
            0%, 100% { background-color: #fff3cd; }
            50% { background-color: #ffeaa7; }
        }

        /* نظام رسائل التنبيه الحديث */
        .modern-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 350px;
            max-width: 500px;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            animation: slideInRight 0.4s ease-out;
            transition: all 0.3s ease;
        }

        .modern-alert:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .modern-alert.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .modern-alert.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .modern-alert.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .modern-alert.info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }

        .modern-alert-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .modern-alert-icon {
            font-size: 20px;
            margin-left: 12px;
            opacity: 0.9;
        }

        .modern-alert-title {
            font-weight: 600;
            font-size: 16px;
            margin: 0;
        }

        .modern-alert-message {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
            margin: 0;
        }

        .modern-alert-close {
            position: absolute;
            top: 12px;
            left: 12px;
            background: none;
            border: none;
            color: inherit;
            font-size: 18px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s ease;
            padding: 4px;
            border-radius: 4px;
        }

        .modern-alert-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }

        .modern-alert-progress {
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 0 0 12px 12px;
            overflow: hidden;
        }

        .modern-alert-progress-bar {
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            width: 100%;
            animation: progressBar 5s linear forwards;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }

        .modern-alert.hiding {
            animation: slideOutRight 0.3s ease-in forwards;
        }
    </style>

    <script>
        // نظام رسائل التنبيه الحديث
        function showModernAlert(type, title, message, duration = 5000) {
            // إنشاء عنصر الرسالة
            const alertElement = document.createElement('div');
            alertElement.className = `modern-alert ${type}`;

            // تحديد الأيقونة حسب النوع
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            alertElement.innerHTML = `
                <button class="modern-alert-close" onclick="closeModernAlert(this)">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modern-alert-header">
                    <i class="modern-alert-icon ${icons[type]}"></i>
                    <h4 class="modern-alert-title">${title}</h4>
                </div>
                <p class="modern-alert-message">${message}</p>
                <div class="modern-alert-progress">
                    <div class="modern-alert-progress-bar"></div>
                </div>
            `;

            // إضافة الرسالة إلى الصفحة
            document.body.appendChild(alertElement);

            // إزالة الرسالة تلقائياً بعد المدة المحددة
            setTimeout(() => {
                closeModernAlert(alertElement.querySelector('.modern-alert-close'));
            }, duration);

            return alertElement;
        }

        function closeModernAlert(closeButton) {
            const alertElement = closeButton.closest('.modern-alert');
            if (alertElement) {
                alertElement.classList.add('hiding');
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.parentNode.removeChild(alertElement);
                    }
                }, 300);
            }
        }

        // دوال حفظ التفاصيل المتخصصة - تم إصلاح مشكلة "الحفظ الجزئي"
        async function saveAgriculturalFireDetails() {
            try {
                console.log('🔥 بدء حفظ تفاصيل حريق المحاصيل...');
                console.log('🆔 معرف التدخل الحالي:', window.currentInterventionId);

                // جمع أنواع المحاصيل المختارة من الـ checkboxes
                const selectedCropTypes = [];
                document.querySelectorAll('#agricultural-fire-details input[type="checkbox"]:checked').forEach(checkbox => {
                    selectedCropTypes.push(checkbox.value);
                });
                console.log('🌾 أنواع المحاصيل المختارة:', selectedCropTypes);

                const detailsData = {
                    intervention_id: window.currentInterventionId,
                    // بيانات من نموذج التعرف فقط (متاحة في مرحلة التعرف)
                    fire_type: selectedCropTypes.join(', ') || '',
                    fire_sources_count: document.getElementById('crop-fire-points')?.value || 0,
                    wind_direction: document.getElementById('crop-wind-direction')?.value || '',
                    wind_speed: document.getElementById('crop-wind-speed')?.value || 0,
                    population_threat: document.getElementById('crop-population-threat')?.value === 'نعم',
                    evacuation_location: document.getElementById('crop-evacuation-location')?.value || '',
                    affected_families_count: document.getElementById('crop-affected-families')?.value || 0,
                    support_request: document.getElementById('support-needed')?.value || ''
                };

                console.log('📋 البيانات الأساسية المجمعة:', detailsData);

                // إضافة بيانات الخسائر فقط إذا كانت متاحة (في مرحلة الإنهاء)
                const wheatAreaField = document.getElementById('crop-wheat-area');
                if (wheatAreaField) {
                    detailsData.standing_wheat_area = wheatAreaField.value || 0;
                    detailsData.harvest_area = document.getElementById('crop-harvest-area')?.value || 0;
                    detailsData.barley_area = document.getElementById('crop-barley-area')?.value || 0;
                    detailsData.forest_area = document.getElementById('crop-forest-area')?.value || 0;
                    detailsData.straw_bales_count = document.getElementById('crop-straw-bales')?.value || 0;
                    detailsData.grain_bags_count = document.getElementById('crop-grain-bags')?.value || 0;
                    detailsData.fruit_trees_count = document.getElementById('crop-fruit-trees')?.value || 0;
                    detailsData.beehives_count = document.getElementById('crop-beehives')?.value || 0;
                    detailsData.saved_area = document.getElementById('crop-saved-area')?.value || 0;
                    detailsData.saved_straw_bales = document.getElementById('crop-saved-bales')?.value || 0;
                    detailsData.saved_equipment = document.getElementById('crop-saved-equipment')?.value || '';
                }

                // إضافة بيانات إضافية فقط إذا كانت متاحة
                const endTimeField = document.getElementById('end-time');
                if (endTimeField) {
                    detailsData.end_time = endTimeField.value || '';
                    detailsData.total_duration = document.getElementById('total-duration')?.value || '';
                    detailsData.injured_count = document.getElementById('final-injured-count')?.value || 0;
                    detailsData.deaths_count = document.getElementById('final-deaths-count')?.value || 0;
                    detailsData.total_interventions = document.getElementById('total-interventions')?.value || 0;
                    detailsData.final_notes = document.getElementById('final-notes')?.value || '';
                }

                console.log('📤 إرسال البيانات إلى API:', detailsData);
                console.log('🔑 CSRF Token:', getCsrfToken());

                const response = await fetch('/api/interventions/save-agricultural-fire-details/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(detailsData)
                });

                console.log('📡 HTTP Response Status:', response.status);
                console.log('📡 HTTP Response OK:', response.ok);

                const result = await response.json();
                console.log('✅ نتيجة API:', result);
                console.log('✅ نجح الحفظ؟', result.success);
                return result.success;
            } catch (error) {
                console.error('❌ خطأ في حفظ تفاصيل حريق المحاصيل:', error);
                console.error('❌ تفاصيل الخطأ:', error.message);
                console.error('❌ Stack trace:', error.stack);
                return false;
            }
        }

        async function saveBuildingFireDetails() {
            try {
                const detailsData = {
                    intervention_id: window.currentInterventionId,
                    // بيانات من النموذج المتخصص المنفصل
                    fire_nature: document.getElementById('building-fire-nature')?.value || '',
                    fire_location: document.getElementById('building-fire-location')?.value || '',
                    specific_floor: document.getElementById('building-specific-floor')?.value || '',
                    specific_room: document.getElementById('building-specific-room')?.value || '',
                    fire_points_count: document.getElementById('building-fire-points-count')?.value || 0,
                    wind_direction: document.getElementById('building-wind-direction')?.value || '',
                    wind_speed: document.getElementById('building-wind-speed')?.value || 0,
                    population_threat: document.getElementById('building-population-threat')?.checked || false,
                    population_evacuated: document.getElementById('building-population-evacuated')?.checked || false,
                    assistance_provided: document.getElementById('building-assistance-provided')?.value || '',
                    affected_families_count: document.getElementById('building-affected-families-count')?.value || 0,
                    present_personnel: document.getElementById('building-present-personnel')?.value || '',
                    support_request: document.getElementById('building-support-request')?.value || '',
                    final_notes: document.getElementById('building-final-notes')?.value || ''
                };

                console.log('بيانات حريق البنايات من النموذج المتخصص:', detailsData);

                const response = await fetch('/api/interventions/save-building-fire-details/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(detailsData)
                });

                const result = await response.json();
                console.log('نتيجة حفظ تفاصيل حريق البنايات:', result);
                return result.success;
            } catch (error) {
                console.error('خطأ في حفظ تفاصيل حريق البنايات:', error);
                return false;
            }
        }

        async function saveMedicalEvacuationDetails() {
            try {
                const detailsData = {
                    intervention_id: window.currentInterventionId,
                    evacuation_type: document.getElementById('intervention-subtype')?.value || '',
                    patient_condition: document.getElementById('medical-nature')?.value || '',
                    support_request: document.getElementById('support-needed')?.value || '',
                    incident_location: document.getElementById('incident-location')?.value || '',
                    final_notes: document.getElementById('final-notes')?.value || ''
                };

                console.log('بيانات الإجلاء الصحي المرسلة:', detailsData);

                const response = await fetch('/api/interventions/save-medical-evacuation-details/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(detailsData)
                });

                const result = await response.json();
                console.log('نتيجة حفظ تفاصيل الإجلاء الصحي:', result);
                return result.success;
            } catch (error) {
                console.error('خطأ في حفظ تفاصيل الإجلاء الصحي:', error);
                return false;
            }
        }

        async function saveTrafficAccidentDetails() {
            try {
                const detailsData = {
                    intervention_id: window.currentInterventionId,
                    accident_type: document.getElementById('intervention-subtype')?.value || '',
                    accident_nature: document.getElementById('accident-nature')?.value || '',
                    support_request: document.getElementById('support-needed')?.value || '',
                    road_type: document.getElementById('road-type')?.value || '',
                    vehicle_fuel_type: document.getElementById('vehicle-fuel-type')?.value || '',
                    incident_location: document.getElementById('incident-location')?.value || '',
                    material_damage_notes: document.getElementById('material-damage')?.value || '',
                    final_notes: document.getElementById('final-notes')?.value || '',

                    // إضافة بيانات الضحايا والوفيات
                    final_injured_count: document.getElementById('final-injured-count')?.value || 0,
                    final_deaths_count: document.getElementById('final-deaths-count')?.value || 0,
                    total_interventions: document.getElementById('total-interventions')?.value || 0
                };

                // جمع بيانات الضحايا (المسعفين في حوادث المرور)
                const victimsData = [];
                const victimsCount = parseInt(document.getElementById('final-injured-count')?.value) || 0;
                for (let i = 1; i <= victimsCount; i++) {
                    const name = document.getElementById(`injured-name-${i}`)?.value || '';
                    const age = document.getElementById(`injured-age-${i}`)?.value || '';
                    const gender = document.getElementById(`injured-gender-${i}`)?.value || '';
                    const status = document.getElementById(`injured-vehicle-status-${i}`)?.value || ''; // سائق/راكب/مشاة
                    if (name) {
                        victimsData.push({ name, age, gender, status });
                    }
                }
                detailsData.victims_details = victimsData;

                // جمع بيانات الوفيات
                const fatalitiesData = [];
                const fatalitiesCount = parseInt(document.getElementById('final-deaths-count')?.value) || 0;
                for (let i = 1; i <= fatalitiesCount; i++) {
                    const name = document.getElementById(`death-name-${i}`)?.value || '';
                    const age = document.getElementById(`death-age-${i}`)?.value || '';
                    const gender = document.getElementById(`death-gender-${i}`)?.value || '';
                    if (name) {
                        fatalitiesData.push({ name, age, gender });
                    }
                }
                detailsData.fatalities_details = fatalitiesData;

                console.log('بيانات حادث المرور المرسلة:', detailsData);

                const response = await fetch('/api/interventions/save-traffic-accident-details/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(detailsData)
                });

                const result = await response.json();
                console.log('نتيجة حفظ تفاصيل حادث المرور:', result);
                return result.success;
            } catch (error) {
                console.error('خطأ في حفظ تفاصيل حادث المرور:', error);
                return false;
            }
        }

        // دالة لتحديث حقول الخسائر حسب نوع المحصول المختار
        function updateCropLossFields() {
            const selectedCrops = [];

            // جمع المحاصيل المختارة
            document.querySelectorAll('#agricultural-fire-details input[type="checkbox"]:checked').forEach(checkbox => {
                selectedCrops.push(checkbox.value);
            });

            // إظهار/إخفاء حقول الخسائر في نموذج إنهاء المهمة
            const lossFields = {
                'standing_wheat': 'crop-wheat-area',
                'harvest': 'crop-harvest-area',
                'barley': 'crop-barley-area',
                'forest_bushes': 'crop-forest-area',
                'straw_bales': 'crop-straw-bales',
                'grain_bags': 'crop-grain-bags',
                'fruit_trees': 'crop-fruit-trees',
                'beehives': 'crop-beehives'
            };

            // إخفاء جميع الحقول أولاً
            Object.values(lossFields).forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        formGroup.style.display = 'none';
                    }
                }
            });

            // إظهار الحقول المطلوبة فقط
            selectedCrops.forEach(cropType => {
                const fieldId = lossFields[cropType];
                if (fieldId) {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        const formGroup = field.closest('.form-group');
                        if (formGroup) {
                            formGroup.style.display = 'block';
                        }
                    }
                }
            });

            console.log('المحاصيل المختارة:', selectedCrops);
        }
    </script>

    <script>
        // التعامل مع معاملات URL للتوجيه من صفحة intervention-details
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const highlightId = urlParams.get('highlight');
            const action = urlParams.get('action');
            const date = urlParams.get('date');

            // تعيين التاريخ إذا كان محدداً
            if (date) {
                const dateInput = document.getElementById('date-filter');
                if (dateInput) {
                    dateInput.value = date;
                }
            }

            // تمييز الصف المحدد وتنفيذ الإجراء
            if (highlightId) {
                setTimeout(() => {
                    const targetRow = document.querySelector(`tr[data-intervention-id="${highlightId}"]`);
                    if (targetRow) {
                        // تمييز الصف
                        targetRow.classList.add('highlighted-row');

                        // التمرير إلى الصف
                        targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // تنفيذ الإجراء المطلوب
                        if (action === 'reconnaissance') {
                            setTimeout(() => {
                                updateToReconnaissance(parseInt(highlightId));
                            }, 1000);
                        } else if (action === 'completion') {
                            setTimeout(() => {
                                updateToComplete(parseInt(highlightId));
                            }, 1000);
                        }

                        // إزالة التمييز بعد 5 ثوانٍ
                        setTimeout(() => {
                            targetRow.classList.remove('highlighted-row');
                        }, 5000);
                    }
                }, 500); // انتظار تحميل الجدول
            }
        });
    </script>

    <!-- النماذج المتخصصة المنفصلة -->
    {% include 'coordination_center/intervention_forms/agricultural_fire_form.html' %}
    {% include 'coordination_center/intervention_forms/building_fire_form.html' %}
    {% include 'coordination_center/intervention_forms/medical_evacuation_form.html' %}
    {% include 'coordination_center/intervention_forms/traffic_accident_form.html' %}

</body>
</html>
