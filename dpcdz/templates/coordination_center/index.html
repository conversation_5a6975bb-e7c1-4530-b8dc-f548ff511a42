{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحماية المدنية الجزائرية - نظام إدخال البيانات</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="home-container">
            <h2>مركز التنسيق العملي</h2>

            <div class="menu-grid">
                <!-- الصف الأول -->
                <!-- 1. التعداد الصباحي للوحدة -->
                <a href="{% url 'daily_unit_count' %}" class="menu-item">
                    <div class="menu-icon icon-unit-count">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="menu-title">التعداد الصباحي للوحدة</div>
                    <div class="menu-description">
                        تسجيل الجاهزية اليومية للعتاد البشري والوسائل
                    </div>
                </a>

                <!-- 2. تعيين طاقم الوسائل -->
                <a href="{% url 'vehicle_crew_assignment' %}" class="menu-item">
                    <div class="menu-icon icon-crew-assignment">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="menu-title">تعيين طاقم الوسائل</div>
                    <div class="menu-description">
                        إدارة وتعيين الأعوان للوسائل المختلفة
                    </div>
                </a>

                <!-- 3. التدخلات اليومية -->
                <a href="{% url 'daily_interventions' %}" class="menu-item">
                    <div class="menu-icon icon-interventions">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="menu-title">التدخلات اليومية</div>
                    <div class="menu-description">
                        متابعة التدخلات اليومية
                    </div>
                </a>

                <!-- 4. جاهزية الوسائل -->
                <a href="{% url 'vehicle_readiness_dashboard' %}" class="menu-item">
                    <div class="menu-icon icon-vehicle-readiness">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="menu-title">جاهزية الوسائل</div>
                    <div class="menu-description">
                        مراقبة وإدارة جاهزية الوسائل وتوزيع الأعوان
                    </div>
                </a>

                <!-- الصف الثاني -->
                <!-- 5. لوحة تحكم التحقق الصباحي -->
                <a href="{% url 'morning_check_dashboard' %}" class="menu-item">
                    <div class="menu-icon icon-dashboard">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="menu-title">لوحة تحكم التحقق الصباحي</div>
                    <div class="menu-description">
                        نظرة شاملة على جاهزية جميع الوحدات
                    </div>
                </a>

                <!-- 6. نظام التحقق الصباحي المتقدم -->
                <a href="{% url 'advanced_morning_check' %}" class="menu-item">
                    <div class="menu-icon icon-advanced-morning-check">
                        <i class="fas fa-sun"></i>
                    </div>
                    <div class="menu-title">نظام التحقق الصباحي المتقدم</div>
                    <div class="menu-description">
                        نظام شامل متقدم لإدارة التعداد الصباحي والجاهزية
                    </div>
                </a>

                <!-- 7. نماذج برقيات تدخل -->
                <a href="{% url 'telegram_forms' %}" class="menu-item">
                    <div class="menu-icon icon-telegram-forms">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="menu-title">نماذج برقيات تدخل</div>
                    <div class="menu-description">
                        إدارة البرقيات والتقارير العاجلة
                    </div>
                </a>

                <!-- 8. نماذج إدخال بيانات -->
                <a href="{% url 'data_entry_forms' %}" class="menu-item">
                    <div class="menu-icon icon-data-forms">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="menu-title">نماذج إدخال بيانات</div>
                    <div class="menu-description">
                        نماذج إدخال البيانات والتقارير المختلفة
                    </div>
                </a>

                <!-- الصف الثالث -->
                <!-- 9. رئيس العدد -->
                <a href="{% url 'field_agent' %}" class="menu-item">
                    <div class="menu-icon icon-field-agent">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="menu-title">رئيس العدد</div>
                    <div class="menu-description">
                        واجهة العون الميداني لتحديث معلومات التدخل
                    </div>
                </a>

                <!-- 10. قائد الوحدة -->
                <a href="{% url 'unit_leader' %}" class="menu-item">
                    <div class="menu-icon icon-unit-leader">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="menu-title">قائد الوحدة</div>
                    <div class="menu-description">
                        واجهة قائد الوحدة عند الدعم لوحدة أخرى
                    </div>
                </a>
            </div>

            <!-- الأزرار العائمة -->
            <div class="floating-buttons">
                <a href="{% url 'home' %}" class="floating-btn home-btn" title="الصفحة الرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" id="back-to-top" class="floating-btn top-btn" title="العودة إلى الأعلى">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>

    <style>
        /* Floating buttons styles */
        .floating-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
            text-decoration: none;
            font-size: 18px;
            border: none;
            cursor: pointer;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .home-btn {
            background-color: #0d47a1;
        }

        .home-btn:hover {
            background-color: #0a3880;
        }

        .top-btn {
            background-color: #0d6efd;
        }

        .top-btn:hover {
            background-color: #0a58ca;
        }

        /* تخصيص ألوان الأيقونات حسب الوظيفة */
        .icon-daily-telegrams {
            color: #457b9d !important; /* أزرق */
        }

        .icon-urgent {
            color: #d62828 !important; /* أحمر داكن للطوارئ */
        }

        .icon-interventions {
            color: #f4a261 !important; /* برتقالي للتدخلات */
        }

        .icon-forest-fires {
            color: #e76f51 !important; /* أحمر-برتقالي للحرائق */
        }

        .icon-water {
            color: #2a9d8f !important; /* أخضر-أزرق للمياه */
        }

        .icon-unit-count {
            color: #1d3557 !important; /* أزرق داكن للتعداد */
        }

        .icon-field-agent {
            color: #6c757d !important; /* رمادي للعون الميداني */
        }

        .icon-unit-leader {
            color: #495057 !important; /* رمادي داكن لقائد الوحدة */
        }

        .icon-dashboard {
            color: #28a745 !important; /* أخضر للوحة التحكم */
        }

        .icon-advanced-morning-check {
            color: #ffc107 !important; /* أصفر للتحقق الصباحي المتقدم */
        }

        .icon-vehicle-readiness {
            color: #fd7e14 !important; /* برتقالي للوسائل */
        }

        .icon-data-forms {
            color: #17a2b8 !important; /* أزرق فاتح لنماذج البيانات */
        }

        .icon-telegram-forms {
            color: #6f42c1 !important; /* بنفسجي لنماذج البرقيات */
        }

        .icon-crew-assignment {
            color: #20c997 !important; /* أخضر فاتح لتعيين الطاقم */
        }

        /* تحديث الشبكة لعرض 4 أزرار في كل صف */
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        /* تصميم متجاوب */
        @media (max-width: 1200px) {
            .menu-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 992px) {
            .menu-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .menu-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>