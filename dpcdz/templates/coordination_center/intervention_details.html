{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>الحماية المدنية الجزائرية - تفاصيل التدخلات حسب النوع</title>
    <!-- نظام CSS الموحد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="unified-container">
            {% csrf_token %}
            
            <!-- العنوان والأدوات -->
            <div class="header-section">
                <div class="unit-count-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h1 class="unit-count-title">تفاصيل التدخلات حسب النوع</h1>
                <p class="unit-count-subtitle">عرض مفصل للتدخلات مقسمة حسب النوع مع إمكانية التصدير والطباعة</p>
                <div class="system-badge">
                    <span class="badge badge-info">
                        <i class="fas fa-chart-bar"></i> نظام التفاصيل المتقدم
                    </span>
                </div>
                
                <div class="controls-enhanced">
                    <!-- الصف الأول: التحكم الأساسي -->
                    <div class="control-row primary-controls">
                        <div class="control-item">
                            <label for="dateSelect">
                                <i class="fas fa-calendar"></i>
                                التاريخ
                            </label>
                            <input type="date" id="dateSelect" class="form-control enhanced-input"
                                   value="{{ selected_date|date:'Y-m-d'|default:'' }}" onchange="onDateChange()">
                        </div>

                        <div class="control-item">
                            <label>
                                <i class="fas fa-building"></i>
                                الوحدة الحالية
                            </label>
                            <div class="current-unit-display">
                                <div class="unit-badge">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>{{ current_unit.name }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثاني: أزرار التنقل -->
                    <div class="control-row navigation-controls">
                        <a href="{% url 'daily_interventions' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right"></i>
                            التدخلات اليومية
                        </a>

                        <button class="btn btn-outline-success" onclick="exportAllToExcel()" title="تصدير جميع البيانات">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>

                        <button class="btn btn-outline-danger" onclick="exportAllToPDF()" title="تصدير تقرير شامل">
                            <i class="fas fa-file-pdf"></i>
                            تقرير PDF
                        </button>
                    </div>
                </div>
            </div>

            <!-- أزرار أنواع التدخلات -->
            <div class="navigation-actions">
                <button class="action-btn medical-btn active" onclick="showInterventionType('medical')" id="medical-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-ambulance"></i>
                        <h3>إجلاء صحي</h3>
                        <span class="count-badge" id="medical-count">0</span>
                    </div>
                </button>

                <button class="action-btn accident-btn" onclick="showInterventionType('accident')" id="accident-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-car-crash"></i>
                        <h3>حادث مرور</h3>
                        <span class="count-badge" id="accident-count">0</span>
                    </div>
                </button>

                <button class="action-btn fire-btn" onclick="showInterventionType('fire')" id="fire-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-fire"></i>
                        <h3>حرائق البنايات</h3>
                        <span class="count-badge" id="fire-count">0</span>
                    </div>
                </button>

                <button class="action-btn crop-btn" onclick="showInterventionType('crop')" id="crop-btn">
                    <div class="btn-content-inline">
                        <i class="fas fa-seedling"></i>
                        <h3>حرائق المحاصيل</h3>
                        <span class="count-badge" id="crop-count">0</span>
                    </div>
                </button>
            </div>

            <!-- حالة التحميل -->
            <div id="loading-state" class="loading-state" style="display: none;">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>جاري تحميل البيانات...</p>
                </div>
            </div>

            <!-- جدول الإجلاء الصحي -->
            <div id="medical-table" class="intervention-table main-table-section active">
                <div class="table-header">
                    <h2><i class="fas fa-ambulance"></i> جدول الإجلاء الصحي</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-success" onclick="exportTableToExcel('medical')">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="unified-table" id="medical-data-table">
                        <thead>
                            <tr>
                                <th>معرف التدخل</th>
                                <th>توقيت الخروج</th>
                                <th>نوع التدخل</th>
                                <th>مكان التدخل</th>
                                <th>الوسائل المرسلة</th>
                                <th>الجهة المتصلة</th>
                                <th>نوع الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>اسم المتصل</th>
                                <th>ملاحظة إضافية</th>
                                <th>ساعة الوصول</th>
                                <th>موقع الحادث</th>
                                <th>ملاحظة عن الخسائر المادية</th>
                                <th>نوع الإجلاء</th>
                                <th>طبيعة التدخل</th>
                                <th>طلب الدعم</th>
                                <th>ساعة نهاية التدخل</th>
                                <th>أسماء المسعفين</th>
                                <th>أعمار المسعفين</th>
                                <th>جنس المسعفين</th>
                                <th>عدد الوفيات</th>
                                <th>أسماء الوفيات</th>
                                <th>أعمار الوفيات</th>
                                <th>جنس الوفيات</th>
                                <th>ملاحظات ختامية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="medical-table-body">
                            <tr>
                                <td colspan="27" class="text-center">لا توجد بيانات متاحة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- جدول حوادث المرور -->
            <div id="accident-table" class="intervention-table main-table-section" style="display: none;">
                <div class="table-header">
                    <h2><i class="fas fa-car-crash"></i> جدول حوادث المرور</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-success" onclick="exportTableToExcel('accident')">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="unified-table" id="accident-data-table">
                        <thead>
                            <tr>
                                <th>معرف التدخل</th>
                                <th>توقيت الخروج</th>
                                <th>نوع التدخل</th>
                                <th>مكان التدخل</th>
                                <th>الوسائل المرسلة</th>
                                <th>الجهة المتصلة</th>
                                <th>نوع الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>اسم المتصل</th>
                                <th>ملاحظة إضافية</th>
                                <th>ساعة الوصول</th>
                                <th>ملاحظة عن الخسائر المادية</th>
                                <th>نوع الحادث</th>
                                <th>طبيعة الحادث</th>
                                <th>طلب الدعم</th>
                                <th>نوع الطريق</th>
                                <th>السيارات مزودة</th>
                                <th>عدد الضحايا</th>
                                <th>أسماء الضحايا</th>
                                <th>أعمار الضحايا</th>
                                <th>جنس الضحايا</th>
                                <th>الحالة (سائق/راكب/مشاة)</th>
                                <th>عدد الوفيات</th>
                                <th>أسماء الوفيات</th>
                                <th>أعمار الوفيات</th>
                                <th>جنس الوفيات</th>
                                <th>الخسائر المادية</th>
                                <th>عدد التدخلات</th>
                                <th>ملاحظات ختامية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="accident-table-body">
                            <tr>
                                <td colspan="31" class="text-center">لا توجد بيانات متاحة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- جدول حرائق البنايات -->
            <div id="fire-table" class="intervention-table main-table-section" style="display: none;">
                <div class="table-header">
                    <h2><i class="fas fa-fire"></i> جدول حرائق البنايات</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-success" onclick="exportTableToExcel('fire')">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="unified-table" id="fire-data-table">
                        <thead>
                            <tr>
                                <th>معرف التدخل</th>
                                <th>توقيت الخروج</th>
                                <th>نوع التدخل</th>
                                <th>مكان التدخل</th>
                                <th>الوسائل المرسلة</th>
                                <th>الجهة المتصلة</th>
                                <th>نوع الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>اسم المتصل</th>
                                <th>ملاحظة إضافية</th>
                                <th>ساعة الوصول</th>
                                <th>ملاحظة عن الخسائر المادية</th>
                                <th>طبيعة الحريق</th>
                                <th>الموقع</th>
                                <th>طابق معين</th>
                                <th>غرفة محددة</th>
                                <th>عدد نقاط الاشتعال</th>
                                <th>جهة الرياح</th>
                                <th>سرعة الرياح (كم/سا)</th>
                                <th>تهديد السكان</th>
                                <th>هل تم إجلاء السكان؟</th>
                                <th>ماهية المساعدات المقدمة للسكان</th>
                                <th>طلب الدعم</th>
                                <th>ساعة نهاية التدخل</th>
                                <th>مدة التدخل الإجمالية</th>
                                <th>عدد الضحايا</th>
                                <th>أسماء الضحايا</th>
                                <th>أعمار الضحايا</th>
                                <th>جنس الضحايا</th>
                                <th>عدد الوفيات</th>
                                <th>أسماء الوفيات</th>
                                <th>أعمار الوفيات</th>
                                <th>جنس الوفيات</th>
                                <th>عدد التدخلات</th>
                                <th>عدد العائلات المتضررة</th>
                                <th>عدد الأعوان المتدخلين</th>
                                <th>وصف الخسائر</th>
                                <th>وصف الأملاك المنقذة</th>
                                <th>ملاحظات ختامية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="fire-table-body">
                            <tr>
                                <td colspan="41" class="text-center">لا توجد بيانات متاحة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- جدول حرائق المحاصيل -->
            <div id="crop-table" class="intervention-table main-table-section" style="display: none;">
                <div class="table-header">
                    <h2><i class="fas fa-seedling"></i> جدول حرائق المحاصيل الزراعية</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-success" onclick="exportTableToExcel('crop')">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="unified-table" id="crop-data-table">
                        <thead>
                            <tr>
                                <th>معرف التدخل</th>
                                <th>توقيت الخروج</th>
                                <th>نوع التدخل</th>
                                <th>مكان التدخل</th>
                                <th>الوسائل المرسلة</th>
                                <th>الجهة المتصلة</th>
                                <th>نوع الاتصال</th>
                                <th>رقم الهاتف</th>
                                <th>اسم المتصل</th>
                                <th>ملاحظة إضافية</th>
                                <th>ساعة الوصول</th>
                                <th>ملاحظة عن الخسائر المادية</th>
                                <th>نوع المحصول المحترق</th>
                                <th>عدد البؤر (الموقد)</th>
                                <th>اتجاه الرياح</th>
                                <th>سرعة الرياح (كم/سا)</th>
                                <th>تهديد للسكان</th>
                                <th>مكان إجلاء السكان</th>
                                <th>عدد العائلات المتأثرة</th>
                                <th>الجهات الحاضرة</th>
                                <th>طلب الدعم</th>
                                <th>ساعة نهاية التدخل</th>
                                <th>مدة التدخل الإجمالية</th>
                                <th>عدد الضحايا</th>
                                <th>أسماء الضحايا</th>
                                <th>أعمار الضحايا</th>
                                <th>جنس الضحايا</th>
                                <th>عدد الوفيات</th>
                                <th>أسماء الوفيات</th>
                                <th>أعمار الوفيات</th>
                                <th>جنس الوفيات</th>
                                <th>عدد التدخلات</th>
                                <th>قمح واقف (هكتار)</th>
                                <th>حصيدة (هكتار)</th>
                                <th>شعير (هكتار)</th>
                                <th>غابة/أحراش (هكتار)</th>
                                <th>حزم تبن (عدد)</th>
                                <th>أكياس قمح/شعير (عدد)</th>
                                <th>أشجار مثمرة (عدد)</th>
                                <th>خلايا نحل (عدد)</th>
                                <th>مساحة منقذة (هكتار)</th>
                                <th>حزم التبن المنقذة (عدد)</th>
                                <th>ممتلكات أو آلات تم إنقاذها</th>
                                <th>ملاحظات ختامية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="crop-table-body">
                            <tr>
                                <td colspan="46" class="text-center">لا توجد بيانات متاحة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- تضمين JavaScript -->
    <script src="{% static 'js/sidebar.js' %}"></script>
    <!-- مكتبة تصدير Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        // متغيرات عامة
        let currentInterventionType = 'medical';
        let currentDate = new Date().toISOString().split('T')[0];
        let highlightInterventionId = null;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تعيين التاريخ الحالي
            const dateSelect = document.getElementById('dateSelect');
            if (!dateSelect.value) {
                dateSelect.value = currentDate;
            }

            // فحص معرف التدخل في URL
            const urlParams = new URLSearchParams(window.location.search);
            const interventionId = urlParams.get('id');
            if (interventionId) {
                highlightInterventionId = parseInt(interventionId);
                // إضافة رسالة تنبيه
                showInterventionHighlight(interventionId);
            }

            // تحميل البيانات الأولية
            loadInterventionData();

            // تحميل العدادات لجميع الأنواع
            loadAllCounts();
        });

        // دالة إظهار نوع التدخل
        function showInterventionType(type) {
            // إخفاء جميع الجداول
            document.querySelectorAll('.intervention-table').forEach(table => {
                table.style.display = 'none';
                table.classList.remove('active');
            });

            // إظهار الجدول المطلوب
            const targetTable = document.getElementById(`${type}-table`);
            if (targetTable) {
                targetTable.style.display = 'block';
                targetTable.classList.add('active');
            }

            // تحديث الأزرار النشطة
            updateActiveButton(type);

            // تحديث المتغير الحالي
            currentInterventionType = type;

            // تحميل البيانات
            loadInterventionData(type);
        }

        // دالة تحديث الزر النشط
        function updateActiveButton(type) {
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إضافة الفئة النشطة للزر المحدد
            const activeBtn = document.getElementById(`${type}-btn`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
        }

        // دالة تحميل البيانات
        async function loadInterventionData(type = null) {
            const targetType = type || currentInterventionType;
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            // إظهار حالة التحميل
            showLoadingState(true);

            try {
                let url = `/api/interventions/get-by-type/?type=${targetType}&date=${selectedDate}`;

                // إضافة معرف التدخل إذا كان موجوداً
                if (highlightInterventionId && !type) {
                    url += `&intervention_id=${highlightInterventionId}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    // إذا كان هناك تدخل محدد، تحديث النوع المناسب
                    if (highlightInterventionId && !type && data.intervention_type) {
                        const typeMapping = {
                            'إجلاء صحي': 'medical',
                            'حادث مرور': 'accident',
                            'حريق بناية': 'fire',
                            'حريق محاصيل': 'crop'
                        };
                        const detectedType = typeMapping[data.intervention_type];
                        if (detectedType && detectedType !== targetType) {
                            showInterventionType(detectedType);
                            return;
                        }
                    }

                    populateTable(targetType, data.interventions);
                    updateCountBadge(targetType, data.count);
                } else {
                    console.error('خطأ في تحميل البيانات:', data.error);
                    showErrorMessage(data.error);
                }
            } catch (error) {
                console.error('خطأ في الشبكة:', error);
                showErrorMessage('حدث خطأ في الاتصال بالخادم');
            } finally {
                showLoadingState(false);
            }
        }

        // دالة ملء الجدول بالبيانات
        function populateTable(type, interventions) {
            const tableBody = document.getElementById(`${type}-table-body`);
            if (!tableBody) return;

            // تحديد عدد الأعمدة حسب نوع الجدول
            const columnCounts = {
                'medical': 27,
                'accident': 31,
                'fire': 41,
                'crop': 46
            };

            if (interventions.length === 0) {
                const colCount = columnCounts[type] || 25;
                tableBody.innerHTML = `<tr><td colspan="${colCount}" class="text-center">لا توجد بيانات متاحة</td></tr>`;
                return;
            }

            let html = '';
            interventions.forEach(intervention => {
                html += createTableRow(type, intervention);
            });

            tableBody.innerHTML = html;

            // تسليط الضوء على التدخل المحدد إذا كان موجوداً
            if (highlightInterventionId) {
                highlightInterventionRow(highlightInterventionId);
            }
        }

        // دوال تنسيق البيانات التفصيلية
        function formatDetailedList(dataArray, fields) {
            if (!dataArray || !Array.isArray(dataArray) || dataArray.length === 0) {
                return '-';
            }

            return dataArray.map(item => {
                return fields.map(field => item[field] || '-').join(' - ');
            }).join('<br>');
        }

        function formatSimpleList(dataArray) {
            if (!dataArray || !Array.isArray(dataArray) || dataArray.length === 0) {
                return '-';
            }
            return dataArray.join('<br>');
        }

        function formatPersonDetails(personsData) {
            // معالجة أفضل للبيانات
            if (!personsData) {
                return { names: '-', ages: '-', genders: '-' };
            }

            // إذا كانت البيانات نص JSON، قم بتحويلها
            if (typeof personsData === 'string') {
                try {
                    personsData = JSON.parse(personsData);
                } catch (e) {
                    return { names: '-', ages: '-', genders: '-' };
                }
            }

            // التأكد من أنها مصفوفة
            if (!Array.isArray(personsData) || personsData.length === 0) {
                return { names: '-', ages: '-', genders: '-' };
            }

            return {
                names: personsData.map(p => p.name || '-').join('<br>'),
                ages: personsData.map(p => p.age || '-').join('<br>'),
                genders: personsData.map(p => translateValue(p.gender) || '-').join('<br>')
            };
        }

        // دالة تحويل القيم من الإنجليزية إلى العربية
        function translateValue(value) {
            const translations = {
                // طبيعة التدخل الطبي
                'respiratory': 'مشاكل تنفسية',
                'cardiac': 'مشاكل قلبية',
                'trauma': 'إصابات خارجية',
                'burns': 'حروق',
                'poisoning': 'تسمم',
                'other': 'أخرى',

                // نوع التدخل (القيم الأساسية من النموذج)
                'medical': 'إجلاء صحي',
                'accident': 'حادث مرور',
                'fire': 'حريق',
                'agricultural-fire': 'حريق محاصيل زراعية',
                'building-fire': 'حرائق البنايات والمؤسسات',
                'rescue': 'إنقاذ',
                'emergency': 'طوارئ',

                // أنواع التدخل البديلة
                'medical_evacuation': 'إجلاء صحي',
                'traffic_accident': 'حوادث المرور',
                'building_fire': 'حريق البنايات',
                'agricultural_fire': 'حريق المحاصيل',

                // أنواع التدخل بالإنجليزية (القيم المحتملة في قاعدة البيانات)
                'Medical Evacuation': 'إجلاء صحي',
                'Traffic Accident': 'حوادث المرور',
                'Building Fire': 'حريق البنايات',
                'Agricultural Fire': 'حريق المحاصيل',
                'Rescue': 'إنقاذ',
                'Emergency': 'طوارئ',

                // أنواع التدخل بالعربية والإنجليزية المختلطة
                'إجلاء صحي': 'إجلاء صحي',
                'حوادث المرور': 'حوادث المرور',
                'حريق البنايات': 'حريق البنايات',
                'حريق المحاصيل': 'حريق المحاصيل',

                // الجهة المتصلة
                'citizen': 'مواطن',
                'police': 'الشرطة',
                'gendarmerie': 'الدرك الوطني',
                'hospital': 'مستشفى',
                'clinic': 'عيادة',
                'pharmacy': 'صيدلية',
                'civil_protection': 'الحماية المدنية',
                'fire_department': 'مصلحة الإطفاء',
                'municipality': 'البلدية',
                'wali': 'الولاية',
                'other_authority': 'جهة أخرى',

                // نوع الاتصال
                'phone': 'هاتف',
                'mobile': 'هاتف نقال',
                'radio': 'لاسلكي',
                'direct': 'مباشر',
                'fax': 'فاكس',
                'internet': 'إنترنت',

                // الجنس
                'male': 'ذكر',
                'female': 'أنثى',
                'M': 'ذكر',
                'F': 'أنثى',

                // موقع الحادث
                'inside-house': 'داخل المنزل',
                'outside-house': 'خارج المنزل',
                'public-place': 'مكان عام',
                'workplace': 'مكان العمل',
                'road': 'على الطريق',

                // طلب الدعم
                'no': 'شكراً، الوضع تحت السيطرة',
                'vehicle': 'نعم وسيلة إضافية',
                'neighboring-unit': 'نعم وحدة مجاورة',
                'specialized-team': 'نعم فريق متخصص',
                'major-disaster': 'تصعيد إلى كارثة كبرى',
                'none': 'لا يوجد',
                'ambulance': 'سيارة إسعاف إضافية',
                'medical-team': 'فريق طبي متخصص',
                'helicopter': 'إجلاء جوي',
                'under_control': 'شكراً، الوضع تحت السيطرة',
                'additional_vehicle': 'نعم وسيلة إضافية',
                'neighboring_unit': 'نعم وحدة مجاورة',
                'specialized_team': 'نعم فريق متخصص',

                // طبيعة الحادث (حوادث المرور)
                'collision': 'تصادم',
                'rollover': 'انقلاب',
                'hit-pedestrian': 'دهس مشاة',
                'single-vehicle': 'مركبة واحدة',
                'multi-vehicle': 'عدة مركبات',
                'head-on': 'تصادم أمامي',
                'rear-end': 'تصادم خلفي',
                'side-impact': 'تصادم جانبي',
                'other-accident': 'أخرى',
                // قيم إضافية قد يدخلها المستخدمون
                'سيارة بشاحنة': 'سيارة بشاحنة',
                'تصادم سيارات': 'تصادم سيارات',
                'انقلاب مركبة': 'انقلاب مركبة',
                'دهس مشاة': 'دهس مشاة',

                // نوع الطريق
                'highway': 'طريق سريع',
                'national': 'طريق وطني',
                'regional': 'طريق ولائي',
                'local': 'طريق محلي',
                'urban': 'طريق حضري',
                'rural': 'طريق ريفي',
                'intersection': 'تقاطع',
                'roundabout': 'دوار',
                'bridge': 'جسر',
                'tunnel': 'نفق',
                'wilaya': 'طريق ولائي',
                'municipal': 'طريق بلدي',

                // حالة الضحايا (سائق/راكب/مشاة)
                'driver': 'سائق',
                'passenger': 'راكب',
                'pedestrian': 'مشاة',
                'other': 'أخرى',

                // أنواع الوقود للسيارات
                'gasoline': 'الوقود (البنزين)',
                'diesel': 'الديزل',
                'gas': 'الغاز',
                'electric': 'الكهرباء',
                'hybrid': 'هجين (وقود + كهرباء)',
                'gas-gasoline': 'غاز + بنزين',
                'unknown': 'غير محدد',

                // أنواع المحاصيل المحترقة
                'wheat': 'قمح',
                'barley': 'شعير',
                'corn': 'ذرة',
                'rice': 'أرز',
                'oats': 'شوفان',
                'straw': 'تبن',
                'hay': 'علف',
                'forest': 'غابة',
                'orchard': 'بستان',
                'mixed': 'مختلط',
                // أنواع المحاصيل المحترقة - القيم الجديدة
                'standing_wheat': 'قمح واقف',
                'harvest': 'حصيدة',
                'straw_bales': 'حزم تبن',
                'forest_bushes': 'غابة / أحراش',
                'grain_bags': 'أكياس شعير / قمح',
                'fruit_trees': 'أشجار مثمرة',
                'beehives': 'خلايا نحل',

                // اتجاه الرياح
                'north': 'شمال',
                'south': 'جنوب',
                'east': 'شرق',
                'west': 'غرب',
                'northeast': 'شمال شرق',
                'northwest': 'شمال غرب',
                'southeast': 'جنوب شرق',
                'southwest': 'جنوب غرب',
                // اتجاه الرياح - القيم العربية
                'شمالي': 'شمالي',
                'جنوبي': 'جنوبي',
                'شرقي': 'شرقي',
                'غربي': 'غربي',
                'شمالي شرقي': 'شمالي شرقي',
                'شمالي غربي': 'شمالي غربي',
                'جنوبي شرقي': 'جنوبي شرقي',
                'جنوبي غربي': 'جنوبي غربي',

                // طلب الدعم
                'none': 'لا يوجد',
                'police': 'الشرطة',
                'gendarmerie': 'الدرك',
                'army': 'الجيش',
                'forest_protection': 'حماية الغابات',
                'municipality': 'البلدية',
                'other_units': 'وحدات أخرى'
            };

            return translations[value] || value || '-';
        }

        // دالة إنشاء صف الجدول
        function createTableRow(type, intervention) {
            const statusClass = getStatusClass(intervention.status);
            const statusText = getStatusText(intervention.status);

            let row = `
                <tr>
                    <td>${intervention.id}</td>
                    <td>${intervention.time}</td>
                    <td>${translateValue(intervention.intervention_type)}</td>
                    <td>${intervention.location}</td>
                    <td>${intervention.vehicles_sent || '-'}</td>
                    <td>${translateValue(intervention.caller_entity)}</td>
                    <td>${translateValue(intervention.contact_type)}</td>
                    <td>${intervention.phone_number || '-'}</td>
                    <td>${intervention.caller_name || '-'}</td>
                    <td>${intervention.additional_notes || '-'}</td>
                    <td>${intervention.arrival_time || '-'}</td>
            `;

            // إضافة الأعمدة الخاصة بكل نوع تدخل
            if (type === 'medical') {
                // تنسيق بيانات المسعفين والوفيات من النموذج المتخصص
                const medicalDetail = intervention.medical_detail || {};
                const injuredDetails = formatPersonDetails(medicalDetail.injured_details || intervention.injured_details || []);
                const fatalityDetails = formatPersonDetails(medicalDetail.fatalities_details || intervention.fatality_details || []);

                row += `
                    <td>${translateValue(intervention.incident_location)}</td> <!-- موقع الحادث -->
                    <td>${intervention.material_damage || intervention.material_damage_notes || '-'}</td> <!-- ملاحظة عن الخسائر المادية -->
                    <td>${intervention.intervention_subtype || intervention.evacuation_type || '-'}</td> <!-- نوع الإجلاء -->
                    <td>${translateValue(medicalDetail.patient_condition || intervention.patient_condition)}</td> <!-- طبيعة التدخل -->
                    <td>${translateValue(medicalDetail.support_request || intervention.support_request)}</td> <!-- طلب الدعم -->
                    <td>${intervention.end_time || '-'}</td> <!-- ساعة نهاية التدخل -->
                    <td>${injuredDetails.names}</td> <!-- أسماء المسعفين -->
                    <td>${injuredDetails.ages}</td> <!-- أعمار المسعفين -->
                    <td>${injuredDetails.genders}</td> <!-- جنس المسعفين -->
                    <td>${intervention.final_deaths_count || intervention.deaths_count || 0}</td> <!-- عدد الوفيات -->
                    <td>${fatalityDetails.names}</td> <!-- أسماء الوفيات -->
                    <td>${fatalityDetails.ages}</td> <!-- أعمار الوفيات -->
                    <td>${fatalityDetails.genders}</td> <!-- جنس الوفيات -->
                    <td>${medicalDetail.final_notes || intervention.final_notes || intervention.initial_notes || '-'}</td> <!-- ملاحظات ختامية -->
                `;
            } else if (type === 'accident') {
                // تنسيق بيانات الضحايا والوفيات - استخدام نفس منطق الإجلاء الصحي
                const accidentDetail = intervention.traffic_accident_detail || {};

                // استخدام مصدر واحد فقط لتجنب التكرار - نفس طريقة "السيارات مزودة"
                const victimsData = intervention.victims_details || [];
                const fatalitiesData = intervention.fatalities_details || [];

                const victimDetails = formatPersonDetails(victimsData);
                const fatalityDetails = formatPersonDetails(fatalitiesData);
                const victimStatuses = formatSimpleList(victimsData.map(v => translateValue(v.status) || '-'));
                const involvedVehicles = formatSimpleList(intervention.involved_vehicles || []);

                row += `
                    <td>${intervention.material_damage || intervention.material_damage_notes || '-'}</td> <!-- ملاحظة عن الخسائر المادية -->
                    <td>${accidentDetail.accident_type || intervention.intervention_subtype || intervention.accident_type || '-'}</td> <!-- نوع الحادث -->
                    <td>${translateValue(accidentDetail.accident_nature || intervention.accident_nature || '-')}</td> <!-- طبيعة الحادث -->
                    <td>${translateValue(accidentDetail.support_request || intervention.support_request || '-')}</td> <!-- طلب الدعم -->
                    <td>${translateValue(accidentDetail.road_type || intervention.road_type || '-')}</td> <!-- نوع الطريق -->
                    <td>${translateValue(accidentDetail.vehicle_fuel_type || intervention.vehicle_fuel_type || '-')}</td> <!-- السيارات مزودة -->
                    <td>${intervention.final_injured_count || intervention.injured_count || 0}</td> <!-- عدد الضحايا -->
                    <td>${victimDetails.names}</td> <!-- أسماء الضحايا -->
                    <td>${victimDetails.ages}</td> <!-- أعمار الضحايا -->
                    <td>${victimDetails.genders}</td> <!-- جنس الضحايا -->
                    <td>${victimStatuses}</td> <!-- الحالة (سائق/راكب/مشاة) -->
                    <td>${intervention.final_deaths_count || intervention.deaths_count || 0}</td> <!-- عدد الوفيات -->
                    <td>${fatalityDetails.names}</td> <!-- أسماء الوفيات -->
                    <td>${fatalityDetails.ages}</td> <!-- أعمار الوفيات -->
                    <td>${fatalityDetails.genders}</td> <!-- جنس الوفيات -->
                    <td>${intervention.material_damage || intervention.material_damage_notes || '-'}</td> <!-- الخسائر المادية -->
                    <td>${intervention.total_interventions || '-'}</td> <!-- عدد التدخلات -->
                    <td>${accidentDetail.final_notes || intervention.final_notes || intervention.initial_notes || '-'}</td> <!-- ملاحظات ختامية -->
                `;
            } else if (type === 'fire') {
                // تنسيق بيانات المسعفين والوفيات - استخدام مصدر واحد فقط
                const injuredDetails = formatPersonDetails(intervention.victims_details || []);
                const fatalityDetails = formatPersonDetails(intervention.fatalities_details || []);



                row += `
                    <td>${intervention.material_damage_notes || intervention.material_damage || '-'}</td> <!-- ملاحظة عن الخسائر المادية -->
                    <td>${intervention.fire_type || '-'}</td> <!-- طبيعة الحريق -->
                    <td>${intervention.fire_location || intervention.location || '-'}</td> <!-- الموقع -->
                    <td>${intervention.specific_floor || '-'}</td> <!-- طابق معين -->
                    <td>${intervention.specific_room || '-'}</td> <!-- غرفة محددة -->
                    <td>${intervention.fire_points_count || '-'}</td> <!-- عدد نقاط الاشتعال -->
                    <td>${translateValue(intervention.wind_direction || '-')}</td> <!-- جهة الرياح -->
                    <td>${intervention.wind_speed || '-'}</td> <!-- سرعة الرياح (كم/سا) -->
                    <td>${intervention.population_threat === 'نعم' ? 'نعم' : 'لا'}</td> <!-- تهديد السكان -->
                    <td>${intervention.population_evacuated === 'نعم' ? 'نعم' : 'لا'}</td> <!-- هل تم إجلاء السكان؟ -->
                    <td>${intervention.assistance_provided || '-'}</td> <!-- ماهية المساعدات المقدمة للسكان -->
                    <td>${translateValue(intervention.support_request || '-')}</td> <!-- طلب الدعم -->
                    <td>${intervention.end_time || '-'}</td> <!-- ساعة نهاية التدخل -->
                    <td>${intervention.total_duration || '-'}</td> <!-- مدة التدخل الإجمالية -->
                    <td>${intervention.injured_count || 0}</td> <!-- عدد الضحايا -->
                    <td>${injuredDetails.names}</td> <!-- أسماء الضحايا -->
                    <td>${injuredDetails.ages}</td> <!-- أعمار الضحايا -->
                    <td>${injuredDetails.genders}</td> <!-- جنس الضحايا -->
                    <td>${intervention.deaths_count || 0}</td> <!-- عدد الوفيات -->
                    <td>${fatalityDetails.names}</td> <!-- أسماء الوفيات -->
                    <td>${fatalityDetails.ages}</td> <!-- أعمار الوفيات -->
                    <td>${fatalityDetails.genders}</td> <!-- جنس الوفيات -->
                    <td>${intervention.total_interventions || '-'}</td> <!-- عدد التدخلات -->
                    <td>${intervention.affected_families || '-'}</td> <!-- عدد العائلات المتضررة -->
                    <td>${intervention.intervening_agents || '-'}</td> <!-- عدد الأعوان المتدخلين -->
                    <td>${intervention.damages_description || '-'}</td> <!-- وصف الخسائر -->
                    <td>${intervention.saved_properties || '-'}</td> <!-- وصف الأملاك المنقذة -->
                    <td>${intervention.final_notes || intervention.initial_notes || '-'}</td> <!-- ملاحظات ختامية -->
                `;
            } else if (type === 'crop') {
                // تم إعادة بناء عرض حريق المحاصيل من الصفر بنفس طريقة "السيارات مزودة"

                // تنسيق بيانات الضحايا والوفيات
                const injuredDetails = formatPersonDetails(intervention.victims_details || []);
                const fatalityDetails = formatPersonDetails(intervention.fatalities_details || []);

                row += `
                    <td>${intervention.material_damage || intervention.material_damage_notes || '-'}</td> <!-- ملاحظة عن الخسائر المادية -->
                    <td>${translateValue(intervention.fire_type || '-')}</td> <!-- نوع المحصول المحترق -->
                    <td>${intervention.fire_points_count || '-'}</td> <!-- عدد البؤر (الموقد) -->
                    <td>${translateValue(intervention.wind_direction || '-')}</td> <!-- اتجاه الرياح -->
                    <td>${intervention.wind_speed || '-'}</td> <!-- سرعة الرياح (كم/سا) -->
                    <td>${intervention.population_threat === 'نعم' ? 'نعم' : 'لا'}</td> <!-- تهديد للسكان -->
                    <td>${intervention.evacuation_location || '-'}</td> <!-- مكان إجلاء السكان -->
                    <td>${intervention.affected_families || '-'}</td> <!-- عدد العائلات المتأثرة -->
                    <td>${intervention.present_entities ? (Array.isArray(intervention.present_entities) ? intervention.present_entities.join(', ') : intervention.present_entities) : '-'}</td> <!-- الجهات الحاضرة -->
                    <td>${translateValue(intervention.support_request || '-')}</td> <!-- طلب الدعم -->
                    <td>${intervention.end_time || '-'}</td> <!-- ساعة نهاية التدخل -->
                    <td>${intervention.total_duration || '-'}</td> <!-- مدة التدخل الإجمالية -->
                    <td>${intervention.injured_count || 0}</td> <!-- عدد الضحايا -->
                    <td>${injuredDetails.names}</td> <!-- أسماء الضحايا -->
                    <td>${injuredDetails.ages}</td> <!-- أعمار الضحايا -->
                    <td>${injuredDetails.genders}</td> <!-- جنس الضحايا -->
                    <td>${intervention.deaths_count || 0}</td> <!-- عدد الوفيات -->
                    <td>${fatalityDetails.names}</td> <!-- أسماء الوفيات -->
                    <td>${fatalityDetails.ages}</td> <!-- أعمار الوفيات -->
                    <td>${fatalityDetails.genders}</td> <!-- جنس الوفيات -->
                    <td>${intervention.total_interventions || '-'}</td> <!-- عدد التدخلات -->
                    <td>${intervention.standing_wheat_area || 0}</td> <!-- قمح واقف (هكتار) -->
                    <td>${intervention.harvest_area || 0}</td> <!-- حصيدة (هكتار) -->
                    <td>${intervention.barley_area || 0}</td> <!-- شعير (هكتار) -->
                    <td>${intervention.forest_area || 0}</td> <!-- غابة/أحراش (هكتار) -->
                    <td>${intervention.straw_bales_count || 0}</td> <!-- حزم تبن (عدد) -->
                    <td>${intervention.grain_bags_count || 0}</td> <!-- أكياس قمح/شعير (عدد) -->
                    <td>${intervention.fruit_trees_count || 0}</td> <!-- أشجار مثمرة (عدد) -->
                    <td>${intervention.beehives_count || 0}</td> <!-- خلايا نحل (عدد) -->
                    <td>${intervention.saved_area || 0}</td> <!-- مساحة منقذة (هكتار) -->
                    <td>${intervention.saved_straw_bales || 0}</td> <!-- حزم التبن المنقذة (عدد) -->
                    <td>${intervention.saved_equipment || intervention.saved_properties || '-'}</td> <!-- ممتلكات أو آلات تم إنقاذها -->
                    <td>${intervention.final_notes || intervention.initial_notes || '-'}</td> <!-- ملاحظات ختامية -->
                `;
            }

            // إضافة أزرار الإجراءات حسب حالة التدخل
            let actionButtons = '';

            if (intervention.status === 'initial_report') {
                actionButtons = `
                    <button class="btn btn-sm btn-warning" onclick="continueToReconnaissance(${intervention.id})" title="عملية التعرف">
                        <i class="fas fa-search"></i> عملية التعرف
                    </button>
                `;
            } else if (intervention.status === 'reconnaissance' || intervention.status === 'intervention') {
                actionButtons = `
                    <button class="btn btn-sm btn-success" onclick="continueToCompletion(${intervention.id})" title="إنهاء المهمة">
                        <i class="fas fa-check"></i> انهاء المهمة
                    </button>
                `;
            } else if (intervention.status === 'completed') {
                actionButtons = `
                    <button class="btn btn-sm btn-info" onclick="viewDetails(${intervention.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i> عرض التفاصيل
                    </button>
                `;
            }

            // إضافة زر التعديل دائماً
            actionButtons += `
                <button class="btn btn-sm btn-secondary" onclick="editIntervention(${intervention.id})" title="تعديل في الصفحة الرئيسية">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-success" onclick="exportToPDF(${intervention.id})" title="تصدير PDF">
                    <i class="fas fa-file-pdf"></i>
                </button>
            `;

            row += `
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td class="actions-cell">
                        ${actionButtons}
                    </td>
                </tr>
            `;

            return row;
        }

        // دالة تحديث عداد الشارة
        function updateCountBadge(type, count) {
            const badge = document.getElementById(`${type}-count`);
            if (badge) {
                badge.textContent = count;
            }
        }

        // دالة إظهار/إخفاء حالة التحميل
        function showLoadingState(show) {
            const loadingState = document.getElementById('loading-state');
            if (loadingState) {
                loadingState.style.display = show ? 'block' : 'none';
            }
        }

        // دالة إظهار رسالة خطأ
        function showErrorMessage(message) {
            alert('خطأ: ' + message);
        }

        // دالة الحصول على فئة الحالة
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'badge-warning';
                case 'in_progress': return 'badge-info';
                case 'completed': return 'badge-success';
                default: return 'badge-secondary';
            }
        }

        // دالة الحصول على نص الحالة
        function getStatusText(status) {
            switch(status) {
                case 'pending': return 'قيد التعرف';
                case 'in_progress': return 'عملية تدخل';
                case 'completed': return 'منتهية';
                default: return 'غير محدد';
            }
        }

        // دوال الإجراءات
        function editIntervention(id) {
            // توجيه المستخدم إلى صفحة التدخلات اليومية مع تحديد التدخل
            const currentDate = document.getElementById('dateSelect').value || new Date().toISOString().split('T')[0];
            window.location.href = `/coordination-center/daily-interventions/?date=${currentDate}&highlight=${id}`;
        }

        function viewDetails(id) {
            // فتح صفحة تفاصيل التدخل في نافذة جديدة
            window.open(`/coordination-center/intervention-details/?id=${id}`, '_blank');
        }

        // دالة للانتقال إلى عملية التعرف
        function continueToReconnaissance(id) {
            // توجيه المستخدم إلى صفحة التدخلات اليومية لإكمال عملية التعرف
            const currentDate = document.getElementById('dateSelect').value || new Date().toISOString().split('T')[0];
            window.location.href = `/coordination-center/daily-interventions/?date=${currentDate}&highlight=${id}&action=reconnaissance`;
        }

        // دالة للانتقال إلى إنهاء المهمة
        function continueToCompletion(id) {
            // توجيه المستخدم إلى صفحة التدخلات اليومية لإكمال إنهاء المهمة
            const currentDate = document.getElementById('dateSelect').value || new Date().toISOString().split('T')[0];
            window.location.href = `/coordination-center/daily-interventions/?date=${currentDate}&highlight=${id}&action=completion`;
        }

        function exportToPDF(id) {
            // البحث عن صف التدخل في الجدول الحالي
            const currentTable = document.querySelector('.intervention-table.active tbody');
            if (!currentTable) {
                alert('لا يمكن العثور على الجدول النشط');
                return;
            }

            const rows = currentTable.querySelectorAll('tr');
            let targetRow = null;

            rows.forEach(row => {
                const firstCell = row.querySelector('td');
                if (firstCell && firstCell.textContent.trim() === id.toString()) {
                    targetRow = row;
                }
            });

            if (!targetRow) {
                alert('لا يمكن العثور على التدخل المحدد');
                return;
            }

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير التدخل رقم ${id}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .intervention-details { margin: 20px 0; }
                        .intervention-details h3 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f8f9fa; font-weight: bold; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>المديرية العامة للحماية المدنية</h1>
                        <h2>تقرير التدخل رقم ${id}</h2>
                        <p>التاريخ: ${selectedDate}</p>
                    </div>
                    <div class="intervention-details">
                        <h3>تفاصيل التدخل</h3>
                        <p><strong>معرف التدخل:</strong> ${id}</p>
                        <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();

            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 250);
        }

        // دوال التصدير
        function exportTableToExcel(type) {
            const table = document.getElementById(`${type}-data-table`);
            if (!table) {
                alert('لا يمكن العثور على الجدول');
                return;
            }

            // الحصول على اسم النوع بالعربية
            const typeNames = {
                'medical': 'الإجلاء الصحي',
                'accident': 'حوادث المرور',
                'fire': 'حرائق البنايات',
                'crop': 'حرائق المحاصيل'
            };

            const typeName = typeNames[type] || type;
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            // إنشاء workbook جديد
            const wb = XLSX.utils.book_new();

            // تحويل الجدول إلى worksheet
            const ws = XLSX.utils.table_to_sheet(table, {
                raw: false,
                dateNF: 'dd/mm/yyyy'
            });

            // إضافة الـ worksheet إلى الـ workbook
            XLSX.utils.book_append_sheet(wb, ws, typeName);

            // تصدير الملف
            const fileName = `${typeName}_${selectedDate}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        function exportAllToExcel() {
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            // إنشاء workbook جديد
            const wb = XLSX.utils.book_new();

            const types = ['medical', 'accident', 'fire', 'crop'];
            const typeNames = {
                'medical': 'الإجلاء الصحي',
                'accident': 'حوادث المرور',
                'fire': 'حرائق البنايات',
                'crop': 'حرائق المحاصيل'
            };

            types.forEach(type => {
                const table = document.getElementById(`${type}-data-table`);
                if (table) {
                    const ws = XLSX.utils.table_to_sheet(table, {
                        raw: false,
                        dateNF: 'dd/mm/yyyy'
                    });
                    XLSX.utils.book_append_sheet(wb, ws, typeNames[type]);
                }
            });

            // تصدير الملف
            const fileName = `تقرير_التدخلات_الشامل_${selectedDate}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        function exportAllToPDF() {
            // استخدام window.print() لطباعة الصفحة كـ PDF
            const originalTitle = document.title;
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            document.title = `تقرير التدخلات - ${selectedDate}`;

            // إخفاء العناصر غير المرغوب فيها في الطباعة
            const elementsToHide = document.querySelectorAll('.btn, .navigation-actions, .controls-enhanced');
            elementsToHide.forEach(el => el.style.display = 'none');

            // طباعة الصفحة
            window.print();

            // إعادة إظهار العناصر
            elementsToHide.forEach(el => el.style.display = '');
            document.title = originalTitle;
        }

        // دالة تحميل عدادات جميع أنواع التدخلات
        async function loadAllCounts() {
            const dateSelect = document.getElementById('dateSelect');
            const selectedDate = dateSelect.value || currentDate;

            try {
                const response = await fetch(`/api/interventions/get-counts/?date=${selectedDate}`);
                const data = await response.json();

                if (data.success) {
                    // تحديث العدادات في الأزرار
                    updateCountBadge('medical', data.counts.medical || 0);
                    updateCountBadge('accident', data.counts.accident || 0);
                    updateCountBadge('fire', data.counts.fire || 0);
                    updateCountBadge('crop', data.counts.crop || 0);
                } else {
                    console.error('خطأ في تحميل العدادات:', data.error);
                }
            } catch (error) {
                console.error('خطأ في الشبكة:', error);
            }
        }

        // دالة تغيير التاريخ
        function onDateChange() {
            // تحديث التاريخ الحالي
            const dateSelect = document.getElementById('dateSelect');
            currentDate = dateSelect.value;

            // تحديث البيانات والعدادات
            loadInterventionData();
            loadAllCounts();
        }

        // دالة إظهار تنبيه التدخل المحدد
        function showInterventionHighlight(interventionId) {
            // إضافة تنبيه في أعلى الصفحة
            const headerSection = document.querySelector('.header-section');
            if (headerSection) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info intervention-highlight';
                alertDiv.innerHTML = `
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تم الوصول من صفحة التدخلات اليومية</strong>
                            <br>
                            <small>عرض تفاصيل التدخل رقم: ${interventionId}</small>
                        </div>
                        <button type="button" class="btn-close" onclick="closeInterventionHighlight()"></button>
                    </div>
                `;
                headerSection.appendChild(alertDiv);
            }
        }

        // دالة إغلاق تنبيه التدخل
        function closeInterventionHighlight() {
            const alert = document.querySelector('.intervention-highlight');
            if (alert) {
                alert.remove();
            }
            highlightInterventionId = null;
        }

        // دالة تسليط الضوء على صف التدخل المحدد
        function highlightInterventionRow(interventionId) {
            // البحث عن الصف في جميع الجداول
            const tables = ['medical', 'accident', 'fire', 'crop'];
            tables.forEach(tableType => {
                const tableBody = document.getElementById(`${tableType}-table-body`);
                if (tableBody) {
                    const rows = tableBody.querySelectorAll('tr');
                    rows.forEach(row => {
                        const firstCell = row.querySelector('td');
                        if (firstCell && firstCell.textContent.trim() === interventionId.toString()) {
                            row.classList.add('highlighted-intervention');
                            // التمرير إلى الصف
                            setTimeout(() => {
                                row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }, 500);
                        }
                    });
                }
            });
        }
    </script>

    <style>
        /* تنسيقات CSS للصفحة */
        .unified-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .unit-count-icon {
            font-size: 4rem;
            color: #17a2b8;
            margin-bottom: 20px;
        }

        .unit-count-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1d3557;
            margin-bottom: 10px;
        }

        .unit-count-subtitle {
            font-size: 1.2rem;
            color: #457b9d;
            margin-bottom: 20px;
        }

        .system-badge {
            margin-bottom: 30px;
        }

        .system-badge .badge {
            font-size: 1rem;
            padding: 8px 16px;
            border-radius: 20px;
        }

        .controls-enhanced {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .control-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .control-item label {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .enhanced-input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }

        .current-unit-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px 12px;
        }

        .unit-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
            font-weight: 600;
        }

        /* أزرار أنواع التدخلات */
        .navigation-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }

        .action-btn.active {
            border-color: #007bff;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .medical-btn.active {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-color: #c82333;
        }

        .accident-btn.active {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            border-color: #e0a800;
            color: #212529;
        }

        .fire-btn.active {
            background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%);
            border-color: #e8590c;
        }

        .crop-btn.active {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border-color: #1e7e34;
        }

        .btn-content-inline {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .btn-content-inline i {
            font-size: 2.5rem;
        }

        .btn-content-inline h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .count-badge {
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: bold;
            min-width: 24px;
            display: inline-block;
        }

        .action-btn.active .count-badge {
            background: rgba(255, 255, 255, 0.3);
        }

        /* حالة التحميل */
        .loading-state {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .loading-spinner i {
            font-size: 3rem;
            color: #007bff;
            margin-bottom: 15px;
        }

        .loading-spinner p {
            font-size: 1.1rem;
            color: #6c757d;
            margin: 0;
        }

        /* الجداول */
        .intervention-table {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .intervention-table.active {
            opacity: 1;
            transform: translateY(0);
        }

        .main-table-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .table-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h2 {
            margin: 0;
            color: #495057;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
            overflow-x: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .unified-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            min-width: 1200px;
        }

        .unified-table th,
        .unified-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            white-space: nowrap;
            vertical-align: middle;
            font-size: 13px;
        }

        .unified-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .unified-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .actions-cell {
            white-space: nowrap;
        }

        .actions-cell .btn {
            margin: 0 2px;
            padding: 4px 8px;
        }

        /* الشارات */
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-info {
            background-color: #17a2b8;
            color: white;
        }

        .badge-success {
            background-color: #28a745;
            color: white;
        }

        .badge-secondary {
            background-color: #6c757d;
            color: white;
        }

        /* تنسيق التنبيه والتسليط */
        .intervention-highlight {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #b6d4d9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            animation: slideDown 0.3s ease;
        }

        .intervention-highlight .btn-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #0c5460;
            opacity: 0.7;
        }

        .intervention-highlight .btn-close:hover {
            opacity: 1;
        }

        .highlighted-intervention {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
            border: 2px solid #ffc107 !important;
            animation: pulse 2s infinite;
        }

        .highlighted-intervention td {
            font-weight: 600;
            color: #856404;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
            }
        }

        .alert {
            padding: 12px 16px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 6px;
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #b6d4d9;
        }

        .d-flex {
            display: flex !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }

        .me-2 {
            margin-left: 0.5rem !important;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .navigation-actions {
                grid-template-columns: 1fr;
            }

            .control-row {
                flex-direction: column;
                gap: 15px;
            }

            .unified-table {
                min-width: 800px;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .intervention-highlight {
                padding: 10px;
            }

            .intervention-highlight .d-flex {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* أنماط الطباعة */
        @media print {
            body * {
                visibility: hidden;
            }

            .intervention-table.active,
            .intervention-table.active * {
                visibility: visible;
            }

            .intervention-table.active {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }

            .btn, .navigation-actions, .controls-enhanced {
                display: none !important;
            }

            .header-section {
                page-break-after: avoid;
            }

            .unified-table {
                font-size: 10px;
            }

            .unified-table th,
            .unified-table td {
                padding: 4px;
                font-size: 9px;
            }
        }
    </style>
    </script>
</body>
</html>
