


i have created http://127.0.0.1:8000/coordination-center/daily-interventions/ (البلاغ الأولي، عملية التعرف، إنهاء المهمة)، مخصصة لأنواع التدخل التالية:

1. 🚑 إجلاء صحي  
2. 🚗 حادث مرور  
3. 🏢 حرائق البنايات والمؤسسات  
4. 🌾 حرائق المحاصيل الزراعية  

---

## الصفحة الرئيسية

- جدول واحد يعرض **جميع التدخلات**، مع الأعمدة التالية:

| معرف التدخل | توقيت الخروج | نوع التدخل | الجهة المتصلة | نوع الاتصال | رقم الهاتف | الوسائل المرسلة | موقع الحادث | الحالة | الإجراءات |
|--------------|----------------|--------------|----------------|----------------|--------------|--------------------|----------------|----------|----------------|
| 00123         | 21:05              | انقلاب جرار  | الدرك الوطني      | هاتفية 📞         | 0660xxxxxx    | سيارة إسعاف 1        | مشتة البياضة | قيد التعرف | 🔍 التعرف ⬩ 🏁 إنهاء ⬩ 📄 التفاصيل |

> زر "📄 التفاصيل" يفتح صفحة مخصصة للتدخل حسب نوعه.

---

## 📂 صفحة التفاصيل: عرض حسب نوع التدخل

تظهر 4 أزرار رئيسية:

- 🚑 **إجلاء صحي**  
- 🚗 **حادث مرور**  
- 🏢 **حرائق البنايات**  
- 🌾 **حرائق المحاصيل الزراعية**

---

### 📊 نموذج الجدول داخل كل نوع تدخل

| توقيت الخروج | نوع التدخل | الجهة المتصلة | نوع الاتصال | رقم الهاتف | الوسائل المرسلة | موقع الحادث | ساعة الوصول | عدد الضحايا | عدد الوفيات | أسماء المسعفين | أسماء الضحايا | الأعمار | الجنس | تفاصيل الإصابة | طبيعة الطريق أو الحريق | الخسائر | الأملاك المنقذة | الملاحظات | توقيت الانتهاء |
|--------------|--------------|----------------|----------------|--------------|--------------------|----------------|---------------|----------------|----------------|------------------|------------------|----------|--------|---------------------|----------------------------|-----------|--------------------|--------------|------------------|
| 21:05        | انقلاب جرار  | الدرك الوطني      | هاتفية 📞         | 0660xxxxxx    | سيارة إسعاف 1        | مشتة البياضة | 21:20          | 1              | 1              | إسماعيل، مراد         | إسحاق واصل            | 19 سنة  | ذكر   | كسر حوض – فقد وعي | طريق فلاحي ترابي           | تلفيات في الجرار | لا شيء             | [عرض التفاصيل] | 22:10            |

- عند الضغط على [عرض التفاصيل] تظهر نافذة جانبية فيها ملاحظة كاملة.  
- عند التصدير إلى Excel: تقسم كل 3 كلمات في سطر (داخل نفس الخلية).

---

## 📄 محتوى النماذج (مستخرج من الملفات الأصلية)

### 🔹 البلاغ الأولي – مشترك بين الأنواع

- 🕒 توقيت الخروج  
- 🧭 نوع التدخل  
- 🚒 الوسائل المرسلة  
- 👤 الجهة المتصلة  
- ☎️ رقم الهاتف  
- نوع الاتصال: (هاتفي، مباشر، راديو، طلب دعم...)  
- 📝 ملاحظة أولية  
- 🟡 الحالة = `قيد التعرف`

---

### 🔹 عملية التعرف – حسب النوع

#### 🚑 إجلاء:
- الوضعية الصحية – الوعي – التنفس – الإصابة
- نوع النقل: عادي، مستعجل

#### 🚗 حادث مرور:
- عدد الضحايا
- نوع الطريق: وطني، ولائي، بلدي، فلاحي
- المركبات المتصادمة
- السبب المحتمل
- الإصابات المسجلة
- الطلب دعم وسيلة أخرى أو وحدة

#### 🏢 حرائق بنايات:
- نوع المؤسسة: سكن، مصنع، مستشفى، مدرسة...
- طبيعة الحريق: داخلي، خارجي، طابق محدد...
- عدد نقاط الاشتعال
- تهديد السكان، إجلاء
- طلب الدعم: وسيلة/وحدة/فرق متخصصة

#### 🌾 حرائق محاصيل:
- نوع المحاصيل: تبن، قمح، شعير...
- المساحة المحترقة: هكتار، آر، م²
- عدد الحزم، عدد الأشجار، عدد الخلايا
- الأملاك المنقذة
- الدعم المطلوب
- وصف الخسائر

🟠 الحالة = `عملية تدخل`

---

### 🔹 إنهاء المهمة – حسب النوع

#### جميع النماذج تحتوي على:

- 🕙 ساعة نهاية المهمة  
- 👥 عدد الأعوان  
- 👨‍⚕️ أسماء المسعفين  
- ⚰️ أسماء الوفيات (إن وُجدت)  
- 📍 الموقع النهائي  
- 📝 ملاحظات ختامية  
- 🧾 تقرير مفصل حسب نوع التدخل

🟢 الحالة = `منتهية`

---

## 📦 التحميل والتصدير

- 📥 **تحميل Excel**:
  - لكل نوع تدخل جدول منفصل
  - تنسيق wrap text داخل الخلايا الطويلة

- 📄 **تحميل PDF**:
  - تقرير مفصل لكل تدخل

---

## ⚙️ المزايا البرمجية:

- كل التدخلات مرتبطة بـ `intervention_id`
- كل مرحلة محفوظة كـ `intervention_stage`
- كل سجل يحتوي على `created_by_unit`
- يتم جلب أسماء الأعوان تلقائيًا من توزيع التعداد الصباحي
- يتم إرسال إشعار في حال تأخر إدخال "إنهاء المهمة"

---

## 🔗 ترابط بين الصفحات

- `/coordination-center/all-interventions/` → الرئيسية  
- `/intervention/<id>/details/` → التفاصيل حسب نوع التدخل  
- `/intervention/<id>/complete/` → إنهاء المهمة  
- `/export/excel` و `/export/pdf/<id>` → التصدير

---

## 📌 ملاحظات عامة

- النظام يدعم تصعيد التدخل إلى "كارثة كبرى" عند الحاجة (زر يظهر لمركز التنسيق الولائي فقط).
- واجهة موحدة تضمن المرونة وتنوع النماذج دون تشويش.
- تقارير منظمة ومحفوظة بنظام شجري يسهل أرشفته ومراجعته.

---

📁 آخر تحديث: 22/07/2025  
👤 جهة الإعداد: مركز البرمجة والرقمنة – الحماية المدنية  
