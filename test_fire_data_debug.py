#!/usr/bin/env python3
"""
اختبار تشخيص مشكلة بيانات حريق المحاصيل
"""

import os
import sys
import django
import json

# إعداد Django
sys.path.append('/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/dpcdz')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dpcdz.settings')
django.setup()

from home.models import DailyIntervention, AgriculturalFireDetail, InterventionUnit
from datetime import date

def debug_fire_data():
    print("=== تشخيص مشكلة بيانات حريق المحاصيل ===\n")
    
    # 1. فحص التدخلات الموجودة
    print("1. فحص التدخلات الموجودة:")
    interventions = DailyIntervention.objects.all()
    print(f"   إجمالي التدخلات: {interventions.count()}")
    
    # 2. فحص أنواع التدخلات
    print("\n2. أنواع التدخلات الموجودة:")
    types = DailyIntervention.objects.values_list('intervention_type', flat=True).distinct()
    for t in types:
        count = DailyIntervention.objects.filter(intervention_type=t).count()
        print(f"   - {t}: {count}")
    
    # 3. فحص تدخلات الحرائق
    print("\n3. فحص تدخلات الحرائق:")
    fire_keywords = ['حريق', 'fire', 'محاصيل', 'agricultural', 'crop']
    for keyword in fire_keywords:
        count = DailyIntervention.objects.filter(intervention_type__icontains=keyword).count()
        print(f"   - يحتوي على '{keyword}': {count}")
    
    # 4. فحص النماذج المتخصصة
    print("\n4. فحص النماذج المتخصصة:")
    agricultural_details = AgriculturalFireDetail.objects.all()
    print(f"   تفاصيل حريق المحاصيل: {agricultural_details.count()}")
    
    # 5. إنشاء تدخل تجريبي
    print("\n5. إنشاء تدخل تجريبي:")
    unit = InterventionUnit.objects.first()
    if not unit:
        print("   خطأ: لا توجد وحدات")
        return
    
    # إنشاء تدخل
    intervention = DailyIntervention.objects.create(
        unit=unit,
        intervention_type='حريق محاصيل زراعية',
        location='منطقة تجريبية',
        date=date.today(),
        contact_source='citizen',
        contact_type='phone',
        status='reconnaissance'
    )
    print(f"   تم إنشاء تدخل رقم: {intervention.id}")
    
    # إنشاء تفاصيل متخصصة
    detail = AgriculturalFireDetail.objects.create(
        intervention=intervention,
        fire_type='standing_wheat',
        fire_sources_count=5,
        wind_direction='شمال شرق',
        wind_speed=20.0,
        population_threat=True,
        evacuation_location='المدرسة الابتدائية',
        intervening_agents_count=12,
        affected_families_count=8,
        standing_wheat_area=3.5,
        harvest_area=2.0,
        barley_area=1.5,
        straw_bales_count=75,
        grain_bags_count=30,
        fruit_trees_count=15,
        beehives_count=5,
        saved_area=2.0,
        saved_straw_bales=45,
        saved_equipment='جرار زراعي، آلة حصاد، مضخة مياه',
        final_notes='تدخل تجريبي لاختبار النظام - تم إخماد الحريق بنجاح'
    )
    print(f"   تم إنشاء تفاصيل متخصصة رقم: {detail.id}")
    
    # 6. اختبار جلب البيانات
    print("\n6. اختبار جلب البيانات:")
    test_intervention = DailyIntervention.objects.select_related('agricultural_fire_detail').get(id=intervention.id)
    
    print(f"   التدخل: {test_intervention.intervention_type}")
    print(f"   هل يوجد تفاصيل متخصصة: {hasattr(test_intervention, 'agricultural_fire_detail')}")
    
    if hasattr(test_intervention, 'agricultural_fire_detail'):
        detail = test_intervention.agricultural_fire_detail
        print(f"   نوع الحريق: {detail.fire_type}")
        print(f"   عدد البؤر: {detail.fire_sources_count}")
        print(f"   اتجاه الرياح: {detail.wind_direction}")
        print(f"   سرعة الرياح: {detail.wind_speed}")
        print(f"   قمح واقف: {detail.standing_wheat_area} هكتار")
        print(f"   حصيدة: {detail.harvest_area} هكتار")
        print(f"   حزم تبن: {detail.straw_bales_count}")
        print(f"   الأملاك المنقذة: {detail.saved_equipment}")
    
    # 7. اختبار API
    print("\n7. اختبار محاكاة API:")
    from django.db.models import Q
    
    # محاكاة get_interventions_by_type
    patterns = ['حريق محاصيل', 'حرائق المحاصيل', 'حريق زراعي', 'محاصيل', 'crop', 'agricultural-fire']
    query = Q()
    for pattern in patterns:
        query |= Q(intervention_type__icontains=pattern) | Q(intervention_type__exact=pattern)
    
    interventions_query = DailyIntervention.objects.filter(
        query,
        unit=unit,
        date=date.today()
    ).select_related(
        'agricultural_fire_detail',
        'building_fire_detail',
        'traffic_detail',
        'medical_detail'
    )
    
    print(f"   عدد التدخلات المطابقة: {interventions_query.count()}")
    
    for intervention in interventions_query:
        print(f"\n   تدخل رقم {intervention.id}:")
        print(f"   النوع: {intervention.intervention_type}")
        
        if hasattr(intervention, 'agricultural_fire_detail') and intervention.agricultural_fire_detail:
            detail = intervention.agricultural_fire_detail
            print(f"   التفاصيل المتخصصة موجودة:")
            print(f"     - نوع الحريق: {detail.fire_type}")
            print(f"     - عدد البؤر: {detail.fire_sources_count}")
            print(f"     - قمح واقف: {detail.standing_wheat_area}")
            print(f"     - حزم تبن: {detail.straw_bales_count}")
        else:
            print(f"   لا توجد تفاصيل متخصصة")
    
    print(f"\n=== انتهى التشخيص ===")
    print(f"معرف التدخل التجريبي: {intervention.id}")
    print(f"يمكنك اختبار الجدول على: http://127.0.0.1:8000/coordination-center/intervention-details/?id={intervention.id}")

if __name__ == '__main__':
    debug_fire_data()
