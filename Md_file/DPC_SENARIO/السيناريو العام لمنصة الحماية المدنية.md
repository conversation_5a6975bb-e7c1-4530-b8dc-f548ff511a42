# 📘 السيناريو العام لمنصة الحماية المدنية

هذا الملف يصف السيناريو الكامل لتدفق العمل داخل منصة الحماية المدنية، من تسجيل العتاد الصباحي إلى إدارة التدخلات اليومية وتصعيد الكوارث الكبرى، ويشمل أدوار المستخدمين، سير العمل، والواجهات المقترحة.

---

## 🧱 البنية العامة للمنصة

### 🏢 المستويات الإدارية:
1. مركز تنسيق الوحدة (بلدية/دائرة)
2. مركز التنسيق الولائي
3. مركز التنسيق الوطني

---

## 🕗 1. الصفحة الصباحية – تسجيل العتاد والوسائل

### 📋 المحتوى:
- ✅ تسجيل الحضور والغياب لكل عون عبر `Toggle`
- 🛠️ تحديد حالة كل عتاد (يعمل / معطل)
- ➕ إضافة عون جديد (نموذج يحتوي الرتبة، المهام، رقم القيد...)
- ➕ إضافة عتاد جديد (النوع، رقم التسلسلي، رقم إشارة الراديو...)
- 🔄 إمكانية تبديل العتاد البشري أو المادي بين الوحدات/الولايات (بإذن من المركز الولائي أو الوطني)

### 🟦 من يستخدم هذه الصفحة؟
- مركز التنسيق للوحدة
- المركز الولائي (عرض فقط + صلاحية تعديل عند الحاجة)

---

## 🚨 2. واجهة التدخلات اليومية – مركز التنسيق للوحدة

### 🎛️ الصفحة تحتوي على:
- 3 أزرار رئيسية:
  1. **📢 بلاغ أولي**
  2. **🧭 عملية التعرف**
  3. **✅ إنهاء المهمة**
  
### 📋 تدفق العملية:

#### 1️⃣ بلاغ أولي:
- يتم إدخال بيانات الاتصال والمكان والوسيلة ونوع الحادث.
- الحالة تصبح: `قيد التعرف`.

#### 2️⃣ عملية التعرف:
- يتم الاتصال بالعون الميداني.
- يُسجّل عدد الضحايا، الحاضرون، الخسائر.
- يمكن طلب دعم من وحدة أخرى.
  - إذا كان الحادث داخل المجال: يرسل الطلب إلى المركز الولائي، وهو من يحدد الوحدة الداعمة.
  - إذا كانت الوحدة تتدخل لصالح أخرى: يتم اختيار الوسيلة يدوياً.

#### 3️⃣ إنهاء المهمة:
- تسجيل أسماء وأعمار الضحايا، الإصابات، الوفيات.
- في حالة حريق: تسجيل المساحات المحترقة والمنقذة.

#### 🗂️ جدول التدخلات اليومية:
- يعرض كل تدخل بحالته (قيد التعرف / تدخل / منتهية / كارثة كبرى).
- يتم تحديث السطر نفسه في كل مرحلة.

---

## 👨‍🚒 3. واجهة العون الميداني (رئيس العدد)

### ✅ صفحته تحتوي على:
- 🔘 زرين كبيرين:
  - "🛠️ أثناء التدخل": لملء المعطيات الميدانية (الحاضرون، الضحايا...).
  - "✅ إنهاء التدخل": لملء التقرير حسب نوع التدخل.

### 📝 ملاحظات:
- لا يمكنه تعديل سطر التدخلات اليومية.
- يمكنه فقط حفظ تقريره الخاص، الذي يُستخدم من طرف مركز التنسيق.

---

## 🧑‍✈️ 4. قائد الوحدة الداعمة (رئيس الفريق)

### ✅ صفحته مخصصة عند وجود دعم بين الوحدات:
- ملء تقرير الدعم: الوسيلة، الطاقم، الخسائر الملاحظة.
- لا يعدّل السطر الرئيسي في جدول التدخل.
- كل تدخل يتم ربطه بالوحدة التي طلبت الدعم.

---

## 🛰️ 5. مركز التنسيق الولائي

### 📋 صلاحياته:
- مشاهدة كل تدخلات الوحدات التابعة.
- تلقي طلبات الدعم من الوحدات.
- تحديد الوحدة الداعمة وإرسالها.
- تصعيد الحوادث إلى كوارث كبرى إذا خرجت عن السيطرة.

---

## 🏛️ 6. مركز التنسيق الوطني

### 📋 صلاحياته:
- يتلقى بلاغات الكوارث الكبرى من الولايات.
- يُنسّق بين الولايات (إرسال طائرات، دعم بري، فرق متخصصة).
- مسؤول عن التقارير الوطنية والقرارات الإستراتيجية.

---

## 📤 تصعيد إلى الكوارث الكبرى

يتم من طرف المركز الولائي فقط عند:
- 🔥 انتشار حريق واسع
- 🌊 فيضانات مهددة للسكان
- 🌍 زلازل أو انهيارات أرضية

يتم تحويل الحادث إلى:
- صفحة الكوارث الكبرى
- يُدار هناك من طرف المركز الوطني والولائي

---

## 👤 أدوار المستخدمين

| الدور | صلاحياته | الصفحة الرئيسية |
|------|-----------|-----------------|
| رئيس العدد | إدخال تقارير ميدانية | `FieldAgentDashboard` |
| قائد الوحدة الداعمة | إدخال تقرير الدعم | `SupportUnitLeaderDashboard` |
| رئيس مركز التنسيق للوحدة | إدارة التدخلات اليومية | `LocalCoordinationDashboard` |
| مركز التنسيق الولائي | إدارة الدعم وتصعيد الكوارث | `WilayaCoordinationDashboard` |
| مركز التنسيق الوطني | إدارة الكوارث الكبرى | `NationalCommandDashboard` |

---

## 🔧 ملاحظات تقنية

- كل تدخل يحمل UUID
- كل مرحلة من التدخلات تُحدث نفس السطر
- يتم لاحقًا ربط التقرير بالعون الميداني
- تنبيهات صوتية عند وصول دعم أو بلاغ جديد
- تقارير PDF يتم توليدها تلقائيًا

---
