# 🛑 واجهة القيادة والتنسيق – الكوارث الكبرى

## 📌 الهدف:
لوحة تحكم مركزية لمتابعة الكوارث الكبرى (حرائق، فيضانات، زلازل) في الزمن الحقيقي من طرف:
- مراكز التنسيق المحلية (بلدية/دائرة)
- مراكز التنسيق الولائية
- مركز التنسيق الوطني بالجزائر العاصمة

---

## 🔤 اللغة:
- اللغة الأساسية: العربية (RTL)
- الخط: **Cairo**
- التصميم: حديث – بسيط – ديناميكي
- الخلفية: بيضاء (#ffffff)

---

## 🧭 أقسام الواجهة:

### 1️⃣ العنوان الرئيسي

- `🛑 منصة القيادة – الحماية المدنية`
- محاذاة: يمين
- حجم الخط: 24px – Bold

---

### 2️⃣ شريط الأدوات العلوي

| الأداة | الوظيفة |
|--------|----------|
| 🔍 بحث عن حادث | إدخال اسم، نوع أو رقم تدخل |
| 🔄 تحديث | يجلب آخر البيانات من الخادم |
| 📊 فلاتر | حسب النوع، الشدة، الولاية |
| 🌐 اللغة | خيار تبديل بين العربية/الفرنسية |

---

### 3️⃣ 🗺️ خريطة تفاعلية (Leaflet)

#### الخريطة تعرض:
- 🔥 حرائق فعالة
- 🌊 فيضانات
- 🌍 زلازل
- 🟠 المناطق الملوّنة حسب درجة الخطورة
- 🚒 مواقع الشاحنات والوحدات
- ✈️ الطائرات (رمز الطائرة)

#### الميزات:
- النقر على أيقونة → فتح لوحة معلومات
- تحديث مباشر عبر WebSocket
- زر [ملء الشاشة] في الركن العلوي

---

### 4️⃣ 📋 لوحة تفاصيل الحادث (جانب الخريطة – قابلة للطي)

#### بيانات الحادث:

| الحقل | القيمة (مثال) |
|-------|----------------|
| 🔥 نوع الحادث | حريق غابة – جبل بوكرع |
| 📍 الموقع | دائرة المشروحة، ولاية سوق أهراس |
| 🕒 وقت التبليغ | 2025/07/03 – 14:22 |
| 🚨 شدة الخطر | 🔴 مرتفع |
| 👨‍🚒 المُبلّغ | خالد (الوحدة SDA) |
| 🗒️ ملاحظات | رياح قوية، الحريق ينتشر بسرعة |

---

#### 🧯 قسم الدعم والتدخل:

| البند | المحتوى |
|------|----------|
| ✔️ وحدات محلية | 3 شاحنات FPT في الموقع |
| 🚒 دعم إضافي | وحدتان من تبسة – الوصول خلال 20 دقيقة |
| ✈️ دعم جوي | تم طلبه الساعة 14:35 – في انتظار |
| 🛰️ تتبع GPS | مفعّل لكل الشاحنات |

---

#### 🗺️ أوامر على الخريطة:

- [✓] تمركز على الموقع
- [🔄] تحديث الإحداثيات
- [📍] تتبع الوحدة
- [✏️] تحرير منطقة الخطر
- [🧾] توليد تقرير PDF

---

#### 🛠️ أوامر تنسيقية:

| الزر | الوظيفة |
|------|---------|
| 📤 طلب دعم جوي | إرسال طلب لطائرة قاذفة للمياه |
| 📡 إرسال وحدات قريبة | من أقرب وحدة متاحة حسب الجاهزية |
| 📨 إشعار المديرية العامة | إشعار مركزي تلقائي |
| 📞 سجل الإتصالات | سجل الإشعارات الصوتية/الراديو |

---

#### 📎 المرفقات:

- 📸 صورة جوية
- 🎤 تسجيل صوتي
- 📄 ملف منطقة خطر (KML)

---

### 5️⃣ 🧠 منطق التنسيق متعدد المستويات

#### التسلسل:

1. 🟢 **مركز تنسيق بلدي/دائرة**  
   يتلقى البلاغ من ميداني (شاحنة أو عون)

2. 🟠 **مركز تنسيق ولائي**  
   يتلقى طلبات الدعم من مراكز البلديات

3. 🔴 **مركز تنسيق وطني**  
   يتابع تطور الكارثة ويتدخل في حال الخطر الشامل أو الحاجة للطيران والدعم الجهوي

#### يمكن تغيير أسماء المراكز لاحقًا بسهولة من النظام

---

## 🔊 إشعارات النظام

- صوت تنبيه فوري لكل بلاغ كبير
- إشعار بصري (نافذة منبثقة + وميض)

---

## 🛰️ تتبع وحدات الدعم

- كل شاحنة أو طائرة مرفقة بـ GPS أو تطبيق هاتف
- تتبع مباشر على الخريطة
- تحديث الموقع كل 10-30 ثانية
- عرض السرعة، المسار، زمن الوصول

---

## 📄 مخرجات النظام

- 🧾 تقارير PDF تلقائية بعد كل كارثة
- 🧠 أرشيف لتفاصيل كل تدخل
- 📈 تحليلات لاحقة عبر واجهات أخرى

---

## 🎨 تفاصيل تصميم الواجهة

| العنصر | التصميم |
|--------|---------|
| الخط | Cairo – 16px |
| العناوين | 18–24px Bold |
| الأزرار | مستديرة – ألوان حسب السياق (أحمر/أخضر/أزرق) |
| الخريطة | Leaflet.js – خلفية Terrain |
| النوافذ الجانبية | White – Shadow – قابلة للطي |

---

## 📎 الملاحظات

- تعتمد الخريطة والتحديثات على WebSocket (Django Channels)
- صلاحيات الوصول تُحدد حسب الدور (ميداني – محلي – ولائي – وطني)
- كل خطوة تسجل زمنياً للتتبع

---
## 📡 كيف تقوم الشاحنة الميدانية بإرسال بلاغ كارثة كبرى؟

### 🧭 الهدف:
تمكين قائد الوحدة (في شاحنة الإطفاء أو سيارة التدخل) من إرسال بلاغ دقيق وفوري لحادث كبير (🔥، 🌊، 🌍) مباشرة من الميدان عبر تطبيق الهاتف أو التابلت.

---

### 🛠️ الخطوات التي يقوم بها عون الميدان:

1. 📱 يفتح تطبيق الحماية المدنية على هاتفه أو التابلت.
2. يضغط على زر: `🚨 بلّغ كارثة كبرى`
3. تظهر له واجهة تحتوي على:

   | الحقل | البيانات المطلوبة |
   |--------|------------------|
   | نوع الكارثة | 🔥 حريق، 🌊 فيضان، 🌍 زلزال |
   | الموقع | زر `📍 تحديد موقعي تلقائيًا` (GPS) |
   | درجة الخطورة | عادي / متوسط / مرتفع |
   | ملاحظات | نصية أو صوتية اختيارية |
   | مرفقات | (📸 صورة – 🎤 تسجيل صوتي – 📎 ملف) |

4. ✅ عند الضغط على "إرسال البلاغ":
   - تُرسل البيانات إلى الخادم (API)
   - يُضاف الحادث مباشرة في قاعدة البيانات
   - يتم:
     - رسم موقع الحادث آليًا على الخريطة في الواجهة
     - إطلاق إنذار صوتي في مراكز التنسيق
     - عرض معلومات البلاغ داخل اللوحة الجانبية

---

### 🗺️ رسم الخريطة آليًا:

- تعتمد المنصة على مكتبة **Leaflet.js**
- تُعرض أيقونة تمثل نوع الكارثة:
  - 🔥 نار
  - 🌊 فيضان
  - 🌍 زلزال
- يتم تلوين المنطقة حسب درجة الخطورة (أحمر، برتقالي، أصفر)
- يتم إنشاء "Zone Polygon" في حال كان المستخدم حدد منطقة يدويًا

---

### 🛰️ الموقع الجغرافي:

- إذا كان الهاتف مفعّل به GPS → يتم أخذ الإحداثيات تلقائيًا (lat/lng)
- إذا تعذر ذلك → يمكن إدخال الموقع يدويًا أو اختيار نقطة على الخريطة

---

### 🧾 مثال بيانات البلاغ:

```json
{
  "type": "fire",
  "location": {
    "latitude": 36.5221,
    "longitude": 7.2441
  },
  "severity": "high",
  "reported_by": "عون 301 – وحدة سوق أهراس",
  "notes": "رياح قوية، صعوبة في الإطفاء",
  "attachments": ["image1.jpg", "voicenote.mp3"]
}

📍جهة التصميم: فريق نظم الحماية المدنية  
📅 آخر تحديث: 2025/07/13
