# 👥 أدوار المستخدمين في نظام الحماية المدنية

هذا الملف يوضح كل دور من أدوار المستخدمين في المنصة، مع تحديد صلاحياته، الصفحة التي تظهر عند الدخول، ونطاق عمله داخل النظام.

---

## 1️⃣ 🧑‍🚒 رئيس العدد (العون الميداني)

### 🎯 المهام:
- تحديث بيانات التدخل عند الوصول إلى مكان الحادث.
- تسجيل عدد الضحايا، الإصابات، الحاضرون.
- ملء التقرير الميداني الخاص بنوع التدخل (حريق، حادث، إجلاء...).
- لا يمكنه تعديل السطر الرئيسي في جدول التدخلات اليومية.

### 🧩 الصفحة الافتراضية:
`FieldAgentDashboard`

---

## 2️⃣ 🧑‍✈️ قائد الوحدة الداعمة (عند إرسال دعم)

### 🎯 المهام:
- ملء التقرير الخاص بالتدخل الداعم (المساندة لوحدة أخرى).
- تحديد الوسيلة المتدخلة، الأفراد، ومدى النجاح.
- لا يملك صلاحيات تعديل السطر الأصلي، فقط تقرير دعم.

### 🧩 الصفحة الافتراضية:
`SupportUnitLeaderDashboard`

---

## 3️⃣ 🧑‍💻 رئيس مركز تنسيق الوحدة

### 🎯 المهام:
- استقبال البلاغات اليومية (عبر الهاتف أو الراديو).
- تسجيل البلاغ الأولي.
- تفعيل مرحلة "عملية التعرف" عند وصول العون الميداني.
- إنهاء المهمة بعد التدخل.
- طلب دعم من وحدات مجاورة أو من المركز الولائي.
- إدارة جدول التدخلات اليومية الخاص بوحدته.

### 🧩 الصفحة الافتراضية:
`LocalCoordinationDashboard`

---

## 4️⃣ 🧑‍💼 مركز التنسيق الولائي

### 🎯 المهام:
- مشاهدة كل التدخلات الخاصة بالوحدات التابعة للولاية.
- الرد على طلبات الدعم المرسلة من الوحدات.
- اختيار الوحدة الأنسب لإرسال الدعم.
- إصدار إنذارات صوتية للوحدات الداعمة.
- تصعيد الوضع إلى الكوارث الكبرى عند الحاجة.

### 🧩 الصفحة الافتراضية:
`WilayaCoordinationDashboard`

---

## 5️⃣ 🏛️ مركز التنسيق الوطني

### 🎯 المهام:
- مراقبة جميع البلاغات والتدخلات المصنفة "كوارث كبرى".
- التنسيق بين الولايات المتعددة لإرسال دعم جوي أو بري.
- إصدار قرارات وطنية في الحالات القصوى.
- إعداد تقارير وطنية شاملة.

### 🧩 الصفحة الافتراضية:
`NationalCommandDashboard`

---

## 🔐 ملاحظات تقنية

- يتم تحديد الصلاحيات باستخدام `Groups` أو `Roles` عبر Django Admin أو عند إنشاء المستخدم.
- كل دور يعرض له واجهته الخاصة فقط.
- يمكن استخدام مكتبة مثل `django-role-permissions` أو `django-guardian` للتحكم الدقيق في الوصول.

---
