# ✅ واجهة التعداد الصباحي للوحدة – التصميم UI

## 📌 الهدف:
صفحة مخصصة لتسجيل الجاهزية اليومية لكل وحدة (مثال: وحدة بئر بوحوش)، تشمل:
- 🧍‍♂️ العتاد البشري (أعوان، سائقون، رئيس الوحدة ، مستخلف رئيس الوحدة ، قائد الفصيلة ، مع إنشاء صفحة لاضافة مهام جديدة او تعديلها 
دة...)
- 🚒 العتاد والوسائل (شاحنات، مضخات، مولدات...)
- 🔄 إمكانية تحويل العتاد والأعوان (حسب الصلاحيات)
- 🕒 التسجيل يتم كل صباح ويتم استخدامه في التنسيق لاحقًا

---

## 🔤 اللغة:
- اللغة: العربية (RTL)
- الخط: **Cairo**
- الخلفية: **بيضاء (#ffffff)**
- التصميم: بسيط، واضح، سريع الاستخدام

---

## 🧭 أقسام الواجهة:

### 1️⃣ العنوان الرئيسي
- النص: `✅ التعداد الصباحي – وحدة [اسم الوحدة] – [تاريخ اليوم]`
- المحاذاة: جهة اليمين
- الحجم: **24px – Bold**
- اللون: أسود

---

### 2️⃣ العتاد البشري 👥

#### ✅ جدول الحالة:

| رقم القيد | الاسم الكامل | الرتبة | الحالة | ملاحظات | تعديل |
|----------|--------------|--------|--------|----------|--------|
| 1543     | خالد زواوي   | عون تدخل | ✅ حاضر | – | ✏️ |
| 1544     | علي بوزيد    | سائق     | 🚫 غائب | عطلة مرضية | ✏️ |

#### 🎛️ الحالات (toggle):
- ✅ حاضر (أخضر)
- 🚫 غائب (أحمر)
- 🕒 في مهمة (برتقالي)

#### ➕ زر "إضافة عون جديد"
- يفتح Modal Form يحتوي:
  - رقم القيد
  - الاسم الكامل
  - الرتبة (نص أو قائمة منسدلة)
  - الحالة (اختيار من الحالات الثلاث)
  - ملاحظات

#### ✏️ زر التعديل:
- يفتح نفس النموذج مع البيانات قابلة للتعديل

---

### 3️⃣ الوسائل والعتاد المتدخل 🚒

#### ✅ جدول الوسائل:

| الرقم التسلسلي | نوع الوسيلة | رقم إشارة الراديو | الحالة | ملاحظات | تعديل |
|----------------|--------------|--------------------|--------|----------|--------|
| 221-DAF-003     | شاحنة FPT     | R12                | ✅ تعمل | –        | ✏️     |
| GEN-455         | مولد كهربائي  | RGEN1              | 🔧 معطلة | عطل في المحرك | ✏️ |

#### 🛠️ Toggle الحالة:
- ✅ تعمل (أخضر)
- 🔧 معطلة (أحمر)
- ⏳ تحت الصيانة (برتقالي)

#### ➕ زر "إضافة وسيلة"
- يفتح Modal Form يحتوي:
  - الرقم التسلسلي
  - نوع الوسيلة
  - رقم إشارة الراديو
  - الحالة (تعمل، معطلة، صيانة)
  - ملاحظات

---

### 4️⃣ قسم التحويل (متاح فقط لصلاحية عليا 🛡️)

#### تحويل العتاد أو الأعوان:
- قائمة بالعنصر القابل للتحويل
- اختيار الوحدة المستقبلة من قائمة
- زر "↪️ تنفيذ التحويل"

#### سجل التحويلات:
| التاريخ | العنصر | من وحدة | إلى وحدة | المستخدم |
|---------|--------|---------|----------|-----------|

---

## 🎨 تصميم الواجهة – تفاصيل

| العنصر | النمط |
|--------|--------|
| الخط العام | Cairo – 16px |
| العناوين | 18px – Medium |
| الأزرار | زر أزرق (Radius: 8px، Padding: 12px) |
| الخلفية | بيضاء كاملة |
| الجداول | خطوط خفيفة، صفوف متناوبة (رمادي فاتح) |
| Toggle | ألوان الحالة (أخضر/أحمر/برتقالي) |

---

## 🧩 الملاحظات التقنية:

- يجب أن يتم التحقق من الصلاحيات قبل عرض أزرار التحويل
- يتم حفظ البيانات في قاعدة بيانات مرتبطة بتاريخ الإدخال
- كل سجل يومي يجب أن يكون منفصل لتتبع تغييرات كل يوم
- قابلية ربط التعداد مع لوحة القيادة الرئيسية لاحقًا

---

## 🏁 مخرجات النظام

- تسجيل يومي دقيق للحالة الميدانية
- مصدر مباشر لقرارات التنسيق (الجاهزية – النقص – الأعطال)
- قاعدة بيانات واضحة للمراقبة والمراجعة
what i need to add 
---


Add new user role مركز تنسيق العملي الوحدة he just can see his http://127.0.0.1:8000/coordination-center/  in his wilaya data because i will create many unit user  in wilaya and one uniter see only his data 