# تحديث نظام التدخلات اليومية - إضافة أنواع التدخل الجديدة

## التاريخ: 19 يوليو 2025 (التحديث النهائي)

---

## 📋 المهمة المطلوبة

تم طلب إضافة نوعين جديدين كأنواع تدخل منفصلة إلى صفحة التدخلات اليومية:

1. **حريق محاصيل زراعية** (نوع تدخل منفصل)
2. **حرائق البنايات والمؤسسات** (نوع تدخل منفصل)

**ملاحظة**: تم الاحتفاظ بنوع "حريق" الأصلي كما هو.

## 🔄 التحديث الأخير (19 يوليو 2025)

**التغيير المطلوب**: إزالة قسم "نوع التدخل الفرعي" للنوعين الجديدين في عملية التعرف، وإظهار الأنواع الفرعية مباشرة في:

- **حريق محاصيل زراعية**: في قائمة "نوع المحصول المحترق"
- **حرائق البنايات والمؤسسات**: في قائمة "طبيعة الحريق"

**النتيجة**:
- ✅ للنوعين الجديدين: لا يظهر قسم "نوع التدخل الفرعي"
- ✅ للأنواع الأخرى (إجلاء صحي، حادث مرور، حريق): يظهر قسم "نوع التدخل الفرعي" كما هو

## 🔄 التحديث الأحدث (19 يوليو 2025)

**التغيير المطلوب**: تحويل قائمة "نوع المحصول المحترق" إلى checkboxes مع إمكانية إضافة أنواع جديدة.

**التحسينات المطبقة**:
- ✅ تحويل القائمة المنسدلة إلى checkboxes
- ✅ إمكانية اختيار أكثر من نوع محصول في نفس الوقت
- ✅ إضافة حقل لإدخال نوع محصول جديد
- ✅ زر "إضافة" لإضافة النوع الجديد
- ✅ زر حذف للأنواع المضافة حديثاً
- ✅ تحديد النوع الجديد تلقائياً عند إضافته
- ✅ منع إضافة أنواع مكررة
- ✅ دعم مفتاح Enter لإضافة نوع جديد

---

## 🔧 التحديثات المطبقة

### 1. إضافة أنواع التدخل الجديدة في HTML

**الملف**: `dpcdz/templates/coordination_center/daily_interventions.html`

```html
<select class="form-control" id="intervention-type" required>
    <option value="">اختر نوع التدخل</option>
    <option value="medical"><i class="fas fa-ambulance"></i> إجلاء صحي</option>
    <option value="accident"><i class="fas fa-car-crash"></i> حادث مرور</option>
    <option value="fire"><i class="fas fa-fire"></i> حريق</option>
    <option value="agricultural-fire"><i class="fas fa-seedling"></i> حريق محاصيل زراعية</option>
    <option value="building-fire"><i class="fas fa-building"></i> حرائق البنايات والمؤسسات</option>
    <option value="other"><i class="fas fa-tools"></i> عمليات مختلفة</option>
</select>
```

### 2. إضافة معالجة الأنواع الجديدة في JavaScript

**التحديث الجديد**: إخفاء قسم "نوع التدخل الفرعي" للنوعين الجديدين وإظهار الأنواع مباشرة في الحقول المتخصصة.

```javascript
// معالجة حريق المحاصيل الزراعية
} else if (interventionType === 'agricultural-fire') {
    // إخفاء قسم نوع التدخل الفرعي
    const subtypeSection = document.getElementById('intervention-subtype').closest('.form-section');
    if (subtypeSection) subtypeSection.style.display = 'none';

    // إظهار قسم تفاصيل حريق المحاصيل وملء أنواع المحاصيل
    const agriculturalFireDetails = document.getElementById('agricultural-fire-details');
    if (agriculturalFireDetails) {
        agriculturalFireDetails.style.display = 'block';

        // ملء أنواع المحاصيل مباشرة في قائمة "نوع المحصول المحترق"
        const cropSelect = document.getElementById('agricultural-fire-nature');
        if (cropSelect) {
            cropSelect.innerHTML = '<option value="">اختر نوع المحصول</option>';
            const cropTypes = [
                'قمح واقف', 'حصيدة', 'شعير', 'حزم تبن',
                'غابة / أحراش', 'أكياس شعير / قمح',
                'أشجار مثمرة', 'خلايا نحل'
            ];

            cropTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.toLowerCase().replace(/\s+/g, '-');
                option.textContent = type;
                cropSelect.appendChild(option);
            });
        }
    }

// معالجة حرائق البنايات والمؤسسات
} else if (interventionType === 'building-fire') {
    // إخفاء قسم نوع التدخل الفرعي
    const subtypeSection = document.getElementById('intervention-subtype').closest('.form-section');
    if (subtypeSection) subtypeSection.style.display = 'none';

    // إظهار قسم تفاصيل حرائق البنايات وملء أنواع الحرائق
    const buildingFireDetails = document.getElementById('building-fire-details');
    if (buildingFireDetails) {
        buildingFireDetails.style.display = 'block';

        // ملء أنواع الحرائق مباشرة في قائمة "طبيعة الحريق"
        const fireNatureSelect = document.getElementById('building-fire-nature');
        if (fireNatureSelect) {
            fireNatureSelect.innerHTML = '<option value="">اختر طبيعة الحريق</option>';
            const fireNatureTypes = [
                'حريق بناية مخصصة للسكن', 'حريق مؤسسة مصنفة',
                'حريق مكان مستقبل للجمهور', 'حريق مركبة',
                'حريق محل أو سوق'
            ];

            fireNatureTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.toLowerCase().replace(/\s+/g, '-');
                option.textContent = type;
                fireNatureSelect.appendChild(option);
            });
        }
    }
}
```

### 2. إضافة معالجة أنواع الحرائق الفرعية

```javascript
// إضافة معالجة في دالة intervention-subtype change handler
} else if (subtype.includes('حريق-محاصيل-زراعية')) {
    updateFireNatureOptions('agricultural-crops');
} else if (subtype.includes('حرائق-البنايات-والمؤسسات')) {
    updateFireNatureOptions('buildings-institutions');
}
```

### 3. إضافة قسم تفاصيل الحريق في نموذج عملية التعرف

```html
<!-- للحرائق -->
<div id="fire-details" style="display: none;">
    <div class="form-row">
        <div class="form-group">
            <label class="form-label"><i class="fas fa-fire"></i> طبيعة الحريق</label>
            <select class="form-control" id="fire-nature">
                <option value="">اختر طبيعة الحريق</option>
            </select>
        </div>
    </div>
    
    <!-- تفاصيل إضافية للحريق -->
    <div class="form-row">
        <div class="form-group">
            <label class="form-label"><i class="fas fa-wind"></i> اتجاه الرياح</label>
            <input type="text" class="form-control" id="wind-direction" placeholder="مثال: شمال شرق">
        </div>
        <div class="form-group">
            <label class="form-label"><i class="fas fa-tachometer-alt"></i> سرعة الرياح (كم/سا)</label>
            <input type="number" class="form-control" id="wind-speed" min="0" placeholder="0">
        </div>
    </div>
    
    <div class="form-row">
        <div class="form-group">
            <label class="form-label"><i class="fas fa-fire-flame-curved"></i> عدد نقاط الاشتعال</label>
            <input type="number" class="form-control" id="ignition-points" min="1" placeholder="1">
        </div>
        <div class="form-group">
            <label class="form-label"><i class="fas fa-exclamation-triangle"></i> تهديد للسكان</label>
            <select class="form-control" id="threat-to-residents">
                <option value="no">لا</option>
                <option value="yes">نعم</option>
            </select>
        </div>
    </div>
    
    <div class="form-row" id="evacuation-details" style="display: none;">
        <div class="form-group">
            <label class="form-label"><i class="fas fa-people-arrows"></i> هل تم إجلاء السكان؟</label>
            <select class="form-control" id="evacuation-status">
                <option value="no">لا</option>
                <option value="yes">نعم</option>
            </select>
        </div>
        <div class="form-group">
            <label class="form-label"><i class="fas fa-hands-helping"></i> المساعدات المقدمة للسكان</label>
            <textarea class="form-control" id="assistance-provided" rows="2" placeholder="وصف المساعدات المقدمة..."></textarea>
        </div>
    </div>
</div>
```

### 4. إضافة دالة updateFireNatureOptions

```javascript
function updateFireNatureOptions(subtype) {
    const select = document.getElementById('fire-nature');
    select.innerHTML = '<option value="">اختر طبيعة الحريق</option>';

    let options = [];

    switch(subtype) {
        case 'agricultural-crops':
            options = [
                'قمح واقف',
                'حصيدة',
                'شعير',
                'حزم تبن',
                'غابة / أحراش',
                'أكياس شعير / قمح',
                'أشجار مثمرة',
                'خلايا نحل'
            ];
            break;
        case 'buildings-institutions':
            options = [
                'حريق بناية مخصصة للسكن',
                'حريق مؤسسة مصنفة',
                'حريق مكان مستقبل للجمهور',
                'حريق مركبة',
                'حريق محل أو سوق'
            ];
            break;
    }

    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option;
        optionElement.textContent = option;
        select.appendChild(optionElement);
    });
}
```

### 5. تحديث قسم إنهاء المهمة للحرائق

تم تحديث قسم الحرائق في نموذج إنهاء المهمة ليشمل:

#### أ. قسم الخسائر المسجلة:
- **خسائر حسب المساحة**: قمح واقف، حصيدة، شعير، غابة/أحراش، مباني
- **خسائر حسب العدد**: حزم تبن، أكياس قمح/شعير، أشجار مثمرة، خلايا نحل
- **معلومات إضافية**: عدد العائلات المتضررة، وصف الخسائر المادية

#### ب. قسم الأملاك المنقذة:
- مساحة منقذة (هكتار)
- حزم تبن منقذة
- مباني محمية
- وصف الممتلكات والآلات المنقذة

### 6. إضافة دوال مساعدة

#### أ. دالة معالجة تهديد السكان:
```javascript
document.addEventListener('DOMContentLoaded', function() {
    const threatSelect = document.getElementById('threat-to-residents');
    const evacuationDetails = document.getElementById('evacuation-details');
    
    if (threatSelect && evacuationDetails) {
        threatSelect.addEventListener('change', function() {
            if (this.value === 'yes') {
                evacuationDetails.style.display = 'flex';
            } else {
                evacuationDetails.style.display = 'none';
            }
        });
    }
});
```

#### ب. دالة جمع بيانات الحرائق:
```javascript
function collectCompletionFormData() {
    // ... بيانات عامة
    
    // Collect fire-specific data if fire section is visible
    const fireSection = document.getElementById('fire-specific-section');
    if (fireSection && fireSection.style.display !== 'none') {
        data.fireData = {
            // Damaged areas
            wheatStanding: parseFloat(document.getElementById('wheat-standing')?.value || 0),
            harvest: parseFloat(document.getElementById('harvest')?.value || 0),
            barley: parseFloat(document.getElementById('barley')?.value || 0),
            forest: parseFloat(document.getElementById('forest')?.value || 0),
            // ... المزيد من البيانات
        };
    }
    
    return data;
}
```

### 7. إضافة CSS للتصميم

```css
/* Fire intervention specific styles */
.fire-subsection {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.subsection-title {
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #dee2e6;
}

.subsection-title i {
    color: #dc3545;
    margin-left: 8px;
}

#fire-details {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

#fire-specific-section .form-control {
    border-left: 3px solid #dc3545;
}

#fire-specific-section .form-control:focus {
    border-left-color: #c82333;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
```

---

## 🎯 النتائج المحققة

### 1. أنواع التدخل المضافة:
- ✅ حريق محاصيل زراعية (نوع تدخل منفصل)
- ✅ حرائق البنايات والمؤسسات (نوع تدخل منفصل)
- ✅ الاحتفاظ بنوع "حريق" الأصلي

### 2. تفاصيل حريق محاصيل زراعية:
**الأنواع الفرعية**:
- قمح واقف، حصيدة، شعير، حزم تبن
- غابة/أحراش، أكياس شعير/قمح
- أشجار مثمرة، خلايا نحل

**معلومات عملية التعرف**:
- عدد البؤر (الموقد)
- اتجاه وسرعة الرياح
- تهديد للسكان ومكان الإجلاء
- عدد العائلات المتأثرة
- الجهات الحاضرة

**إحصائيات إنهاء المهمة**:
- خسائر حسب المساحة (هكتار): قمح واقف، حصيدة، شعير، غابة/أحراش
- خسائر حسب العدد: حزم تبن، أكياس قمح/شعير، أشجار مثمرة، خلايا نحل
- أملاك منقذة: مساحة منقذة، حزم تبن منقذة، ممتلكات وآلات منقذة

### 3. تفاصيل حرائق البنايات والمؤسسات:
**الأنواع الفرعية**:
- حريق بناية مخصصة للسكن
- حريق مؤسسة مصنفة
- حريق مكان مستقبل للجمهور
- حريق مركبة، حريق محل أو سوق

**معلومات عملية التعرف**:
- الموقع: داخل البناية، خارج المبنى، مكان مهدد بالانتشار
- طابق معين وغرفة محددة (اختياري)
- عدد نقاط الاشتعال
- جهة وسرعة الرياح
- تهديد للسكان وحالة الإجلاء
- المساعدات المقدمة للسكان

**إحصائيات إنهاء المهمة**:
- عدد العائلات المتضررة
- عدد الأعوان المتدخلين
- وصف تفصيلي للخسائر
- وصف الأملاك المنقذة

### 4. أقسام منفصلة في نموذج إنهاء المهمة:
- ✅ قسم خاص بحريق المحاصيل الزراعية
- ✅ قسم خاص بحرائق البنايات والمؤسسات
- ✅ الاحتفاظ بقسم الحريق الأصلي

---

## 🔗 الملفات المرجعية المستخدمة

1. **forms_pc_ai.md**: للبنية العامة وأنواع التدخل
2. **حرائق البنايات والمؤسسات.md**: لتفاصيل حرائق البنايات
3. **حريق محاصيل زراعية.md**: لتفاصيل حرائق المحاصيل

---

## ✅ الحالة النهائية

تم تطبيق جميع المتطلبات بنجاح. النظام الآن يدعم:

- **5 أنواع رئيسية للتدخل**: إجلاء صحي، حادث مرور، حريق، حريق محاصيل زراعية، حرائق البنايات والمؤسسات
- **أنواع فرعية مفصلة لكل نوع تدخل**
- **تفاصيل شاملة ومتخصصة لكل نوع**
- **نماذج متكاملة للبلاغ الأولي، عملية التعرف، وإنهاء المهمة**
- **حفظ وتتبع البيانات المفصلة لجميع أنواع التدخل**
- **أقسام منفصلة ومتخصصة في نموذج إنهاء المهمة**

## 🔄 التحديث الأخير (19 يوليو 2025) - إرجاع نوع "حريق" الأصلي

**المطلوب**: إرجاع نوع "حريق" الأصلي كما كان مع الاحتفاظ بالنوعين الجديدين.

**التحديثات المطبقة**:
- ✅ إرجاع نوع "حريق" الأصلي مع أنواعه الفرعية
- ✅ الاحتفاظ بالنوعين الجديدين كما هما
- ✅ إضافة معالجة كاملة لنوع "حريق" الأصلي

## 🎯 الهيكل النهائي للأنواع:

### أنواع التدخل الرئيسية:
1. **إجلاء صحي** - الأنواع الفرعية الموجودة سابقاً
2. **حادث مرور** - الأنواع الفرعية الموجودة سابقاً
3. **حريق** - النوع الأصلي مع أنواعه الفرعية:
   - حريق عام، حريق غابات، حريق منزلي، حريق صناعي
   - طبيعة الحريق: حريق مواد قابلة للاشتعال، حريق كهربائي، حريق غازات، حريق مواد كيميائية، حريق أخشاب ومواد عضوية، حريق سوائل قابلة للاشتعال
4. **حريق محاصيل زراعية** - نوع جديد منفصل مع نظام checkboxes
5. **حرائق البنايات والمؤسسات** - نوع جديد منفصل مع 5 أنواع فرعية
6. **عمليات مختلفة** - النوع الأصلي كما هو

الصفحة جاهزة للاستخدام على: `http://127.0.0.1:8000/coordination-center/daily-interventions/`

## 🧪 طريقة الاختبار:
1. افتح الصفحة واضغط على "بلاغ أولي"
2. اختر "حريق محاصيل زراعية" أو "حرائق البنايات والمؤسسات"
3. ستظهر الأنواع الفرعية المتخصصة لكل نوع
4. في "عملية التعرف" ستظهر الحقول المتخصصة
5. في "إنهاء المهمة" ستظهر الأقسام المتخصصة لكل نوع
