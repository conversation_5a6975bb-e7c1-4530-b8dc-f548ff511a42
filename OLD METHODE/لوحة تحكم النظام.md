# لوحة تحكم النظام - Morning Check Dashboard

## 📋 نظرة عامة

لوحة تحكم نظام التحقق الصباحي هي واجهة إدارية شاملة تعرض نظرة عامة على جاهزية جميع الوحدات في الولاية أو على مستوى النظام. تتيح للمدراء والمسؤولين مراقبة الأداء العام واتخاذ القرارات الاستراتيجية.

## 🔗 الرابط
```
http://127.0.0.1:8000/morning-check/dashboard/
```

## 🎯 الأهداف الرئيسية

### 1. المراقبة الشاملة
- **عرض جميع الوحدات**: نظرة واحدة على كل الوحدات
- **الإحصائيات العامة**: متوسط الجاهزية والأداء
- **المقارنات**: مقارنة أداء الوحدات المختلفة

### 2. اتخاذ القرارات
- **تحديد الأولويات**: الوحدات التي تحتاج تدخل فوري
- **توزيع الموارد**: نقل الأعوان والوسائل حسب الحاجة
- **التخطيط الاستراتيجي**: بناءً على البيانات والاتجاهات

## 📊 المكونات الأساسية

### 1. الإحصائيات العامة

#### بطاقات الملخص الرئيسية
```html
<!-- إجمالي الوحدات -->
<div class="stat-card primary">
    <div class="icon"><i class="fas fa-building"></i></div>
    <div class="value">12/16</div>
    <div class="label">الوحدات المفعلة</div>
</div>

<!-- الوحدات الجاهزة -->
<div class="stat-card success">
    <div class="icon"><i class="fas fa-check-circle"></i></div>
    <div class="value">8</div>
    <div class="label">وحدات جاهزة بالكامل</div>
</div>

<!-- متوسط الجاهزية -->
<div class="stat-card warning">
    <div class="icon"><i class="fas fa-percentage"></i></div>
    <div class="value">78.5%</div>
    <div class="label">متوسط الجاهزية</div>
</div>

<!-- التنبيهات النشطة -->
<div class="stat-card danger">
    <div class="icon"><i class="fas fa-exclamation-triangle"></i></div>
    <div class="value">15</div>
    <div class="label">تنبيهات نشطة</div>
</div>
```

### 2. الرسوم البيانية التفاعلية

#### رسم بياني لحالة الأعوان
```javascript
// رسم دائري لتوزيع حالة الأعوان
const personnelChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['حاضر', 'غائب', 'في مهمة'],
        datasets: [{
            data: [245, 23, 18],
            backgroundColor: ['#28a745', '#dc3545', '#ffc107']
        }]
    }
});
```

#### رسم بياني لحالة الوسائل
```javascript
// رسم دائري لتوزيع حالة الوسائل
const vehicleChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['جاهز', 'غير جاهز', 'صيانة'],
        datasets: [{
            data: [89, 12, 7],
            backgroundColor: ['#28a745', '#dc3545', '#ffc107']
        }]
    }
});
```

### 3. إدارة التنبيهات

#### عرض التنبيهات النشطة
```html
<div class="alerts-section">
    <div class="alerts-header">
        <h3><i class="fas fa-bell"></i> التنبيهات النشطة</h3>
        <div class="alert-badges">
            <span class="badge badge-danger">حرج: 3</span>
            <span class="badge badge-warning">عالي: 7</span>
            <span class="badge badge-info">متوسط: 4</span>
            <span class="badge badge-secondary">منخفض: 1</span>
        </div>
    </div>
    
    <!-- قائمة التنبيهات -->
    <div class="alert-item critical">
        <div class="alert-content">
            <h4>نقص في الأعوان</h4>
            <p>الوحدة الرئيسية عنابة - عالي - 2025-07-16</p>
        </div>
        <div class="alert-actions">
            <button class="btn btn-sm btn-primary" onclick="acknowledgeAlert(alertId)">
                <i class="fas fa-eye"></i> اطلاع
            </button>
            <button class="btn btn-sm btn-success" onclick="resolveAlert(alertId)">
                <i class="fas fa-check"></i> حل
            </button>
        </div>
    </div>
</div>
```

#### وظائف إدارة التنبيهات
```javascript
// تأكيد الاطلاع على التنبيه
function acknowledgeAlert(alertId) {
    fetch('/api/readiness-alerts/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            action: 'acknowledge_alert',
            alert_id: alertId
        })
    });
}

// حل التنبيه
function resolveAlert(alertId) {
    const notes = prompt('ملاحظات الحل (اختياري):');
    if (notes !== null) {
        fetch('/api/readiness-alerts/', {
            method: 'POST',
            body: JSON.stringify({
                action: 'resolve_alert',
                alert_id: alertId,
                resolution_notes: notes
            })
        });
    }
}
```

### 4. جدول تفاصيل الوحدات

#### عرض شامل للوحدات
```html
<table class="units-table">
    <thead>
        <tr>
            <th>الوحدة</th>
            <th>الفرقة العاملة</th>
            <th>الأعوان</th>
            <th>الوسائل</th>
            <th>نسبة الجاهزية</th>
            <th>الحالة</th>
            <th>الإجراءات</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>الوحدة الرئيسية عنابة</td>
            <td>فصيلة A</td>
            <td>45/50</td>
            <td>12/15</td>
            <td>
                <div class="readiness-bar">
                    <div class="readiness-fill excellent" style="width: 85%"></div>
                    <div class="readiness-text">85%</div>
                </div>
            </td>
            <td>
                <span class="status-ready">
                    <i class="fas fa-check-circle"></i> جاهز
                </span>
            </td>
            <td>
                <a href="/morning-check/?unit_id=14" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> عرض
                </a>
            </td>
        </tr>
    </tbody>
</table>
```

#### شريط الجاهزية التفاعلي
```css
.readiness-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.readiness-fill.excellent {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.readiness-fill.good {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.readiness-fill.poor {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}
```

## 🔧 الوظائف المتقدمة

### 1. الفلترة والبحث
```html
<!-- أدوات التحكم -->
<div class="controls-section">
    <form method="GET">
        <div class="controls-row">
            <div class="control-group">
                <label for="date">التاريخ</label>
                <input type="date" name="date" class="form-control" 
                       value="{{ selected_date|date:'Y-m-d' }}">
            </div>
            
            <div class="control-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> تحديث
                </button>
            </div>
        </div>
    </form>
</div>
```

### 2. التصدير والتقارير
```python
# إنشاء تقارير مخصصة
def generate_dashboard_report(units, date):
    report_data = {
        'summary': {
            'total_units': units.count(),
            'ready_units': units.filter(is_fully_ready=True).count(),
            'avg_readiness': calculate_average_readiness(units),
            'total_alerts': get_active_alerts_count(units, date)
        },
        'units_details': [],
        'alerts_summary': get_alerts_summary(units, date)
    }
    
    return report_data
```

## 📱 التصميم المتجاوب

### للشاشات الكبيرة
```css
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.charts-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}
```

### للشاشات المتوسطة والصغيرة
```css
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .alerts-header {
        flex-direction: column;
        gap: 15px;
    }
}
```

## 🔐 الصلاحيات والأمان

### مستويات الوصول
```python
# تحديد الوحدات المتاحة حسب الدور
if user_profile.role == 'admin':
    available_units = InterventionUnit.objects.all()
elif user_profile.role == 'wilaya_manager':
    available_units = InterventionUnit.objects.filter(wilaya=user_profile.wilaya)
elif user_profile.role in ['unit_manager', 'unit_coordinator']:
    available_units = user_profile.intervention_units.all()
else:
    available_units = InterventionUnit.objects.none()
```

### حماية البيانات
- **تشفير الاتصالات**: استخدام HTTPS
- **التحقق من الهوية**: مطلوب تسجيل الدخول
- **تسجيل العمليات**: تتبع جميع التغييرات
- **النسخ الاحتياطي**: حفظ البيانات بانتظام

## 📊 مؤشرات الأداء (KPIs)

### المؤشرات الرئيسية
1. **نسبة الجاهزية العامة**: متوسط جاهزية جميع الوحدات
2. **معدل الاستجابة**: سرعة حل التنبيهات
3. **نسبة الحضور**: معدل حضور الأعوان
4. **كفاءة الوسائل**: نسبة الوسائل الجاهزة

### التقارير الدورية
- **تقرير يومي**: ملخص يومي للجاهزية
- **تقرير أسبوعي**: تحليل الاتجاهات
- **تقرير شهري**: تقييم الأداء العام
- **تقرير سنوي**: التخطيط الاستراتيجي

## 🚀 التطوير المستقبلي

### المميزات المخططة
1. **الذكاء الاصطناعي**: تنبؤ بالمشاكل قبل حدوثها
2. **التكامل مع الأنظمة الخارجية**: ربط مع أنظمة الطوارئ
3. **التطبيق المحمول**: واجهة للهواتف الذكية
4. **التحليلات المتقدمة**: تحليل البيانات الضخمة

### التحسينات التقنية
1. **تحسين الأداء**: تسريع تحميل البيانات
2. **واجهة أفضل**: تحسين تجربة المستخدم
3. **المزيد من التقارير**: تقارير مخصصة
4. **التنبيهات الذكية**: إشعارات فورية

## 📞 الدعم والمساعدة

### الموارد المتاحة
- **دليل المستخدم**: شرح مفصل لجميع الوظائف
- **الفيديوهات التعليمية**: شروحات مرئية
- **الأسئلة الشائعة**: إجابات للاستفسارات المتكررة
- **الدعم الفني**: فريق متخصص للمساعدة

### التواصل
- **البريد الإلكتروني**: للاستفسارات العامة
- **الهاتف**: للمساعدة الفورية
- **النظام الداخلي**: تذاكر الدعم الفني
