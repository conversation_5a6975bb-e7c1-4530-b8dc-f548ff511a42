# 📋 سيناريو التدخل العام - نظام الحماية المدنية DPC_DZ

## 🎯 الهدف العام
تطوير نظام شامل لإدارة التدخلات والكوارث في الحماية المدنية الجزائرية، يغطي جميع مراحل التدخل من التعداد الصباحي إلى إدارة الكوارث الكبرى.

---

## 🏗️ المراحل الرئيسية للتطوير

### المرحلة الأولى: إضافة زر الكوارث الكبرى ✅ (مطلوب فوراً)
**الموقع**: الصفحة الرئيسية `http://127.0.0.1:8000/home/<USER>

**المطلوب**:
- إضافة زر جديد بعنوان "الكوارث الكبرى" مع أيقونة `fas fa-exclamation-circle`
- عند النقر يوجه إلى صفحة الكوارث الكبرى
- تصميم مطابق للأزرار الموجودة

### المرحلة الثانية: تطوير صفحة الكوارث الكبرى ✅ (مطلوب فوراً)
**الموقع**: `http://127.0.0.1:8000/major-disasters/`

**المكونات**:
1. **زرين رئيسيين**:
   - "رئيس العدد" - للعون الميداني
   - "قائد الوحدة" - لقائد الوحدة الداعمة

2. **خريطة تفاعلية** (Leaflet.js):
   - عرض مواقع الكوارث
   - تتبع الوحدات المتدخلة
   - تحديث في الوقت الفعلي

3. **لوحة تحكم مركزية**:
   - عرض تفاصيل الكوارث النشطة
   - إدارة طلبات الدعم
   - تنسيق بين الوحدات

### المرحلة الثالثة: تطوير واجهة مركز التنسيق ⏳ (التالي)
**الموقع**: `http://127.0.0.1:8000/coordination-center/`

**التحسينات المطلوبة**:
1. **إضافة زرين جديدين**:
   - "رئيس العدد" - يوجه لواجهة العون الميداني
   - "قائد الوحدة" - يوجه لواجهة قائد الوحدة

2. **تطوير واجهة التدخلات اليومية**:
   - بلاغ أولي
   - عملية التعرف
   - إنهاء المهمة
   - جدول التدخلات المباشر

---

## 🧑‍💻 الأدوار والواجهات المطلوبة

### 1. رئيس العدد (العون الميداني)
**الواجهة**: `FieldAgentDashboard`
**المهام**:
- تحديث بيانات التدخل الميداني
- تسجيل الضحايا والخسائر
- رفع تقارير مصورة وصوتية
- إرسال بلاغات الكوارث الكبرى

**الصفحات المطلوبة**:
- واجهة التدخل الميداني
- نموذج تقرير الكارثة
- رفع المرفقات

### 2. قائد الوحدة (الدعم)
**الواجهة**: `SupportUnitLeaderDashboard`
**المهام**:
- إدارة تقارير الدعم
- تنسيق الوحدات الداعمة
- متابعة حالة الوسائل والأعوان

**الصفحات المطلوبة**:
- واجهة إدارة الدعم
- تقارير الوحدات الداعمة
- تتبع الوسائل

### 3. مركز التنسيق المحلي
**الواجهة**: `LocalCoordinationDashboard`
**المهام**:
- إدارة التدخلات اليومية
- طلب الدعم من الوحدات
- تصعيد الكوارث الكبرى

**الصفحات المطلوبة**:
- واجهة التدخلات اليومية
- نظام طلب الدعم
- جدول التدخلات المباشر

### 4. مركز التنسيق الولائي
**الواجهة**: `WilayaCoordinationDashboard`
**المهام**:
- مراقبة جميع وحدات الولاية
- توزيع طلبات الدعم
- تصعيد للمستوى الوطني

### 5. مركز التنسيق الوطني
**الواجهة**: `NationalCommandDashboard`
**المهام**:
- إدارة الكوارث الوطنية
- تنسيق بين الولايات
- اتخاذ القرارات الاستراتيجية

---

## 🔄 سير العمل (Workflow)

### سيناريو التدخل العادي:
1. **التعداد الصباحي** ✅ (مكتمل)
   - تسجيل حضور الأعوان
   - فحص حالة الوسائل
   - تحديث الجاهزية

2. **استقبال البلاغ**
   - تسجيل البلاغ الأولي
   - تحديد نوع التدخل
   - إرسال الوحدة المناسبة

3. **عملية التعرف**
   - وصول الفريق للموقع
   - تقييم الوضع
   - طلب دعم إضافي عند الحاجة

4. **إنهاء المهمة**
   - تسجيل النتائج النهائية
   - توثيق الخسائر والإنجازات
   - إعداد التقرير النهائي

### سيناريو الكارثة الكبرى:
1. **الإبلاغ عن الكارثة**
   - بلاغ من العون الميداني
   - تحديد الموقع GPS
   - تقييم درجة الخطورة

2. **التصعيد**
   - إشعار المركز الولائي
   - طلب دعم إضافي
   - تفعيل خطة الطوارئ

3. **التنسيق**
   - توزيع الوحدات
   - تتبع التقدم
   - تحديث الخريطة

4. **الإدارة المركزية**
   - متابعة من المستوى الوطني
   - تنسيق بين الولايات
   - اتخاذ قرارات استراتيجية

---

## 🛠️ المتطلبات التقنية

### قاعدة البيانات:
**نماذج جديدة مطلوبة**:
```python
# نموذج الكوارث الكبرى
class MajorDisaster(models.Model):
    disaster_type = models.CharField(max_length=50)  # حريق، فيضان، زلزال
    location = models.CharField(max_length=200)
    latitude = models.FloatField()
    longitude = models.FloatField()
    severity = models.CharField(max_length=20)  # عادي، متوسط، مرتفع
    status = models.CharField(max_length=30)  # نشط، تحت السيطرة، منتهي
    reported_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

# نموذج التدخلات اليومية
class DailyIntervention(models.Model):
    intervention_type = models.CharField(max_length=50)
    location = models.CharField(max_length=200)
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE)
    status = models.CharField(max_length=30)  # قيد التعرف، تدخل، منتهية
    casualties = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

# نموذج طلبات الدعم
class SupportRequest(models.Model):
    requesting_unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, related_name='support_requests')
    supporting_unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, related_name='support_provided')
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE)
    status = models.CharField(max_length=20)  # مطلوب، موافق عليه، مرفوض
    created_at = models.DateTimeField(auto_now_add=True)
```

### الواجهة الأمامية:
**التقنيات المطلوبة**:
- **Leaflet.js**: للخرائط التفاعلية
- **WebSocket**: للتحديثات المباشرة
- **Chart.js**: للإحصائيات والرسوم البيانية
- **Bootstrap**: للتصميم المتجاوب

---

## 📅 الجدول الزمني المقترح

### الأسبوع الأول:
- ✅ إضافة زر الكوارث الكبرى للصفحة الرئيسية
- ✅ إنشاء صفحة الكوارث الكبرى الأساسية
- ✅ إضافة الأزرار المطلوبة لمركز التنسيق

### الأسبوع الثاني:
- تطوير واجهة التدخلات اليومية
- إنشاء نماذج قاعدة البيانات الجديدة
- تطوير واجهة رئيس العدد

### الأسبوع الثالث:
- تطوير واجهة قائد الوحدة
- إضافة الخريطة التفاعلية
- تطوير نظام طلب الدعم

### الأسبوع الرابع:
- تطوير واجهات المراكز الولائية والوطنية
- إضافة WebSocket للتحديثات المباشرة
- اختبار النظام الكامل

---

## 🎯 الأولويات الفورية

### المطلوب الآن:
1. **إضافة زر الكوارث الكبرى** للصفحة الرئيسية
2. **إنشاء صفحة الكوارث الكبرى** مع الزرين المطلوبين
3. **إضافة الزرين** لصفحة مركز التنسيق

### المطلوب لاحقاً:
1. تطوير الواجهات المتخصصة
2. إضافة الخرائط التفاعلية
3. تطوير نظام التحديثات المباشرة
4. إضافة التقارير والإحصائيات

---

**تم إعداد هذا السيناريو بواسطة**: عبد الرزاق مختاري  
**التاريخ**: 15 يوليو 2025  
**الحالة**: جاهز للتنفيذ
