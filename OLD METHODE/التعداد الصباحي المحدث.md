# التعداد الصباحي المحدث - نظام Morning Check System

## 📋 نظرة عامة

تم تطوير نظام التعداد الصباحي المحدث ليكون نظاماً شاملاً يدمج جميع عناصر إدارة الوحدات في واجهة واحدة متقدمة. النظام يجمع بين التعداد التقليدي للأعوان والوسائل مع مميزات متقدمة لإدارة الفرق ونظام 8 ساعات والتنبيهات الذكية.

## 🔗 الرابط
```
http://127.0.0.1:8000/coordination-center/daily-unit-count/?unit_id=11
```

## 🎯 المميزات الأساسية

### 1. التعداد التقليدي المحسن
- **إدارة الأعوان**: عرض وتحديث حالة جميع أعوان الوحدة
- **إدارة الوسائل**: متابعة حالة جميع وسائل الوحدة
- **الفلترة والبحث**: إمكانيات بحث متقدمة حسب الحالة والرتبة والمنصب
- **الإحصائيات الفورية**: عرض إحصائيات مباشرة للحضور والغياب

### 2. نظام التحقق الصباحي المتقدم
- **ملخص الجاهزية**: عرض نسبة الجاهزية العامة للوحدة
- **الفرقة العاملة**: عرض الفرقة النشطة حالياً
- **التنبيهات النشطة**: عرض التنبيهات المهمة التي تحتاج متابعة

## 🔧 المكونات الجديدة

### بطاقات الملخص
```html
<!-- نسبة الجاهزية العامة -->
<div class="card text-center">
    <i class="fas fa-percentage fa-2x text-primary"></i>
    <h4 class="text-primary">85%</h4>
    <p class="text-muted">نسبة الجاهزية العامة</p>
</div>

<!-- الفرقة العاملة -->
<div class="card text-center">
    <i class="fas fa-clock fa-2x text-success"></i>
    <h5 class="text-success">فصيلة A</h5>
    <p class="text-muted">الفرقة العاملة</p>
</div>

<!-- التنبيهات النشطة -->
<div class="card text-center">
    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
    <h4 class="text-danger">3</h4>
    <p class="text-muted">تنبيهات نشطة</p>
</div>
```

### التبويبات المتقدمة

#### 1. تبويب إدارة الفرق
- **عرض الفرقة العاملة**: إظهار الفرقة النشطة حالياً
- **قائمة الفرق المتاحة**: عرض جميع الفرق مع تفاصيلها
- **تفعيل الفرق**: إمكانية تغيير الفرقة العاملة
- **إحصائيات الفرق**: عدد الأعوان في كل فرقة

```javascript
// تفعيل فرقة جديدة
function activateShift(shiftId) {
    fetch('/api/shift-management/', {
        method: 'POST',
        body: JSON.stringify({
            action: 'set_active_shift',
            shift_id: shiftId,
            unit_id: unitId,
            date: currentDate
        })
    });
}
```

#### 2. تبويب نظام 8 ساعات
- **عرض الأعوان الإداريين**: قائمة بأعوان نظام 8 ساعات
- **فترات العمل**: عرض الفترات الصباحية والمسائية والليلية
- **أنواع المهام**: تصنيف المهام (إداري، صيانة، اتصالات، أمن)
- **تتبع الحضور**: متابعة حضور وغياب الأعوان

#### 3. تبويب توزيع الوسائل
- **ربط مع نظام الجاهزية**: انتقال مباشر لصفحة توزيع الوسائل
- **عرض التوزيع الحالي**: ملخص توزيع الأعوان على الوسائل

## 📊 حساب نسبة الجاهزية

### خوارزمية الحساب
```python
def calculate_readiness_score(self):
    # نسبة الوسائل الجاهزة (40%)
    vehicle_readiness = (ready_vehicles / total_vehicles) * 100 * 0.4
    
    # نسبة الأعوان الحاضرين (40%)
    personnel_readiness = (present_personnel / total_personnel) * 100 * 0.4
    
    # نسبة التوزيع (20%)
    assignment_readiness = (assigned_personnel / present_personnel) * 100 * 0.2
    
    # النتيجة النهائية
    overall_score = vehicle_readiness + personnel_readiness + assignment_readiness
    
    return int(overall_score)
```

### مستويات الجاهزية
- **90% فأكثر**: جاهز بالكامل (أخضر)
- **70-89%**: جاهز جزئياً (أصفر)
- **50-69%**: يحتاج تحسين (برتقالي)
- **أقل من 50%**: غير جاهز (أحمر)

## 🚨 نظام التنبيهات

### أنواع التنبيهات
1. **نقص في الأعوان**: عندما يكون عدد الحاضرين أقل من المطلوب
2. **نقص في الوسائل**: عندما تكون الوسائل الجاهزة غير كافية
3. **فرقة ناقصة**: عندما تكون الفرقة العاملة غير مكتملة
4. **وسيلة غير جاهزة**: عندما تحتاج وسيلة لصيانة أو إصلاح
5. **تحتاج تأكيد يدوي**: عندما تحتاج الجاهزية لتأكيد من المسؤول

### مستويات الأولوية
- **حرج**: مشاكل تؤثر على الجاهزية الفورية
- **عالي**: مشاكل مهمة تحتاج حل سريع
- **متوسط**: مشاكل عادية يمكن حلها لاحقاً
- **منخفض**: ملاحظات وتحسينات

## 🔄 التكامل مع الأنظمة الموجودة

### النماذج المستخدمة
```python
# النماذج الأساسية الموجودة
- UnitPersonnel: بيانات الأعوان
- UnitEquipment: بيانات الوسائل
- DailyPersonnelStatus: حالة الأعوان اليومية
- DailyEquipmentStatus: حالة الوسائل اليومية

# النماذج الجديدة المضافة
- MorningCheckSummary: ملخص التحقق الصباحي
- WorkShift: الفرق العاملة
- DailyShiftSchedule: جدولة الفرق
- EightHourPersonnel: أعوان نظام 8 ساعات
- ReadinessAlert: التنبيهات
```

### البيانات المتبادلة
```python
context = {
    # البيانات الأساسية
    'daily_count': daily_count,
    'all_personnel': all_personnel,
    'all_equipment': all_equipment,
    
    # بيانات النظام الجديد
    'morning_summary': morning_summary,
    'available_shifts': available_shifts,
    'daily_schedule': daily_schedule,
    'eight_hour_personnel': eight_hour_personnel,
    'active_alerts': active_alerts,
}
```

## 🎨 التصميم والواجهة

### الألوان المستخدمة
- **الأزرق (#007bff)**: العناوين والعناصر الأساسية
- **الأخضر (#28a745)**: الحالات الإيجابية والجاهزية
- **الأحمر (#dc3545)**: التنبيهات والمشاكل
- **الأصفر (#ffc107)**: التحذيرات والحالات المتوسطة

### التخطيط المتجاوب
```css
/* للشاشات الكبيرة */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

/* للشاشات المتوسطة */
@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
}
```

## 📱 الاستخدام

### للمنسقين
1. **اختيار الوحدة**: من القائمة المنسدلة
2. **مراجعة الملخص**: فحص بطاقات الجاهزية
3. **إدارة الفرق**: تفعيل الفرقة المناسبة
4. **متابعة التنبيهات**: حل المشاكل النشطة

### للمدراء
1. **مراقبة عامة**: عرض حالة جميع الوحدات
2. **تحليل الأداء**: مراجعة نسب الجاهزية
3. **اتخاذ القرارات**: بناءً على التقارير والإحصائيات

## 🔧 الصيانة والتطوير

### الملفات المحدثة
- `home/views.py`: تحديث daily_unit_count_view
- `templates/coordination_center/daily_unit_count.html`: إضافة المكونات الجديدة
- `home/models.py`: إضافة النماذج الجديدة

### نقاط التطوير المستقبلية
1. **تحسين الواجهة**: إضافة المزيد من التفاعلية
2. **التقارير المتقدمة**: تقارير تفصيلية للأداء
3. **التنبيهات الذكية**: تنبيهات تلقائية عبر الإشعارات
4. **التكامل مع الأنظمة الخارجية**: ربط مع أنظمة أخرى

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- **التوثيق الفني**: راجع ملفات التوثيق في المجلد
- **سجل الأخطاء**: تحقق من logs النظام
- **اختبار الوظائف**: استخدم بيانات تجريبية للاختبار
