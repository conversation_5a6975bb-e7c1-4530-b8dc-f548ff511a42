# النظام المستقل - Morning Check System

## 📋 نظرة عامة

النظام المستقل لـ Morning Check System هو واجهة مخصصة بالكامل لإدارة التعداد الصباحي والتحقق من الجاهزية. تم تصميمه ليكون نظاماً قائماً بذاته مع جميع الوظائف المتقدمة في واجهة واحدة شاملة.

## 🔗 الرابط
```
http://127.0.0.1:8000/morning-check/?unit_id=11
```

## 🎯 الفلسفة التصميمية

### 1. التخصص الكامل
- **مخصص للتحقق الصباحي**: كل عنصر مصمم لهذا الغرض
- **واجهة موحدة**: جميع الوظائف في مكان واحد
- **تدفق عمل محسن**: خطوات منطقية ومتسلسلة

### 2. الكفاءة والسرعة
- **وصول سريع**: جميع المعلومات في متناول اليد
- **تحديث فوري**: البيانات محدثة في الوقت الفعلي
- **عمليات مبسطة**: أقل عدد من النقرات

## 🏗️ الهيكل العام

### 1. الهيدر الموحد
```html
<header>
    <div class="header-container">
        <div class="flag">
            <img src="algeria_flag.png" alt="العلم الجزائري">
        </div>
        <div class="title">
            <h1>المديرية العامة للحماية المدنية</h1>
        </div>
        <div class="logo">
            <img src="civil_protection_logo.png" alt="شعار الحماية المدنية">
        </div>
    </div>
</header>
```

### 2. العنوان الرئيسي
```html
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-sun"></i>
        نظام التحقق الصباحي
    </h1>
    <p>نظام شامل لإدارة التعداد الصباحي وجاهزية الوحدات</p>
</div>
```

### 3. أدوات التحكم
```html
<div class="controls-section">
    <form method="GET">
        <div class="controls-row">
            <div class="control-group">
                <label for="unit_id">الوحدة</label>
                <select name="unit_id" class="form-control">
                    <option value="11">الوحدة الثانوية أولاد إدريس</option>
                    <!-- المزيد من الوحدات -->
                </select>
            </div>
            
            <div class="control-group">
                <label for="date">التاريخ</label>
                <input type="date" name="date" class="form-control" 
                       value="{{ selected_date|date:'Y-m-d' }}">
            </div>
            
            <div class="control-group">
                <a href="/morning-check/dashboard/" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i> لوحة التحكم
                </a>
            </div>
        </div>
    </form>
</div>
```

## 📊 بطاقات الملخص

### التصميم المرئي
```html
<div class="summary-cards">
    <!-- بطاقة الأعوان -->
    <div class="summary-card">
        <h3><i class="fas fa-users"></i> الأعوان</h3>
        <div class="value">6/7</div>
        <div class="label">حاضر من إجمالي</div>
    </div>
    
    <!-- بطاقة الوسائل -->
    <div class="summary-card">
        <h3><i class="fas fa-truck"></i> الوسائل</h3>
        <div class="value">8/10</div>
        <div class="label">جاهز من إجمالي</div>
    </div>
    
    <!-- بطاقة نسبة الجاهزية -->
    <div class="summary-card">
        <h3><i class="fas fa-percentage"></i> نسبة الجاهزية</h3>
        <div class="value">85%</div>
        <div class="label">الجاهزية العامة</div>
    </div>
    
    <!-- بطاقة الفرقة العاملة -->
    <div class="summary-card">
        <h3><i class="fas fa-clock"></i> الفرقة العاملة</h3>
        <div class="value">فصيلة A</div>
        <div class="label">اليوم</div>
    </div>
</div>
```

### التصميم CSS
```css
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
    border-left: 5px solid #007bff;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
}

.summary-card .value {
    font-size: 2rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 5px;
}
```

## 🚨 نظام التنبيهات المدمج

### عرض التنبيهات
```html
{% if active_alerts %}
<div class="alert alert-warning">
    <h4><i class="fas fa-exclamation-triangle"></i> تنبيهات نشطة ({{ active_alerts.count }})</h4>
    <ul>
        {% for alert in active_alerts %}
        <li>{{ alert.title }} - {{ alert.get_priority_display }}</li>
        {% endfor %}
    </ul>
</div>
{% endif %}
```

### أنواع التنبيهات
1. **نقص في الأعوان**: عدد الحاضرين أقل من المطلوب
2. **نقص في الوسائل**: وسائل غير كافية للعمليات
3. **فرقة ناقصة**: الفرقة العاملة غير مكتملة
4. **وسيلة غير جاهزة**: تحتاج صيانة أو إصلاح
5. **تحتاج تأكيد يدوي**: تتطلب تدخل المسؤول

## 📑 نظام التبويبات المتقدم

### 1. تبويب الأعوان
```html
<div id="personnel" class="tab-content active">
    <div class="action-buttons">
        <button class="btn btn-success" onclick="addPersonnel()">
            <i class="fas fa-plus"></i> إضافة عون
        </button>
    </div>
    
    <table class="data-table">
        <thead>
            <tr>
                <th>رقم القيد</th>
                <th>الاسم الكامل</th>
                <th>الرتبة</th>
                <th>المنصب</th>
                <th>الحالة</th>
                <th>ملاحظات</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for personnel in unit_personnel %}
            <tr>
                <td>{{ personnel.registration_number }}</td>
                <td>{{ personnel.full_name }}</td>
                <td>{{ personnel.rank|default:"-" }}</td>
                <td>{{ personnel.position|default:"-" }}</td>
                <td>
                    <span class="status-badge status-{{ status.status }}">
                        {{ status.get_status_display }}
                    </span>
                </td>
                <td>{{ status.notes|default:"-" }}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="editPersonnelStatus({{ personnel.id }})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

### 2. تبويب الوسائل
```html
<div id="equipment" class="tab-content">
    <div class="action-buttons">
        <button class="btn btn-success" onclick="addEquipment()">
            <i class="fas fa-plus"></i> إضافة وسيلة
        </button>
    </div>
    
    <table class="data-table">
        <thead>
            <tr>
                <th>الرقم التسلسلي</th>
                <th>نوع الوسيلة</th>
                <th>رقم الراديو</th>
                <th>الحالة</th>
                <th>ملاحظات</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for equipment in unit_equipment %}
            <tr>
                <td>{{ equipment.serial_number }}</td>
                <td>{{ equipment.equipment_type }}</td>
                <td>{{ equipment.radio_number|default:"-" }}</td>
                <td>
                    <span class="status-badge status-{{ status.status }}">
                        {{ status.get_status_display }}
                    </span>
                </td>
                <td>{{ status.notes|default:"-" }}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="editEquipmentStatus({{ equipment.id }})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <a href="/vehicle-crew-assignment/?unit={{ selected_unit.id }}&vehicle={{ equipment.id }}" 
                       class="btn btn-warning btn-sm">
                        <i class="fas fa-users"></i> توزيع
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

### 3. تبويب الفرق
```html
<div id="shifts" class="tab-content">
    <div class="action-buttons">
        <button class="btn btn-success" onclick="createShift()">
            <i class="fas fa-plus"></i> إنشاء فرقة
        </button>
        <button class="btn btn-primary" onclick="setActiveShift()">
            <i class="fas fa-clock"></i> تعيين فرقة عاملة
        </button>
    </div>
    
    <div class="alert alert-info">
        <strong>الفرقة العاملة اليوم:</strong>
        {% if daily_schedule.active_shift %}
            {{ daily_schedule.active_shift.get_name_display }} ({{ daily_schedule.active_shift.get_shift_type_display }})
        {% else %}
            لم يتم تحديد فرقة عاملة لهذا اليوم
        {% endif %}
    </div>
    
    <table class="data-table">
        <thead>
            <tr>
                <th>اسم الفرقة</th>
                <th>نوع النظام</th>
                <th>عدد الأعوان</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for shift in available_shifts %}
            <tr>
                <td>{{ shift.get_name_display }}</td>
                <td>{{ shift.get_shift_type_display }}</td>
                <td>{{ shift.personnel.count }}</td>
                <td>
                    {% if shift.is_active %}
                        <span class="status-badge status-operational">نشط</span>
                    {% else %}
                        <span class="status-badge status-maintenance">غير نشط</span>
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="manageShiftPersonnel({{ shift.id }})">
                        <i class="fas fa-users"></i> إدارة الأعوان
                    </button>
                    {% if daily_schedule.active_shift.id != shift.id %}
                    <button class="btn btn-warning btn-sm" onclick="activateShift({{ shift.id }})">
                        <i class="fas fa-play"></i> تفعيل
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

### 4. تبويب نظام 8 ساعات
```html
<div id="eight-hours" class="tab-content">
    <div class="action-buttons">
        <button class="btn btn-success" onclick="addEightHourPersonnel()">
            <i class="fas fa-plus"></i> إضافة عون نظام 8 ساعات
        </button>
    </div>
    
    <table class="data-table">
        <thead>
            <tr>
                <th>الاسم</th>
                <th>فترة العمل</th>
                <th>نوع المهمة</th>
                <th>وقت البداية</th>
                <th>وقت النهاية</th>
                <th>الحضور</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for record in eight_hour_personnel %}
            <tr>
                <td>{{ record.personnel.full_name }}</td>
                <td>{{ record.get_work_period_display }}</td>
                <td>{{ record.get_task_type_display }}</td>
                <td>{{ record.start_time }}</td>
                <td>{{ record.end_time }}</td>
                <td>
                    {% if record.is_present %}
                        <span class="status-badge status-present">حاضر</span>
                    {% else %}
                        <span class="status-badge status-absent">غائب</span>
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="editEightHourRecord({{ record.id }})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

## ⚡ الوظائف التفاعلية

### تبديل التبويبات
```javascript
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.nav-tab').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    document.getElementById(tabName).classList.add('active');
    
    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
}
```

### تفعيل الفرق
```javascript
function activateShift(shiftId) {
    if (confirm('هل أنت متأكد من تفعيل هذه الفرقة؟')) {
        fetch('/api/shift-management/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                action: 'set_active_shift',
                unit_id: unitId,
                shift_id: shiftId,
                date: currentDate
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}
```

## 🎨 التصميم المرئي

### نظام الألوان
```css
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}
```

### التخطيط المتجاوب
```css
.morning-check-container {
    max-width: 1600px;
    width: 100%;
    padding: 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
}

@media (max-width: 768px) {
    .controls-row {
        flex-direction: column;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
}
```

### الجداول التفاعلية
```css
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
}

.data-table th {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}
```

## 🔧 التكامل مع APIs

### إدارة الفرق
```javascript
// API endpoints
const API_ENDPOINTS = {
    shiftManagement: '/api/shift-management/',
    personnelStatus: '/api/personnel-status-update/',
    eightHourPersonnel: '/api/eight-hour-personnel/',
    readinessAlerts: '/api/readiness-alerts/'
};
```

### معالجة البيانات
```python
# في views.py
def morning_check_system_view(request):
    # الحصول على البيانات
    morning_summary = get_or_create_morning_summary(unit, date, user)
    available_shifts = get_available_shifts(unit)
    daily_schedule = get_daily_schedule(unit, date)
    eight_hour_personnel = get_eight_hour_personnel(unit, date)
    active_alerts = get_active_alerts(unit, date)
    
    # حساب الإحصائيات
    update_morning_summary_stats(morning_summary, personnel, equipment)
    
    return render(request, 'morning_check/index.html', context)
```

## 📱 الاستخدام العملي

### سير العمل اليومي
1. **اختيار الوحدة والتاريخ**
2. **مراجعة بطاقات الملخص**
3. **فحص التنبيهات النشطة**
4. **مراجعة حالة الأعوان**
5. **فحص حالة الوسائل**
6. **إدارة الفرق العاملة**
7. **متابعة نظام 8 ساعات**

### نصائح للاستخدام الأمثل
- **تحديث منتظم**: راجع البيانات كل صباح
- **متابعة التنبيهات**: اهتم بحل المشاكل فوراً
- **توثيق التغييرات**: سجل أي تعديلات مهمة
- **التدريب المستمر**: تأكد من فهم جميع الوظائف

## 🔮 المستقبل والتطوير

### المميزات المخططة
1. **الإشعارات الفورية**: تنبيهات على الهاتف
2. **التقارير التلقائية**: تقارير يومية مجدولة
3. **التكامل مع GPS**: تتبع مواقع الوسائل
4. **الذكاء الاصطناعي**: تنبؤ بالمشاكل

### التحسينات التقنية
1. **تحسين الأداء**: تسريع تحميل البيانات
2. **واجهة محسنة**: تجربة مستخدم أفضل
3. **المزيد من التخصيص**: إعدادات شخصية
4. **الأمان المعزز**: حماية أقوى للبيانات

## 🛠️ الأوامر الإدارية

### إعداد الفرق
```bash
# إعداد الفرق لجميع الوحدات
python manage.py setup_shifts

# إعداد الفرق لوحدة محددة
python manage.py setup_shifts --unit-id 11

# إعداد فرق نظام 8 ساعات فقط
python manage.py setup_shifts --shift-type eight_hour
```

### جدولة الفرق
```bash
# جدولة تلقائية لأسبوع
python manage.py auto_schedule_shifts --days 7

# جدولة لوحدة محددة
python manage.py auto_schedule_shifts --unit-id 11 --days 7

# جدولة من تاريخ محدد
python manage.py auto_schedule_shifts --start-date 2025-07-16 --days 7
```

### نظام 8 ساعات
```bash
# إعداد نظام 8 ساعات
python manage.py setup_eight_hour_system --unit-id 11 --days 3

# إعادة تعيين البيانات
python manage.py setup_eight_hour_system --unit-id 11 --reset --days 7
```

## 📋 الملفات والمكونات

### النماذج (Models)
- `home/models.py`: جميع النماذج الجديدة
- `WorkShift`, `ShiftPersonnel`, `DailyShiftSchedule`
- `EightHourPersonnel`, `ReadinessAlert`, `MorningCheckSummary`

### العروض (Views)
- `home/views.py`: morning_check_system_view
- `morning_check_dashboard_view`
- `shift_management_view`
- `eight_hour_personnel_view`
- `readiness_alerts_view`

### القوالب (Templates)
- `templates/morning_check/index.html`
- `templates/morning_check/dashboard.html`
- `templates/coordination_center/daily_unit_count.html` (محدث)

### الأوامر الإدارية
- `home/management/commands/setup_shifts.py`
- `home/management/commands/auto_schedule_shifts.py`
- `home/management/commands/setup_eight_hour_system.py`
