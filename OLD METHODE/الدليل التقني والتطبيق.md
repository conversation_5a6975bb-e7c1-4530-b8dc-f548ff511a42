# الدليل التقني والتطبيق - نظام Morning Check System

## 🔧 التقنيات المستخدمة

### Backend (الخادم)
- **Django 5.2.4**: إطار العمل الأساسي
- **Python 3.13**: لغة البرمجة
- **SQLite**: قاعدة البيانات (يمكن ترقيتها لـ PostgreSQL)
- **Django ORM**: للتعامل مع قاعدة البيانات

### Frontend (الواجهة)
- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **JavaScript (ES6+)**: التفاعلية
- **Bootstrap 4**: إطار العمل للتصميم المتجاوب
- **Font Awesome 6**: الأيقونات
- **Chart.js**: الرسوم البيانية

### أدوات إضافية
- **jQuery**: مكتبة JavaScript
- **AJAX**: للتحديث بدون إعادة تحميل الصفحة
- **Django Management Commands**: الأوامر الإدارية

## 📁 هيكل المشروع

```
dpcdz/
├── home/
│   ├── models.py                 # النماذج الجديدة
│   ├── views.py                  # العروض والمنطق
│   ├── admin.py                  # واجهة الإدارة
│   ├── urls.py                   # المسارات
│   └── management/
│       └── commands/
│           ├── setup_shifts.py
│           ├── auto_schedule_shifts.py
│           └── setup_eight_hour_system.py
├── templates/
│   ├── morning_check/
│   │   ├── index.html
│   │   └── dashboard.html
│   └── coordination_center/
│       └── daily_unit_count.html  # محدث
└── static/
    ├── css/
    │   ├── dpc-unified.css
    │   └── morning-check.css
    └── js/
        └── morning-check.js
```

## 🗄️ قاعدة البيانات

### النماذج الجديدة

#### 1. WorkShift (الفرق العاملة)
```python
class WorkShift(models.Model):
    SHIFT_TYPES = [
        ('24_48', 'نظام 24/48 ساعة'),
        ('eight_hour', 'نظام 8 ساعات'),
    ]
    
    SHIFT_NAMES = [
        ('A', 'فصيلة A'),
        ('B', 'فصيلة B'),
        ('C', 'فصيلة C'),
        ('morning', 'الفترة الصباحية'),
        ('evening', 'الفترة المسائية'),
        ('night', 'الفترة الليلية'),
    ]
    
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE)
    name = models.CharField(max_length=20, choices=SHIFT_NAMES)
    shift_type = models.CharField(max_length=20, choices=SHIFT_TYPES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

#### 2. EightHourPersonnel (أعوان نظام 8 ساعات)
```python
class EightHourPersonnel(models.Model):
    WORK_PERIODS = [
        ('morning', 'الفترة الصباحية (08:00-16:00)'),
        ('evening', 'الفترة المسائية (16:00-00:00)'),
        ('night', 'الفترة الليلية (00:00-08:00)'),
    ]
    
    TASK_TYPES = [
        ('administrative', 'أعمال إدارية'),
        ('maintenance', 'صيانة'),
        ('communications', 'اتصالات'),
        ('storage', 'إدارة المخزن'),
        ('security', 'حراسة وأمن'),
        ('support', 'دعم العمليات'),
        ('technical', 'أعمال تقنية'),
    ]
    
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE)
    personnel = models.ForeignKey(UnitPersonnel, on_delete=models.CASCADE)
    date = models.DateField()
    work_period = models.CharField(max_length=20, choices=WORK_PERIODS)
    task_type = models.CharField(max_length=20, choices=TASK_TYPES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_present = models.BooleanField(default=True)
```

#### 3. MorningCheckSummary (ملخص التحقق الصباحي)
```python
class MorningCheckSummary(models.Model):
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE)
    date = models.DateField()
    
    # إحصائيات الأعوان
    total_personnel = models.IntegerField(default=0)
    present_personnel = models.IntegerField(default=0)
    absent_personnel = models.IntegerField(default=0)
    on_mission_personnel = models.IntegerField(default=0)
    
    # إحصائيات الوسائل
    total_vehicles = models.IntegerField(default=0)
    ready_vehicles = models.IntegerField(default=0)
    under_maintenance_vehicles = models.IntegerField(default=0)
    not_ready_vehicles = models.IntegerField(default=0)
    
    # الجاهزية العامة
    overall_readiness_score = models.IntegerField(default=0)
    is_fully_ready = models.BooleanField(default=False)
    
    def calculate_readiness_score(self):
        # حساب نسبة الجاهزية
        vehicle_readiness = (self.ready_vehicles / max(self.total_vehicles, 1)) * 40
        personnel_readiness = (self.present_personnel / max(self.total_personnel, 1)) * 40
        assignment_readiness = 20  # افتراضي
        
        self.overall_readiness_score = int(vehicle_readiness + personnel_readiness + assignment_readiness)
        self.is_fully_ready = self.overall_readiness_score >= 90
```

## 🔄 APIs والمسارات

### URLs الجديدة
```python
# في home/urls.py
urlpatterns = [
    # النظام المستقل
    path('morning-check/', views.morning_check_system_view, name='morning_check_system'),
    
    # لوحة التحكم
    path('morning-check/dashboard/', views.morning_check_dashboard_view, name='morning_check_dashboard'),
    
    # إدارة الفرق
    path('api/shift-management/', views.shift_management_view, name='shift_management'),
    
    # نظام 8 ساعات
    path('api/eight-hour-personnel/', views.eight_hour_personnel_view, name='eight_hour_personnel'),
    
    # التنبيهات
    path('api/readiness-alerts/', views.readiness_alerts_view, name='readiness_alerts'),
]
```

### API Endpoints

#### 1. إدارة الفرق
```python
# POST /api/shift-management/
{
    "action": "set_active_shift",
    "unit_id": 11,
    "shift_id": 1,
    "date": "2025-07-16"
}

# Response
{
    "success": true,
    "message": "تم تفعيل الفرقة بنجاح",
    "active_shift": {
        "id": 1,
        "name": "فصيلة A",
        "shift_type": "نظام 24/48 ساعة"
    }
}
```

#### 2. نظام 8 ساعات
```python
# POST /api/eight-hour-personnel/
{
    "action": "add_personnel",
    "unit_id": 11,
    "personnel_id": 5,
    "work_period": "morning",
    "task_type": "administrative",
    "date": "2025-07-16"
}

# Response
{
    "success": true,
    "message": "تم إضافة العون لنظام 8 ساعات",
    "record_id": 15
}
```

#### 3. التنبيهات
```python
# POST /api/readiness-alerts/
{
    "action": "resolve_alert",
    "alert_id": 3,
    "resolution_notes": "تم حل المشكلة"
}

# Response
{
    "success": true,
    "message": "تم حل التنبيه بنجاح"
}
```

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
```bash
# Python packages
Django==5.2.4
Pillow>=8.0.0
python-dateutil>=2.8.0

# Database (اختياري للإنتاج)
psycopg2-binary>=2.8.0  # للـ PostgreSQL
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء الجداول الجديدة
python manage.py makemigrations home
python manage.py migrate

# إنشاء مستخدم إداري (إذا لم يكن موجوداً)
python manage.py createsuperuser
```

### 3. إعداد البيانات الأولية
```bash
# إعداد الفرق لجميع الوحدات
python manage.py setup_shifts

# جدولة الفرق لأسبوع
python manage.py auto_schedule_shifts --days 7

# إعداد نظام 8 ساعات للوحدات التي تحتوي على أعوان
python manage.py setup_eight_hour_system --days 7
```

### 4. تشغيل الخادم
```bash
# للتطوير
python manage.py runserver

# للإنتاج (مع gunicorn)
gunicorn dpcdz.wsgi:application --bind 0.0.0.0:8000
```

## 🔧 التخصيص والتطوير

### إضافة أنواع مهام جديدة
```python
# في models.py
class EightHourPersonnel(models.Model):
    TASK_TYPES = [
        ('administrative', 'أعمال إدارية'),
        ('maintenance', 'صيانة'),
        ('communications', 'اتصالات'),
        ('storage', 'إدارة المخزن'),
        ('security', 'حراسة وأمن'),
        ('support', 'دعم العمليات'),
        ('technical', 'أعمال تقنية'),
        ('new_task', 'مهمة جديدة'),  # إضافة جديدة
    ]
```

### تخصيص حساب الجاهزية
```python
def calculate_readiness_score(self):
    # يمكن تعديل النسب حسب الحاجة
    vehicle_weight = 0.4  # 40%
    personnel_weight = 0.4  # 40%
    assignment_weight = 0.2  # 20%
    
    vehicle_readiness = (self.ready_vehicles / max(self.total_vehicles, 1)) * 100 * vehicle_weight
    personnel_readiness = (self.present_personnel / max(self.total_personnel, 1)) * 100 * personnel_weight
    assignment_readiness = self.get_assignment_readiness() * assignment_weight
    
    return int(vehicle_readiness + personnel_readiness + assignment_readiness)
```

### إضافة تنبيهات مخصصة
```python
def create_custom_alert(unit, alert_type, title, description, priority='medium'):
    alert = ReadinessAlert.objects.create(
        unit=unit,
        date=date.today(),
        alert_type=alert_type,
        title=title,
        description=description,
        priority=priority,
        status='active'
    )
    return alert
```

## 📊 التقارير والإحصائيات

### تقرير يومي
```python
def generate_daily_report(unit, date):
    summary = MorningCheckSummary.objects.get(unit=unit, date=date)
    
    report = {
        'unit_name': unit.name,
        'date': date,
        'readiness_score': summary.overall_readiness_score,
        'personnel_stats': {
            'total': summary.total_personnel,
            'present': summary.present_personnel,
            'absent': summary.absent_personnel,
            'on_mission': summary.on_mission_personnel
        },
        'vehicle_stats': {
            'total': summary.total_vehicles,
            'ready': summary.ready_vehicles,
            'maintenance': summary.under_maintenance_vehicles,
            'not_ready': summary.not_ready_vehicles
        },
        'active_alerts': ReadinessAlert.objects.filter(
            unit=unit, date=date, status='active'
        ).count()
    }
    
    return report
```

### تقرير أسبوعي
```python
def generate_weekly_report(unit, start_date, end_date):
    summaries = MorningCheckSummary.objects.filter(
        unit=unit,
        date__range=[start_date, end_date]
    )
    
    avg_readiness = summaries.aggregate(
        avg_score=models.Avg('overall_readiness_score')
    )['avg_score'] or 0
    
    return {
        'unit_name': unit.name,
        'period': f"{start_date} - {end_date}",
        'average_readiness': round(avg_readiness, 1),
        'total_days': summaries.count(),
        'fully_ready_days': summaries.filter(is_fully_ready=True).count()
    }
```

## 🔒 الأمان والصلاحيات

### التحقق من الصلاحيات
```python
def check_unit_access(user, unit):
    """التحقق من صلاحية الوصول للوحدة"""
    if user.is_superuser:
        return True
    
    if hasattr(user, 'userprofile'):
        profile = user.userprofile
        
        if profile.role == 'admin':
            return True
        elif profile.role == 'wilaya_manager':
            return unit.wilaya == profile.wilaya
        elif profile.role in ['unit_manager', 'unit_coordinator']:
            return unit in profile.intervention_units.all()
    
    return False
```

### حماية APIs
```python
@login_required
@require_http_methods(["POST"])
def shift_management_view(request):
    data = json.loads(request.body)
    unit_id = data.get('unit_id')
    
    try:
        unit = InterventionUnit.objects.get(id=unit_id)
        
        # التحقق من الصلاحية
        if not check_unit_access(request.user, unit):
            return JsonResponse({
                'success': False,
                'message': 'ليس لديك صلاحية للوصول لهذه الوحدة'
            })
        
        # تنفيذ العملية
        # ...
        
    except InterventionUnit.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'الوحدة غير موجودة'
        })
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في إنشاء الفرق
```bash
# إذا فشل إنشاء الفرق
python manage.py setup_shifts --reset

# للتحقق من الفرق الموجودة
python manage.py shell -c "from home.models import WorkShift; print(WorkShift.objects.count())"
```

#### 2. مشكلة في حساب الجاهزية
```python
# في Django shell
from home.models import MorningCheckSummary
from datetime import date

summary = MorningCheckSummary.objects.get(unit_id=11, date=date.today())
summary.calculate_readiness_score()
summary.save()
print(f"نسبة الجاهزية: {summary.overall_readiness_score}%")
```

#### 3. مشكلة في التنبيهات
```python
# حذف التنبيهات القديمة
from home.models import ReadinessAlert
from datetime import date, timedelta

old_alerts = ReadinessAlert.objects.filter(
    date__lt=date.today() - timedelta(days=7)
)
old_alerts.delete()
```

## 📞 الدعم والمساعدة

### سجلات النظام
```python
# في settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'morning_check.log',
        },
    },
    'loggers': {
        'home.views': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### أوامر التشخيص
```bash
# فحص حالة النظام
python manage.py check

# فحص قاعدة البيانات
python manage.py dbshell

# عرض الجداول
python manage.py inspectdb | grep -E "(WorkShift|EightHour|MorningCheck)"
```
