# 🔄 **أمثلة APIs الجديدة - نظام التدخلات اليومية**
# تاريخ الإنشاء: 25 يوليو 2025
# الهدف: توضيح APIs الجديدة المطلوبة مع أمثلة عملية

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.shortcuts import get_object_or_404
from django.db import transaction
import json
from datetime import datetime

# استيراد النماذج الجديدة
from .models import (
    DailyIntervention, 
    InterventionCasualty,
    MedicalEvacuationDetail,
    TrafficAccidentDetail,
    BuildingFireDetail,
    AgriculturalFireDetail
)

# ========================================
# APIs الحفظ الجديدة
# ========================================

@csrf_exempt
@require_http_methods(["POST"])
def save_intervention_basic_data(request):
    """حفظ البيانات الأساسية للتدخل"""
    try:
        data = json.loads(request.body)
        
        with transaction.atomic():
            # إنشاء التدخل الأساسي
            intervention = DailyIntervention.objects.create(
                intervention_number=data['intervention_number'],
                intervention_type=data['intervention_type'],
                departure_time=data['departure_time'],
                caller_entity=data['caller_entity'],
                contact_type=data['contact_type'],
                phone_number=data.get('phone_number', ''),
                location=data['location'],
                initial_notes=data.get('initial_notes', ''),
                unit_id=data['unit_id'],
                date=data['date'],
                created_by=request.user
            )
            
            return JsonResponse({
                'success': True,
                'intervention_id': intervention.id,
                'intervention_number': intervention.intervention_number,
                'message': 'تم حفظ البيانات الأساسية بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ البيانات: {str(e)}'
        }, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def save_casualties_data(request):
    """حفظ بيانات الضحايا والوفيات"""
    try:
        data = json.loads(request.body)
        intervention_id = data['intervention_id']
        casualties_data = data['casualties']  # قائمة الضحايا
        
        intervention = get_object_or_404(DailyIntervention, id=intervention_id)
        
        with transaction.atomic():
            # حذف الضحايا السابقة (إذا كان تحديث)
            intervention.casualties.all().delete()
            
            # إضافة الضحايا الجديدة
            for casualty_data in casualties_data:
                InterventionCasualty.objects.create(
                    intervention=intervention,
                    casualty_type=casualty_data['casualty_type'],  # 'injured' أو 'fatality'
                    full_name=casualty_data['full_name'],
                    age=casualty_data['age'],
                    gender=casualty_data['gender'],
                    person_status=casualty_data.get('person_status'),  # للحوادث المرورية
                    notes=casualty_data.get('notes', '')
                )
            
            # تحديث العدد في التدخل الأساسي
            injured_count = intervention.casualties.filter(casualty_type='injured').count()
            deaths_count = intervention.casualties.filter(casualty_type='fatality').count()
            
            intervention.injured_count = injured_count
            intervention.deaths_count = deaths_count
            intervention.save()
            
            return JsonResponse({
                'success': True,
                'injured_count': injured_count,
                'deaths_count': deaths_count,
                'message': 'تم حفظ بيانات الضحايا بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ بيانات الضحايا: {str(e)}'
        }, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def save_medical_evacuation_details(request):
    """حفظ تفاصيل الإجلاء الصحي"""
    try:
        data = json.loads(request.body)
        intervention_id = data['intervention_id']
        
        intervention = get_object_or_404(DailyIntervention, id=intervention_id)
        
        with transaction.atomic():
            # إنشاء أو تحديث التفاصيل المتخصصة
            detail, created = MedicalEvacuationDetail.objects.get_or_create(
                intervention=intervention,
                defaults={
                    'evacuation_type': data['evacuation_type'],
                    'intervention_nature': data.get('intervention_nature', ''),
                    'support_request': data.get('support_request', 'none'),
                    'material_losses': data.get('material_losses', ''),
                    'saved_properties': data.get('saved_properties', ''),
                }
            )
            
            if not created:
                # تحديث البيانات الموجودة
                detail.evacuation_type = data['evacuation_type']
                detail.intervention_nature = data.get('intervention_nature', '')
                detail.support_request = data.get('support_request', 'none')
                detail.material_losses = data.get('material_losses', '')
                detail.saved_properties = data.get('saved_properties', '')
                detail.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم حفظ تفاصيل الإجلاء الصحي بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ التفاصيل: {str(e)}'
        }, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def save_traffic_accident_details(request):
    """حفظ تفاصيل حوادث المرور"""
    try:
        data = json.loads(request.body)
        intervention_id = data['intervention_id']
        
        intervention = get_object_or_404(DailyIntervention, id=intervention_id)
        
        with transaction.atomic():
            detail, created = TrafficAccidentDetail.objects.get_or_create(
                intervention=intervention,
                defaults={
                    'accident_type': data['accident_type'],
                    'road_type': data.get('road_type', ''),
                    'accident_nature': data.get('accident_nature', ''),
                    'material_damage': data.get('material_damage', ''),
                    'saved_properties': data.get('saved_properties', ''),
                }
            )
            
            if not created:
                detail.accident_type = data['accident_type']
                detail.road_type = data.get('road_type', '')
                detail.accident_nature = data.get('accident_nature', '')
                detail.material_damage = data.get('material_damage', '')
                detail.saved_properties = data.get('saved_properties', '')
                detail.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم حفظ تفاصيل حادث المرور بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ التفاصيل: {str(e)}'
        }, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def save_building_fire_details(request):
    """حفظ تفاصيل حرائق البنايات"""
    try:
        data = json.loads(request.body)
        intervention_id = data['intervention_id']
        
        intervention = get_object_or_404(DailyIntervention, id=intervention_id)
        
        with transaction.atomic():
            detail, created = BuildingFireDetail.objects.get_or_create(
                intervention=intervention,
                defaults={
                    'fire_nature': data['fire_nature'],
                    'fire_location': data.get('fire_location', ''),
                    'ignition_points_count': data.get('ignition_points_count', 0),
                    'wind_direction': data.get('wind_direction', ''),
                    'wind_speed': data.get('wind_speed'),
                    'population_threat': data.get('population_threat', False),
                    'population_evacuated': data.get('population_evacuated', False),
                    'assistance_to_residents': data.get('assistance_to_residents', ''),
                    'intervening_agents_count': data.get('intervening_agents_count', 0),
                    'affected_families_count': data.get('affected_families_count', 0),
                    'support_request': data.get('support_request', 'none'),
                    'fire_losses': data.get('fire_losses', ''),
                    'saved_properties': data.get('saved_properties', ''),
                }
            )
            
            if not created:
                # تحديث البيانات
                for field, value in data.items():
                    if hasattr(detail, field):
                        setattr(detail, field, value)
                detail.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم حفظ تفاصيل حريق البنايات بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ التفاصيل: {str(e)}'
        }, status=400)

@csrf_exempt
@require_http_methods(["POST"])
def save_agricultural_fire_details(request):
    """حفظ تفاصيل حرائق المحاصيل الزراعية"""
    try:
        data = json.loads(request.body)
        intervention_id = data['intervention_id']
        
        intervention = get_object_or_404(DailyIntervention, id=intervention_id)
        
        with transaction.atomic():
            detail, created = AgriculturalFireDetail.objects.get_or_create(
                intervention=intervention,
                defaults={
                    'fire_type': data['fire_type'],
                    'fire_sources_count': data.get('fire_sources_count', 0),
                    'wind_direction': data.get('wind_direction', ''),
                    'wind_speed': data.get('wind_speed'),
                    'population_threat': data.get('population_threat', False),
                    'evacuation_location': data.get('evacuation_location', ''),
                    'intervening_agents_count': data.get('intervening_agents_count', 0),
                    'affected_families_count': data.get('affected_families_count', 0),
                    'present_authorities': data.get('present_authorities', []),
                    'support_request': data.get('support_request', 'none'),
                    'area_losses': data.get('area_losses', ''),
                    'count_losses': data.get('count_losses', ''),
                    'saved_properties': data.get('saved_properties', ''),
                    'casualties_detail': data.get('casualties_detail', ''),
                }
            )
            
            if not created:
                # تحديث البيانات
                for field, value in data.items():
                    if hasattr(detail, field):
                        setattr(detail, field, value)
                detail.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم حفظ تفاصيل حريق المحاصيل بنجاح'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في حفظ التفاصيل: {str(e)}'
        }, status=400)

# ========================================
# APIs العرض الجديدة
# ========================================

@require_http_methods(["GET"])
def get_interventions_by_type(request, intervention_type):
    """جلب التدخلات حسب النوع مع جميع التفاصيل"""
    try:
        # تحديد التاريخ
        date_str = request.GET.get('date', datetime.now().strftime('%Y-%m-%d'))
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # تحديد نوع التدخل
        type_mapping = {
            'medical': 'medical',
            'traffic': 'traffic',
            'fire': 'building_fire',
            'crop': 'agricultural_fire'
        }
        
        db_type = type_mapping.get(intervention_type)
        if not db_type:
            return JsonResponse({'error': 'نوع تدخل غير صحيح'}, status=400)
        
        # جلب التدخلات مع جميع العلاقات
        interventions = DailyIntervention.objects.filter(
            intervention_type=db_type,
            date=date
        ).select_related(
            'medical_detail',
            'traffic_detail', 
            'building_fire_detail',
            'agricultural_fire_detail'
        ).prefetch_related('casualties', 'vehicles').order_by('-departure_time')
        
        # تحويل البيانات إلى JSON
        interventions_data = []
        for intervention in interventions:
            # البيانات الأساسية
            intervention_data = {
                'id': intervention.id,
                'intervention_number': intervention.intervention_number,
                'departure_time': intervention.departure_time.strftime('%H:%M'),
                'intervention_type': intervention.get_intervention_type_display(),
                'caller_entity': intervention.get_caller_entity_display(),
                'contact_type': intervention.get_contact_type_display(),
                'phone_number': intervention.phone_number or '-',
                'location': intervention.location,
                'arrival_time': intervention.arrival_time.strftime('%H:%M') if intervention.arrival_time else '-',
                'injured_count': intervention.injured_count,
                'deaths_count': intervention.deaths_count,
                'end_time': intervention.end_time.strftime('%H:%M') if intervention.end_time else '-',
                'status': intervention.status,
                'final_notes': intervention.final_notes or '-',
            }
            
            # بيانات الضحايا منسقة
            injured_details = []
            fatality_details = []
            
            for casualty in intervention.casualties.all():
                casualty_data = {
                    'name': casualty.full_name,
                    'age': casualty.age,
                    'gender': casualty.get_gender_display(),
                    'status': casualty.get_person_status_display() if casualty.person_status else '-'
                }
                
                if casualty.casualty_type == 'injured':
                    injured_details.append(casualty_data)
                elif casualty.casualty_type == 'fatality':
                    fatality_details.append(casualty_data)
            
            intervention_data['injured_details'] = injured_details
            intervention_data['fatality_details'] = fatality_details
            
            # الوسائل المرسلة
            vehicles = []
            for vehicle_relation in intervention.vehicles.all():
                vehicles.append(f"{vehicle_relation.vehicle.name} ({vehicle_relation.get_vehicle_role_display()})")
            intervention_data['vehicles_sent'] = ' | '.join(vehicles) if vehicles else '-'
            
            # التفاصيل المتخصصة
            if intervention_type == 'medical' and hasattr(intervention, 'medical_detail'):
                detail = intervention.medical_detail
                intervention_data.update({
                    'evacuation_type': detail.get_evacuation_type_display(),
                    'intervention_nature': detail.intervention_nature or '-',
                    'support_request': detail.get_support_request_display(),
                    'material_losses': detail.material_losses or '-',
                    'saved_properties': detail.saved_properties or '-',
                })
                
            elif intervention_type == 'traffic' and hasattr(intervention, 'traffic_detail'):
                detail = intervention.traffic_detail
                intervention_data.update({
                    'accident_type': detail.get_accident_type_display(),
                    'road_type': detail.get_road_type_display(),
                    'accident_nature': detail.accident_nature or '-',
                    'material_damage': detail.material_damage or '-',
                    'saved_properties': detail.saved_properties or '-',
                })
                
            elif intervention_type == 'fire' and hasattr(intervention, 'building_fire_detail'):
                detail = intervention.building_fire_detail
                intervention_data.update({
                    'fire_nature': detail.get_fire_nature_display(),
                    'fire_location': detail.fire_location or '-',
                    'ignition_points_count': detail.ignition_points_count,
                    'wind_direction': detail.wind_direction or '-',
                    'wind_speed': f"{detail.wind_speed} كم/سا" if detail.wind_speed else '-',
                    'population_threat': 'نعم' if detail.population_threat else 'لا',
                    'population_evacuated': 'نعم' if detail.population_evacuated else 'لا',
                    'assistance_to_residents': detail.assistance_to_residents or '-',
                    'intervening_agents_count': detail.intervening_agents_count,
                    'affected_families_count': detail.affected_families_count,
                    'support_request': detail.get_support_request_display(),
                    'fire_losses': detail.fire_losses or '-',
                    'saved_properties': detail.saved_properties or '-',
                })
                
            elif intervention_type == 'crop' and hasattr(intervention, 'agricultural_fire_detail'):
                detail = intervention.agricultural_fire_detail
                intervention_data.update({
                    'fire_type': detail.get_fire_type_display(),
                    'fire_sources_count': detail.fire_sources_count,
                    'wind_direction': detail.wind_direction or '-',
                    'wind_speed': f"{detail.wind_speed} كم/سا" if detail.wind_speed else '-',
                    'population_threat': 'نعم' if detail.population_threat else 'لا',
                    'evacuation_location': detail.evacuation_location or '-',
                    'intervening_agents_count': detail.intervening_agents_count,
                    'affected_families_count': detail.affected_families_count,
                    'present_authorities': detail.present_authorities,
                    'support_request': detail.get_support_request_display(),
                    'area_losses': detail.area_losses or '-',
                    'count_losses': detail.count_losses or '-',
                    'saved_properties': detail.saved_properties or '-',
                    'casualties_detail': detail.casualties_detail or '-',
                })
            
            interventions_data.append(intervention_data)
        
        return JsonResponse({
            'success': True,
            'interventions': interventions_data,
            'count': len(interventions_data)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في جلب البيانات: {str(e)}'
        }, status=500)

@require_http_methods(["GET"])
def get_intervention_details(request, intervention_id):
    """جلب تفاصيل تدخل محدد"""
    try:
        intervention = get_object_or_404(
            DailyIntervention.objects.select_related(
                'medical_detail', 'traffic_detail', 
                'building_fire_detail', 'agricultural_fire_detail'
            ).prefetch_related('casualties', 'vehicles'),
            id=intervention_id
        )
        
        # نفس منطق get_interventions_by_type لكن لتدخل واحد
        # ... (يمكن استخدام نفس الكود أعلاه)
        
        return JsonResponse({
            'success': True,
            'intervention': intervention_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في جلب التفاصيل: {str(e)}'
        }, status=500)
