# 🔄 **تعليمات إعادة بناء قاعدة البيانات - نظام التدخلات اليومية**

**تاريخ الإنشاء**: 25 يوليو 2025  
**الهدف**: إنشاء قاعدة بيانات نظيفة ومنظمة لنظام التدخلات اليومية  
**الأولوية**: عاجل جداً  

---

## 🚨 **المشكلة الحالية**

### **المشاكل المحددة:**
1. **خلط في البيانات**: الحقول لا تتطابق مع المتطلبات الفعلية
2. **أسماء حقول غير واضحة**: مثل `material_damage_notes` يُستخدم لأغراض متعددة
3. **بيانات مفقودة**: حقول مطلوبة غير موجودة في النماذج
4. **عدم تطابق**: بين JavaScript و APIs و النماذج

### **الحقول المشكوك فيها:**
- أسماء الضحايا، أعمار الضحايا، جنس الضحايا
- الحالة (سائق/راكب/مشاة)
- أسماء الوفيات، أعمار الوفيات، جنس الوفيات
- نوع الطريق، طبيعة الحادث
- الأملاك المنقذة

---

## 📋 **المتطلبات الكاملة لكل نوع تدخل**

### **🚑 1. جدول الإجلاء الصحي (24 حقل)**
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
8. موقع الحادث
 ملاحظة عن الخسائر المادية (اختياري)
10. نوع الإجلاء
 طبيعة التدخل
 طلب الدعم 
 ساعة نهاية التدخل 
12. أسماء المسعفين (كل اسم في سطر)
13. أعمار المسعفين (مقابل كل اسم)
14. جنس المسعفين (مقابل كل اسم)
15. عدد الوفيات
16. أسماء الوفيات (كل اسم في سطر)
17. أعمار الوفيات (مقابل كل اسم)
18. جنس الوفيات (مقابل كل اسم)
20. ملاحظات ختامية 
24. الحالة والإجراءات
```

### **🚗 2. جدول حوادث المرور (26 حقل)**
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)

نوع الحادث 
طبيعة الحادث 
طلب الدعم
ساعة الوصول 

12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
21. الخسائر المادية (مفصلة وواضحة)
11. نوع الطريق
 عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
ملاحظات ختامية

25. الحالة والإجراءات




```
### **🌾 4.  حريق محاصيل زراعية
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)

 نوع المحصول المحترق 
  عدد البؤر (الموقد)
 اتجاه الرياح
 سرعة الرياح (كم/سا)
 تهديد للسكان
 مكان إجلاء السكان (إن وُجد)
 عدد العائلات المتأثرة
 الجهات الحاضرة
طلب الدعم
 ساعة نهاية التدخل
 مدة التدخل الإجمالية  حساب توقيت الخروج الى ساعة نهاية التدخل 
 12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
 قمح واقف (هكتار)
 حصيدة (هكتار)
 شعير (هكتار)
 غابة/أحراش (هكتار)
 حزم تبن (عدد)
 أكياس قمح/شعير (عدد)
 أشجار مثمرة (عدد)
 خلايا نحل (عدد)
 مساحة منقذة (هكتار)
 حزم التبن المنقذة (عدد)
 ممتلكات أو آلات تم إنقاذها
📝 ملاحظات ختامية
27. الحالة والإجراءات
```

### ** 4. حرائق البنايات والمؤسسات
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)
 طبيعة الحريق
الموقع
 طابق معين (اختياري)
 غرفة محددة (اختياري)
 عدد نقاط الاشتعال
 جهة الرياح (إن وُجد)
 سرعة الرياح (كم/سا)
 تهديد السكان
 هل تم إجلاء السكان؟
 ماهية المساعدات المقدمة للسكان
21. طلب الدعم
ساعة نهاية التدخل
 مدة التدخل الإجمالية
 12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
 عدد العائلات المتضررة
 عدد الأعوان المتدخلين
 وصف الخسائر
 وصف الأملاك المنقذة
📝 ملاحظات ختامية

24. الحالة والإجراءات
```
## 📋 **المتطلبات الكاملة لكل نوع تدخل**

### **🚑 1. جدول الإجلاء الصحي (24 حقل)**
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
8. موقع الحادث
 ملاحظة عن الخسائر المادية (اختياري)
10. نوع الإجلاء
 طبيعة التدخل
 طلب الدعم 
 ساعة نهاية التدخل 
12. أسماء المسعفين (كل اسم في سطر)
13. أعمار المسعفين (مقابل كل اسم)
14. جنس المسعفين (مقابل كل اسم)
15. عدد الوفيات
16. أسماء الوفيات (كل اسم في سطر)
17. أعمار الوفيات (مقابل كل اسم)
18. جنس الوفيات (مقابل كل اسم)
20. ملاحظات ختامية 
24. الحالة والإجراءات
```

### **🚗 2. جدول حوادث المرور (26 حقل)**
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)

نوع الحادث 
طبيعة الحادث 
طلب الدعم
ساعة الوصول 

12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
21. الخسائر المادية (مفصلة وواضحة)
11. نوع الطريق
 عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
ملاحظات ختامية

25. الحالة والإجراءات




```
### **🌾 4.  حريق محاصيل زراعية
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)

 نوع المحصول المحترق 
  عدد البؤر (الموقد)
 اتجاه الرياح
 سرعة الرياح (كم/سا)
 تهديد للسكان
 مكان إجلاء السكان (إن وُجد)
 عدد العائلات المتأثرة
 الجهات الحاضرة
طلب الدعم
 ساعة نهاية التدخل
 مدة التدخل الإجمالية  حساب توقيت الخروج الى ساعة نهاية التدخل 
 12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
 قمح واقف (هكتار)
 حصيدة (هكتار)
 شعير (هكتار)
 غابة/أحراش (هكتار)
 حزم تبن (عدد)
 أكياس قمح/شعير (عدد)
 أشجار مثمرة (عدد)
 خلايا نحل (عدد)
 مساحة منقذة (هكتار)
 حزم التبن المنقذة (عدد)
 ممتلكات أو آلات تم إنقاذها
📝 ملاحظات ختامية
27. الحالة والإجراءات
```

### ** 4. حرائق البنايات والمؤسسات
```
1. معرف التدخل
2. توقيت الخروج
3. نوع التدخل
مكان التدخل
الوسائل المرسلة 
4. الجهة المتصلة
5. نوع الاتصال
6. رقم الهاتف
اسم المتصل
ملاحظة اضافية 
9. ساعة الوصول
 ملاحظة عن الخسائر المادية (اختياري)
 طبيعة الحريق
الموقع
 طابق معين (اختياري)
 غرفة محددة (اختياري)
 عدد نقاط الاشتعال
 جهة الرياح (إن وُجد)
 سرعة الرياح (كم/سا)
 تهديد السكان
 هل تم إجلاء السكان؟
 ماهية المساعدات المقدمة للسكان
21. طلب الدعم
ساعة نهاية التدخل
 مدة التدخل الإجمالية
 12. عدد الضحايا
13. أسماء الضحايا (كل اسم في سطر)
14. أعمار الضحايا (مقابل كل اسم)
15. جنس الضحايا (مقابل كل اسم)
16. الحالة (سائق/راكب/مشاة) (مقابل كل اسم)
17. عدد الوفيات
18. أسماء الوفيات (كل اسم في سطر)
19. أعمار الوفيات (مقابل كل اسم)
20. جنس الوفيات (مقابل كل اسم)
عدد التدخلات
يتم حسابه تلقائياً: الوسائل الأساسية + وسائل الدعم في البلاغ الاولي مع الدعم 
 عدد العائلات المتضررة
 عدد الأعوان المتدخلين
 وصف الخسائر
 وصف الأملاك المنقذة
📝 ملاحظات ختامية

24. الحالة والإجراءات
```


---

## � **متطلبات العرض الخاصة**

### **🔗 العرض المدمج للحقول:**

#### **1. الإجلاء الصحي:**
- **الحقل 10**: `تفاصيل التدخل` = `نوع الإجلاء` + `طبيعة التدخل` + `تفاصيل إضافية`
- **مثال**: "اختناق | حريق في المنزل | تم إنقاذ 3 أشخاص"

#### **2. حوادث المرور:**
- **الحقل 10**: `تفاصيل الحادث` = `نوع الحادث` + `طبيعة الحادث`
- **الحقل 11**: `نوع الطريق` (منفصل)
- **مثال**: "ضحايا تصادم المركبات | تصادم أمامي بين سيارتين"

#### **3. حرائق البنايات:**
- **الحقل 10**: `تفاصيل الحريق` = `طبيعة الحريق` + `موقع الحريق` + `تفاصيل إضافية`
- **الحقل 12**: `بيانات الرياح` = `اتجاه الرياح` + `سرعة الرياح`
- **مثال**: "حريق بناية مخصصة للسكن | الطابق الثاني | انتشر للطابق الثالث"

#### **4. حرائق المحاصيل:**
- **الحقل 10**: `نوع المحصول المحترق` (منفصل)
- **الحقل 11**: `الخسائر المقابلة` (في نفس السطر مع نوع المحصول)
- **الحقل 13**: `بيانات الرياح` = `اتجاه الرياح` + `سرعة الرياح`
- **مثال**:
  ```
  نوع المحصول: قمح واقف
  الخسائر: 50 هكتار + 200 طن + 500,000 دج
  ```

### **💡 ملاحظات مهمة للعرض:**
1. **استخدم الرمز "|"** لفصل العناصر المدمجة
2. **اعرض البيانات في نفس السطر** عند الطلب
3. **احتفظ بالحسابات واضحة** في الخسائر
4. **استخدم أسطر منفصلة** للأسماء والأعمار والجنس

---

## �🛠️ **خطة العمل للوكيل الجديد**

### **المرحلة 1: النسخ الاحتياطي (أولوية قصوى)**
```bash
# 1. إنشاء نسخة احتياطية كاملة
python manage.py dumpdata > backup_$(date +%Y%m%d_%H%M%S).json

# 2. نسخ احتياطي لقاعدة البيانات
cp db.sqlite3 db_backup_$(date +%Y%m%d_%H%M%S).sqlite3

# 3. نسخ احتياطي للملفات المهمة
cp -r dpcdz/home/<USER>
cp -r dpcdz/home/<USER>
```

### **المرحلة 2: تحليل البيانات الحالية**
1. **فحص النماذج الحالية**: تحديد الحقول الموجودة والمفقودة
2. **فحص البيانات المحفوظة**: تحديد البيانات القابلة للاستعادة
3. **تحديد التعارضات**: بين المتطلبات والتطبيق الحالي

### **المرحلة 3: تصميم النماذج الجديدة**

#### **أ. النموذج الأساسي: `DailyIntervention`**
```python
class DailyIntervention(models.Model):
    # الحقول الأساسية المشتركة
    intervention_number = models.CharField(max_length=50, unique=True)
    intervention_type = models.CharField(max_length=50, choices=INTERVENTION_TYPES)
    
    # بيانات البلاغ
    departure_time = models.TimeField()
    caller_entity = models.CharField(max_length=100)
    contact_type = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=20)
    location = models.CharField(max_length=200)
    
    # بيانات التعرف
    arrival_time = models.TimeField(null=True, blank=True)
    
    # بيانات الإنهاء
    end_time = models.TimeField(null=True, blank=True)
    final_notes = models.TextField(null=True, blank=True)
    
    # الحالة
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    
    # بيانات النظام
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
```

#### **ب. نماذج الضحايا والوفيات**
```python
class InterventionCasualty(models.Model):
    """نموذج موحد للضحايا والوفيات"""
    CASUALTY_TYPES = [
        ('injured', 'مسعف'),
        ('fatality', 'وفاة'),
    ]
    
    PERSON_STATUS = [
        ('driver', 'سائق'),
        ('passenger', 'راكب'),
        ('pedestrian', 'مشاة'),
        ('resident', 'مقيم'),
        ('worker', 'عامل'),
        ('other', 'أخرى'),
    ]
    
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE, related_name='casualties')
    casualty_type = models.CharField(max_length=20, choices=CASUALTY_TYPES)
    
    # بيانات شخصية
    full_name = models.CharField(max_length=100)
    age = models.IntegerField()
    gender = models.CharField(max_length=10, choices=[('male', 'ذكر'), ('female', 'أنثى')])
    
    # حالة الشخص (للحوادث)
    person_status = models.CharField(max_length=20, choices=PERSON_STATUS, null=True, blank=True)
    
    # ملاحظات إضافية
    notes = models.TextField(null=True, blank=True)
```

#### **ج. النماذج المتخصصة الجديدة**

**1. نموذج الإجلاء الصحي:**
```python
class MedicalEvacuationDetail(models.Model):
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='medical_detail')
    
    # نوع الإجلاء
    evacuation_type = models.CharField(max_length=100)
    
    # طبيعة التدخل
    intervention_nature = models.CharField(max_length=200)
    
    # طلب الدعم
    support_request = models.CharField(max_length=200, null=True, blank=True)
    
    # الخسائر
    material_losses = models.TextField(null=True, blank=True)
    
    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True)
```

**2. نموذج حوادث المرور:**
```python
class TrafficAccidentDetail(models.Model):
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='traffic_detail')
    
    # نوع الحادث
    accident_type = models.CharField(max_length=100)
    
    # نوع الطريق
    road_type = models.CharField(max_length=50, choices=ROAD_TYPES)
    
    # طبيعة الحادث
    accident_nature = models.CharField(max_length=200)
    
    # الخسائر المادية
    material_damage = models.TextField(null=True, blank=True)
    
    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True)
```

**3. نموذج حرائق البنايات:**
```python
class BuildingFireDetail(models.Model):
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='building_fire_detail')
    
    # طبيعة الحريق
    fire_nature = models.CharField(max_length=100)
    
    # موقع الحريق
    fire_location = models.CharField(max_length=200)
    
    # عدد نقاط الاشتعال
    ignition_points_count = models.IntegerField(default=0)
    
    # الرياح
    wind_direction = models.CharField(max_length=50, null=True, blank=True)
    wind_speed = models.FloatField(null=True, blank=True)
    
    # تهديد السكان
    population_threat = models.BooleanField(default=False)
    population_evacuated = models.BooleanField(default=False)
    
    # المساعدات للسكان
    assistance_to_residents = models.TextField(null=True, blank=True)
    
    # الأعوان والعائلات
    intervening_agents_count = models.IntegerField(default=0)
    affected_families_count = models.IntegerField(default=0)
    
    # طلب الدعم
    support_request = models.CharField(max_length=200, null=True, blank=True)
    
    # الخسائر والأملاك المنقذة
    fire_losses = models.TextField(null=True, blank=True)
    saved_properties = models.TextField(null=True, blank=True)
```

**4. نموذج حرائق المحاصيل:**
```python
class AgriculturalFireDetail(models.Model):
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='agricultural_fire_detail')
    
    # نوع الحريق
    fire_type = models.CharField(max_length=100)
    
    # عدد البؤر
    fire_sources_count = models.IntegerField(default=0)
    
    # الرياح
    wind_direction = models.CharField(max_length=50, null=True, blank=True)
    wind_speed = models.FloatField(null=True, blank=True)
    
    # تهديد السكان
    population_threat = models.BooleanField(default=False)
    evacuation_location = models.CharField(max_length=200, null=True, blank=True)
    
    # الأعوان والعائلات
    intervening_agents_count = models.IntegerField(default=0)
    affected_families_count = models.IntegerField(default=0)
    
    # الجهات الحاضرة
    present_authorities = models.JSONField(default=list, blank=True)
    
    # طلب الدعم
    support_request = models.CharField(max_length=200, null=True, blank=True)
    
    # الخسائر
    area_losses = models.TextField(null=True, blank=True)  # حسب المساحة
    count_losses = models.TextField(null=True, blank=True)  # حسب العدد
    
    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True)
    
    # تفصيل الضحايا
    casualties_detail = models.TextField(null=True, blank=True)
```

### **المرحلة 4: الهجرة والتطبيق**

#### **أ. إنشاء الهجرات**
```bash
# 1. إنشاء هجرة جديدة
python manage.py makemigrations --name rebuild_intervention_models

# 2. تطبيق الهجرة
python manage.py migrate
```

#### **ب. نقل البيانات**
```python
# سكريبت نقل البيانات من النماذج القديمة إلى الجديدة
def migrate_existing_data():
    # نقل البيانات الأساسية
    # نقل بيانات الضحايا
    # نقل التفاصيل المتخصصة
    pass
```

#### **ج. تحديث APIs**
```python
# تحديث جميع APIs لتتطابق مع النماذج الجديدة
def get_interventions_by_type(request, intervention_type):
    # استخدام النماذج الجديدة
    # إرجاع البيانات بالتنسيق الصحيح
    pass
```

#### **د. تحديث JavaScript**
```javascript
// تحديث دوال العرض لتتطابق مع البيانات الجديدة
function createTableRow(type, intervention) {
    // استخدام أسماء الحقول الجديدة
    // عرض البيانات بالتنسيق المطلوب
}
```

### **المرحلة 5: الاختبار والتحقق**

#### **أ. اختبار الحفظ**
- اختبار حفظ البيانات لكل نوع تدخل
- التأكد من حفظ الضحايا والوفيات بشكل صحيح
- اختبار التفاصيل المتخصصة

#### **ب. اختبار العرض**
- التأكد من عرض جميع الحقول المطلوبة
- اختبار تنسيق البيانات (الأسماء والأعمار في أسطر منفصلة)
- اختبار جميع أنواع التدخلات

#### **ج. اختبار الأداء**
- اختبار سرعة تحميل البيانات
- اختبار الاستعلامات المعقدة
- تحسين الفهارس إذا لزم الأمر

---

## ⚠️ **تحذيرات مهمة**

1. **لا تحذف البيانات القديمة** حتى التأكد من نجاح الهجرة
2. **اختبر على بيئة تطوير** قبل التطبيق على الإنتاج
3. **احتفظ بنسخ احتياطية متعددة** في مراحل مختلفة
4. **وثق جميع التغييرات** للرجوع إليها لاحقاً

---

## 📞 **نقاط الاتصال**

- **المطور الحالي**: تم توثيق جميع المشاكل والحلول
- **قاعدة البيانات**: تحتاج إعادة بناء كاملة
- **الأولوية**: عاجل جداً - النظام الحالي به خلط في البيانات

---

**🎯 الهدف النهائي**: نظام نظيف ومنظم يحفظ ويعرض جميع البيانات المطلوبة بدقة وبدون خلط أو تداخل.

---

## 🔧 **تفاصيل تقنية إضافية**

### **أ. أسماء الملفات المهمة:**
```
dpcdz/home/<USER>
dpcdz/home/<USER>
dpcdz/templates/coordination_center/daily_interventions.html    # نماذج الإدخال
dpcdz/templates/coordination_center/intervention_details.html   # جداول العرض
```

### **ب. APIs التي تحتاج إعادة كتابة:**
```python
# APIs الحفظ
save_agricultural_fire_details()
save_building_fire_details()
save_medical_evacuation_details()
save_traffic_accident_details()

# APIs العرض
get_interventions_by_type()
get_intervention_details()

# APIs الضحايا (جديدة)
save_casualty_details()
get_casualties_by_intervention()
```

### **ج. JavaScript Functions التي تحتاج تحديث:**
```javascript
// دوال الحفظ
saveAgriculturalFireDetails()
saveBuildingFireDetails()
saveMedicalEvacuationDetails()
saveTrafficAccidentDetails()

// دوال العرض
createTableRow()
formatPersonDetails()
formatSimpleList()

// دوال جديدة مطلوبة
saveCasualtyDetails()
loadCasualtiesData()
```

### **د. الحقول المشكوك فيها والحلول:**

#### **1. مشكلة الضحايا والوفيات:**
```
المشكلة الحالية:
- بيانات الضحايا مختلطة مع بيانات أخرى
- لا يوجد تمييز واضح بين المسعفين والوفيات
- الأعمار والأسماء لا تتطابق

الحل المقترح:
- نموذج موحد InterventionCasualty
- حقل casualty_type للتمييز
- ربط واضح بالتدخل الأساسي
```

#### **2. مشكلة الحقول المتعددة الاستخدام:**
```
المشكلة الحالية:
- material_damage_notes يُستخدم لأغراض متعددة
- present_authorities أحياناً نص وأحياناً JSON
- saved_properties غير موجود في بعض النماذج

الحل المقترح:
- حقل منفصل لكل غرض
- تنسيق موحد للبيانات (JSON للقوائم، TEXT للنصوص)
- أسماء واضحة ومحددة
```

#### **3. مشكلة التطابق بين النماذج:**
```
المشكلة الحالية:
- أسماء العلاقات غير متطابقة
- حقول مفقودة في بعض النماذج
- تضارب في أنواع البيانات

الحل المقترح:
- تسمية موحدة للعلاقات
- نفس الحقول الأساسية في جميع النماذج
- أنواع بيانات متسقة
```

### **هـ. خطة الاختبار التفصيلية:**

#### **1. اختبار البيانات الأساسية:**
```python
def test_basic_intervention_data():
    # اختبار إنشاء تدخل جديد
    # اختبار حفظ البيانات الأساسية
    # اختبار ربط الوسائل
    pass
```

#### **2. اختبار الضحايا والوفيات:**
```python
def test_casualties_data():
    # اختبار إضافة مسعف
    # اختبار إضافة وفاة
    # اختبار عرض البيانات منسقة
    pass
```

#### **3. اختبار النماذج المتخصصة:**
```python
def test_specialized_models():
    # اختبار كل نوع تدخل
    # اختبار الحقول المتخصصة
    # اختبار العلاقات
    pass
```

#### **4. اختبار العرض في الجداول:**
```python
def test_table_display():
    # اختبار عرض جميع الحقول
    # اختبار تنسيق البيانات
    # اختبار الأداء
    pass
```

### **و. سكريبت الهجرة المقترح:**

```python
# migration_script.py
from django.core.management.base import BaseCommand
from django.db import transaction

class Command(BaseCommand):
    help = 'Migrate existing intervention data to new models'

    def handle(self, *args, **options):
        with transaction.atomic():
            # 1. نسخ البيانات الأساسية
            self.migrate_basic_interventions()

            # 2. نسخ بيانات الضحايا
            self.migrate_casualties()

            # 3. نسخ التفاصيل المتخصصة
            self.migrate_specialized_details()

            # 4. التحقق من صحة البيانات
            self.verify_migration()

    def migrate_basic_interventions(self):
        # نقل البيانات من DailyIntervention القديم إلى الجديد
        pass

    def migrate_casualties(self):
        # استخراج بيانات الضحايا من الحقول المختلطة
        # إنشاء سجلات InterventionCasualty جديدة
        pass

    def migrate_specialized_details(self):
        # نقل التفاصيل المتخصصة لكل نوع تدخل
        pass

    def verify_migration(self):
        # التحقق من صحة البيانات المنقولة
        # مقارنة العدد قبل وبعد
        # اختبار العلاقات
        pass
```

### **ز. قائمة مراجعة للوكيل الجديد:**

#### **قبل البدء:**
- [ ] إنشاء نسخة احتياطية كاملة
- [ ] فهم المتطلبات بالتفصيل
- [ ] مراجعة النماذج الحالية
- [ ] تحديد البيانات القابلة للاستعادة

#### **أثناء التطوير:**
- [ ] إنشاء النماذج الجديدة
- [ ] كتابة سكريبت الهجرة
- [ ] تحديث APIs
- [ ] تحديث JavaScript
- [ ] تحديث القوالب

#### **بعد التطوير:**
- [ ] اختبار شامل لجميع الوظائف
- [ ] التحقق من صحة البيانات
- [ ] اختبار الأداء
- [ ] توثيق التغييرات
- [ ] تدريب المستخدمين

#### **قبل النشر:**
- [ ] اختبار على بيئة مشابهة للإنتاج
- [ ] نسخة احتياطية نهائية
- [ ] خطة الرجوع للخلف
- [ ] إشعار المستخدمين

---

## 📋 **ملخص المهام للوكيل الجديد**

### **المهمة الأساسية:**
إعادة بناء نظام التدخلات اليومية بقاعدة بيانات نظيفة ومنظمة تدعم جميع المتطلبات المحددة أعلاه.

### **المخرجات المطلوبة:**
1. **قاعدة بيانات جديدة** مع نماذج منظمة
2. **APIs محدثة** تدعم جميع العمليات
3. **واجهات محدثة** تعرض البيانات بالتنسيق المطلوب
4. **سكريبت هجرة** لنقل البيانات الحالية
5. **اختبارات شاملة** للتأكد من صحة النظام
6. **توثيق كامل** للتغييرات والاستخدام

### **المدة الزمنية المقترحة:**
- **التحليل والتصميم**: 1-2 أيام
- **التطوير**: 3-4 أيام
- **الاختبار**: 1-2 أيام
- **النشر والمتابعة**: 1 يوم

**المجموع**: 6-9 أيام عمل

---

**🚨 ملاحظة مهمة للوكيل الجديد:**
هذا المشروع يتطلب دقة عالية وتخطيط محكم. البيانات الحالية مهمة ويجب الحفاظ عليها. لا تتردد في طلب توضيحات إضافية أو مراجعة أي جزء من هذه التعليمات.
