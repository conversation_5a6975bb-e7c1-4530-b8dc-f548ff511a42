# 🧪 دليل اختبار النظام الجديد للتدخلات اليومية

## 📋 نظرة عامة

تم إنشاء نظام جديد ومبسط لحل جميع مشاكل النظام القديم:

### ✅ المشاكل التي تم حلها:
1. **اختلاط البيانات بين التدخلات**
2. **عدم تغيير النموذج حسب نوع التدخل**
3. **فتح النموذج تلقائياً عند تحديث الصفحة**
4. **خطأ "لم يتم تحديد التدخل"**
5. **عدم عمل إنهاء المهمة**

---

## 🔗 الوصول للنظام الجديد

**الرابط الجديد**: `http://127.0.0.1:8000/coordination-center/daily-interventions-new/`

**الرابط القديم**: `http://127.0.0.1:8000/coordination-center/daily-interventions/`

---

## 🧪 خطوات الاختبار

### 1. اختبار البلاغ الأولي ✅

```bash
# افتح الرابط الجديد
http://127.0.0.1:8000/coordination-center/daily-interventions-new/

# خطوات الاختبار:
1. انقر على زر "بلاغ أولي"
2. املأ البيانات التالية:
   - ساعة الخروج: 14:30
   - مكان الحادث: شارع الاستقلال
   - نوع التدخل: إجلاء صحي
   - نوع التدخل الفرعي: الاختناق
   - الجهة المتصلة: مواطن
   - نوع الاتصال: هاتفي
3. اختر وسيلة من الوسائل المتاحة
4. انقر "حفظ البلاغ الأولي"
5. تحقق من ظهور رسالة النجاح
6. تحقق من ظهور التدخل في الجدول
```

### 2. اختبار عملية التعرف 🔍

```bash
# بعد حفظ البلاغ الأولي:
1. انقر على زر "عملية التعرف" (الأيقونة الخضراء) في الجدول
2. تحقق من فتح النموذج تلقائياً
3. تحقق من ملء البيانات الأساسية
4. املأ البيانات الإضافية:
   - ساعة الوصول: 14:45
   - عدد المصابين: 1
   - عدد الوفيات: 0
   - ملاحظات الخسائر: "حالة اختناق بسيطة"
5. انقر "حفظ بيانات التعرف"
6. تحقق من تحديث حالة التدخل في الجدول
```

### 3. اختبار إنهاء المهمة 🏁

```bash
# بعد حفظ بيانات التعرف:
1. انقر على زر "إنهاء المهمة" (الأيقونة الزرقاء) في الجدول
2. تحقق من فتح النموذج تلقائياً
3. تحقق من ملء البيانات من المراحل السابقة
4. املأ البيانات النهائية:
   - ساعة انتهاء المهمة: 15:15
   - العدد النهائي للمصابين: 1
   - العدد النهائي للوفيات: 0
   - الملاحظات الختامية: "تم نقل المصاب للمستشفى بحالة مستقرة"
5. انقر "إنهاء المهمة"
6. تحقق من تحديث حالة التدخل إلى "منتهية"
```

### 4. اختبار تغيير نوع التدخل 🔄

```bash
# اختبار أنواع التدخل المختلفة:

## أ. حادث مرور:
1. افتح بلاغ أولي جديد
2. اختر "حادث مرور"
3. تحقق من ظهور الأنواع الفرعية المناسبة
4. تحقق من ظهور قسم تفاصيل حوادث المرور

## ب. حريق محاصيل زراعية:
1. افتح بلاغ أولي جديد
2. اختر "حريق محاصيل زراعية"
3. تحقق من ظهور الأنواع الفرعية المناسبة
4. تحقق من ظهور قسم تفاصيل الحرائق الزراعية

## ج. حرائق البنايات والمؤسسات:
1. افتح بلاغ أولي جديد
2. اختر "حرائق البنايات والمؤسسات"
3. تحقق من ظهور الأنواع الفرعية المناسبة
4. تحقق من ظهور قسم تفاصيل حرائق البنايات
```

### 5. اختبار عدم اختلاط البيانات 🧹

```bash
# اختبار مهم لضمان عدم اختلاط البيانات:

1. افتح بلاغ أولي لـ "إجلاء صحي"
2. املأ بعض البيانات (لا تحفظ)
3. أغلق النموذج
4. افتح بلاغ أولي لـ "حادث مرور"
5. تحقق من أن النموذج فارغ تماماً
6. تحقق من ظهور الأقسام المناسبة لحوادث المرور فقط
7. تحقق من عدم ظهور أي بيانات من الإجلاء الصحي
```

### 6. اختبار معالجة الأخطاء ⚠️

```bash
# اختبار التعامل مع الأخطاء:

## أ. اختبار حفظ بدون بيانات:
1. افتح بلاغ أولي
2. انقر "حفظ" بدون ملء البيانات المطلوبة
3. تحقق من ظهور رسالة خطأ واضحة

## ب. اختبار فقدان معرف التدخل:
1. افتح عملية التعرف لتدخل موجود
2. في Developer Console، اكتب: interventionsManager.clearCurrentInterventionId()
3. انقر "حفظ بيانات التعرف"
4. تحقق من ظهور رسالة خطأ واضحة

## ج. اختبار تحديث الصفحة:
1. افتح عملية التعرف لتدخل موجود
2. حدث الصفحة (F5)
3. تحقق من عدم فتح أي نموذج تلقائياً
4. تحقق من عمل الجدول بشكل طبيعي
```

---

## 🔧 أدوات التشخيص

### فتح Developer Console:
```bash
# في المتصفح:
F12 → Console

# أوامر مفيدة للتشخيص:
interventionsManager.getCurrentInterventionId()  # عرض معرف التدخل الحالي
interventionsManager.currentForm                 # عرض النموذج الحالي
interventionsManager.isFormOpen                  # هل يوجد نموذج مفتوح؟
```

### رسائل Console المتوقعة:
```bash
✅ تم تعيين معرف التدخل: 123
📝 فتح نموذج: reconnaissance للتدخل: 123
🧹 بدء مسح النموذج: reconnaissanceForm
💾 حفظ بيانات التعرف
📋 جمع بيانات التعرف للتدخل: 123
```

---

## 📊 معايير النجاح

### ✅ يعتبر الاختبار ناجحاً إذا:

1. **البلاغ الأولي**:
   - يتم حفظ البيانات بنجاح
   - يظهر التدخل في الجدول فوراً
   - تظهر رسالة نجاح واضحة

2. **عملية التعرف**:
   - يفتح النموذج مع البيانات الصحيحة
   - يتم حفظ البيانات بنجاح
   - تتحدث حالة التدخل في الجدول

3. **إنهاء المهمة**:
   - يفتح النموذج مع البيانات الصحيحة
   - يتم حفظ البيانات بنجاح
   - تتحدث حالة التدخل إلى "منتهية"

4. **تغيير النماذج**:
   - تظهر الأقسام المناسبة لكل نوع تدخل
   - لا تختلط البيانات بين الأنواع
   - تعمل الأنواع الفرعية بشكل صحيح

5. **معالجة الأخطاء**:
   - رسائل خطأ واضحة ومفيدة
   - لا يحدث crash في النظام
   - يمكن الاستمرار بعد الخطأ

---

## 🚨 في حالة وجود مشاكل

### إذا لم يعمل شيء:
1. تحقق من فتح الرابط الصحيح (daily-interventions-new)
2. تحقق من وجود أخطاء في Console
3. تحقق من تحميل ملفات JavaScript و CSS

### إذا لم تظهر البيانات:
1. تحقق من عمل APIs في Network tab
2. تحقق من وجود بيانات في قاعدة البيانات
3. تحقق من صلاحيات المستخدم

### إذا اختلطت البيانات:
1. تحقق من رسائل Console
2. تحقق من تنفيذ دوال المسح
3. أعد تحميل الصفحة وحاول مرة أخرى

---

## 📞 الدعم

في حالة وجود أي مشاكل، يرجى:
1. حفظ رسائل Console
2. تسجيل خطوات إعادة إنتاج المشكلة
3. أخذ لقطات شاشة للأخطاء
4. التواصل مع فريق التطوير
