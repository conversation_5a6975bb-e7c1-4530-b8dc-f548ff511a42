// 🔄 **أمثلة JavaScript للعرض المدمج - نظام التدخلات اليومية**
// تاريخ الإنشاء: 25 يوليو 2025
// الهدف: توضيح كيفية عرض البيانات المدمجة في الجداول

// ========================================
// دوال مساعدة للعرض المدمج
// ========================================

/**
 * دمج تفاصيل التدخل للإجلاء الصحي
 * @param {Object} intervention - بيانات التدخل
 * @returns {string} - النص المدمج
 */
function formatMedicalInterventionDetails(intervention) {
    const details = [];
    
    if (intervention.evacuation_type) {
        details.push(`نوع الإجلاء: ${intervention.evacuation_type}`);
    }
    
    if (intervention.intervention_nature) {
        details.push(`طبيعة التدخل: ${intervention.intervention_nature}`);
    }
    
    if (intervention.additional_details) {
        details.push(`تفاصيل إضافية: ${intervention.additional_details}`);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج تفاصيل الحادث لحوادث المرور
 * @param {Object} intervention - بيانات التدخل
 * @returns {string} - النص المدمج
 */
function formatTrafficAccidentDetails(intervention) {
    const details = [];
    
    if (intervention.accident_type) {
        details.push(`نوع الحادث: ${intervention.accident_type}`);
    }
    
    if (intervention.accident_nature) {
        details.push(`طبيعة الحادث: ${intervention.accident_nature}`);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج تفاصيل الحريق لحرائق البنايات
 * @param {Object} intervention - بيانات التدخل
 * @returns {string} - النص المدمج
 */
function formatBuildingFireDetails(intervention) {
    const details = [];
    
    if (intervention.fire_nature) {
        details.push(`طبيعة الحريق: ${intervention.fire_nature}`);
    }
    
    if (intervention.fire_location) {
        details.push(`موقع الحريق: ${intervention.fire_location}`);
    }
    
    if (intervention.additional_details) {
        details.push(`تفاصيل إضافية: ${intervention.additional_details}`);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج بيانات الرياح
 * @param {Object} intervention - بيانات التدخل
 * @returns {string} - النص المدمج
 */
function formatWindData(intervention) {
    const windInfo = [];
    
    if (intervention.wind_direction) {
        windInfo.push(`الاتجاه: ${intervention.wind_direction}`);
    }
    
    if (intervention.wind_speed) {
        windInfo.push(`السرعة: ${intervention.wind_speed} كم/سا`);
    }
    
    return windInfo.length > 0 ? windInfo.join(' | ') : '-';
}

/**
 * دمج نوع المحصول مع الخسائر في سطر واحد
 * @param {Object} intervention - بيانات التدخل
 * @returns {Object} - كائن يحتوي على النوع والخسائر
 */
function formatCropAndLosses(intervention) {
    return {
        crop_type: intervention.crop_type || '-',
        corresponding_losses: intervention.corresponding_losses || '-',
        combined: `${intervention.crop_type || '-'} | ${intervention.corresponding_losses || '-'}`
    };
}

/**
 * تنسيق بيانات الأشخاص (الأسماء والأعمار والجنس)
 * @param {Array} personsData - قائمة الأشخاص
 * @returns {Object} - كائن يحتوي على البيانات المنسقة
 */
function formatPersonDetails(personsData) {
    if (!personsData || personsData.length === 0) {
        return {
            names: '-',
            ages: '-',
            genders: '-',
            statuses: '-'
        };
    }
    
    return {
        names: personsData.map(p => p.name || '-').join('<br>'),
        ages: personsData.map(p => p.age || '-').join('<br>'),
        genders: personsData.map(p => p.gender || '-').join('<br>'),
        statuses: personsData.map(p => p.status || '-').join('<br>')
    };
}

/**
 * تنسيق قائمة بسيطة
 * @param {Array} dataArray - قائمة البيانات
 * @returns {string} - النص المنسق
 */
function formatSimpleList(dataArray) {
    if (!dataArray || dataArray.length === 0) {
        return '-';
    }
    return dataArray.join('<br>');
}

// ========================================
// دالة إنشاء صف الجدول الرئيسية
// ========================================

/**
 * إنشاء صف جدول للتدخل
 * @param {string} type - نوع التدخل
 * @param {Object} intervention - بيانات التدخل
 * @returns {string} - HTML للصف
 */
function createTableRow(type, intervention) {
    // البيانات الأساسية المشتركة
    let row = `
        <tr>
            <td>${intervention.intervention_number || '-'}</td>
            <td>${intervention.departure_time || '-'}</td>
            <td>${intervention.intervention_type || '-'}</td>
            <td>${intervention.caller_entity || '-'}</td>
            <td>${intervention.contact_type || '-'}</td>
            <td>${intervention.phone_number || '-'}</td>
            <td>${intervention.vehicles_sent || '-'}</td>
            <td>${intervention.location || '-'}</td>
            <td>${intervention.arrival_time || '-'}</td>
    `;
    
    // الأعمدة المتخصصة حسب نوع التدخل
    if (type === 'medical') {
        // تنسيق بيانات المسعفين والوفيات
        const injuredDetails = formatPersonDetails(intervention.injured_details || []);
        const fatalityDetails = formatPersonDetails(intervention.fatality_details || []);
        
        row += `
            <td>${formatMedicalInterventionDetails(intervention)}</td> <!-- تفاصيل التدخل المدمجة -->
            <td>${intervention.injured_count || 0}</td>
            <td>${injuredDetails.names}</td>
            <td>${injuredDetails.ages}</td>
            <td>${injuredDetails.genders}</td>
            <td>${intervention.deaths_count || 0}</td>
            <td>${fatalityDetails.names}</td>
            <td>${fatalityDetails.ages}</td>
            <td>${fatalityDetails.genders}</td>
            <td>${intervention.support_request || '-'}</td>
            <td>${intervention.detailed_losses || '-'}</td>
            <td>${intervention.saved_properties || '-'}</td>
            <td>${intervention.final_notes || '-'}</td>
            <td>${intervention.end_time || '-'}</td>
            <td>${intervention.status || '-'}</td>
        `;
        
    } else if (type === 'traffic') {
        // تنسيق بيانات الضحايا والوفيات
        const victimDetails = formatPersonDetails(intervention.victims_details || []);
        const fatalityDetails = formatPersonDetails(intervention.fatalities_details || []);
        
        row += `
            <td>${formatTrafficAccidentDetails(intervention)}</td> <!-- تفاصيل الحادث المدمجة -->
            <td>${intervention.road_type || '-'}</td> <!-- نوع الطريق منفصل -->
            <td>${intervention.injured_count || 0}</td>
            <td>${victimDetails.names}</td>
            <td>${victimDetails.ages}</td>
            <td>${victimDetails.genders}</td>
            <td>${victimDetails.statuses}</td> <!-- الحالة (سائق/راكب/مشاة) -->
            <td>${intervention.deaths_count || 0}</td>
            <td>${fatalityDetails.names}</td>
            <td>${fatalityDetails.ages}</td>
            <td>${fatalityDetails.genders}</td>
            <td>${intervention.detailed_material_damage || '-'}</td>
            <td>${intervention.saved_properties || '-'}</td>
            <td>${intervention.final_notes || '-'}</td>
            <td>${intervention.end_time || '-'}</td>
            <td>${intervention.status || '-'}</td>
            <td>${intervention.support_request || '-'}</td>
        `;
        
    } else if (type === 'fire') {
        // تنسيق بيانات المسعفين والوفيات
        const injuredDetails = formatPersonDetails(intervention.injured_details || []);
        const fatalityDetails = formatPersonDetails(intervention.fatality_details || []);
        
        row += `
            <td>${formatBuildingFireDetails(intervention)}</td> <!-- تفاصيل الحريق المدمجة -->
            <td>${intervention.ignition_points_count || 0}</td>
            <td>${formatWindData(intervention)}</td> <!-- بيانات الرياح المدمجة -->
            <td>${intervention.population_threat ? 'نعم' : 'لا'}</td>
            <td>${intervention.population_evacuated ? 'نعم' : 'لا'}</td>
            <td>${intervention.assistance_to_residents || '-'}</td>
            <td>${intervention.intervening_agents_count || 0}</td>
            <td>${intervention.affected_families_count || 0}</td>
            <td>${intervention.injured_count || 0}</td>
            <td>${injuredDetails.names}</td>
            <td>${intervention.deaths_count || 0}</td>
            <td>${fatalityDetails.names}</td>
            <td>${intervention.support_request || '-'}</td>
            <td>${intervention.fire_losses || '-'}</td>
            <td>${intervention.saved_properties || '-'}</td>
            <td>${intervention.final_notes || '-'}</td>
            <td>${intervention.end_time || '-'}</td>
            <td>${intervention.status || '-'}</td>
        `;
        
    } else if (type === 'crop') {
        // تنسيق بيانات المحصول والخسائر
        const cropAndLosses = formatCropAndLosses(intervention);
        const presentAuthorities = formatSimpleList(intervention.present_authorities || []);
        
        row += `
            <td>${cropAndLosses.crop_type}</td> <!-- نوع المحصول -->
            <td>${cropAndLosses.corresponding_losses}</td> <!-- الخسائر في نفس السطر -->
            <td>${intervention.fire_sources_count || 0}</td>
            <td>${formatWindData(intervention)}</td> <!-- بيانات الرياح المدمجة -->
            <td>${intervention.population_threat ? 'نعم' : 'لا'}</td>
            <td>${intervention.evacuation_location || '-'}</td>
            <td>${intervention.intervening_agents_count || 0}</td>
            <td>${intervention.injured_count || 0}</td>
            <td>${intervention.deaths_count || 0}</td>
            <td>${intervention.affected_families_count || 0}</td>
            <td>${presentAuthorities}</td>
            <td>${intervention.support_request || '-'}</td>
            <td>${intervention.saved_properties || '-'}</td>
            <td>${intervention.casualties_detail || '-'}</td>
            <td>${intervention.final_notes || '-'}</td>
            <td>${intervention.end_time || '-'}</td>
            <td>${intervention.status || '-'}</td>
        `;
    }
    
    row += '</tr>';
    return row;
}

// ========================================
// دالة تحديث الجدول الرئيسية
// ========================================

/**
 * تحديث جدول التدخلات
 * @param {string} type - نوع التدخل
 * @param {Array} interventions - قائمة التدخلات
 */
function updateInterventionsTable(type, interventions) {
    const tableBody = document.querySelector(`#${type}-table tbody`);
    
    if (!tableBody) {
        console.error(`لم يتم العثور على جدول ${type}`);
        return;
    }
    
    // مسح المحتوى السابق
    tableBody.innerHTML = '';
    
    if (!interventions || interventions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="100%" class="text-center">لا توجد بيانات</td></tr>';
        return;
    }
    
    // إضافة الصفوف الجديدة
    interventions.forEach(intervention => {
        const row = createTableRow(type, intervention);
        tableBody.innerHTML += row;
    });
    
    console.log(`تم تحديث جدول ${type} بـ ${interventions.length} تدخل`);
}

// ========================================
// دوال مساعدة إضافية
// ========================================

/**
 * تحديث عداد التدخلات
 * @param {string} type - نوع التدخل
 * @param {number} count - العدد
 */
function updateInterventionCount(type, count) {
    const countElement = document.querySelector(`#${type}-count`);
    if (countElement) {
        countElement.textContent = count;
    }
}

/**
 * عرض رسالة خطأ
 * @param {string} message - رسالة الخطأ
 */
function showErrorMessage(message) {
    // استخدام نظام الرسائل الجديد
    showModernAlert(message, 'error');
}

/**
 * عرض رسالة نجاح
 * @param {string} message - رسالة النجاح
 */
function showSuccessMessage(message) {
    // استخدام نظام الرسائل الجديد
    showModernAlert(message, 'success');
}

// ========================================
// مثال على الاستخدام
// ========================================

/*
// مثال على كيفية استخدام الدوال أعلاه:

// 1. جلب البيانات من API
fetch('/api/interventions/medical/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateInterventionsTable('medical', data.interventions);
            updateInterventionCount('medical', data.interventions.length);
            showSuccessMessage('تم تحديث البيانات بنجاح');
        } else {
            showErrorMessage(data.message || 'حدث خطأ في جلب البيانات');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showErrorMessage('حدث خطأ في الاتصال بالخادم');
    });

// 2. مثال على بيانات تدخل
const sampleIntervention = {
    intervention_number: 'INT-2025-001',
    departure_time: '08:30',
    intervention_type: 'إجلاء صحي',
    evacuation_type: 'اختناق',
    intervention_nature: 'حريق في المنزل',
    additional_details: 'تم إنقاذ 3 أشخاص',
    injured_count: 2,
    injured_details: [
        {name: 'أحمد محمد', age: 35, gender: 'ذكر'},
        {name: 'فاطمة علي', age: 28, gender: 'أنثى'}
    ],
    deaths_count: 0,
    fatality_details: []
};

// 3. إنشاء صف للتدخل
const rowHTML = createTableRow('medical', sampleIntervention);
console.log(rowHTML);
*/
