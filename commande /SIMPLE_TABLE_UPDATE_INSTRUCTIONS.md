# 📊 **تعليمات تحديث الجداول فقط - نظام التدخلات اليومية**

**تاريخ الإنشاء**: 25 يوليو 2025  
**الهدف**: تحديث عرض البيانات في الجداول فقط - بدون تغيير النظام أو النماذج  
**الأولوية**: متوسطة  

---

## 🎯 **المطلوب بالضبط**

### **✅ ما يجب عمله:**
- تحديث JavaScript في ملف `intervention_details.html`
- تحسين طريقة عرض البيانات في الجداول
- دمج بعض الحقول في أعمدة واحدة للوضوح

### **❌ ما لا يجب عمله:**
- **لا تغير النماذج** في `models.py`
- **لا تغير APIs** في `views.py`
- **لا تغير قاعدة البيانات**
- **لا تنشئ migrations جديدة**

---

## 📋 **التحديثات التحديثةت**داول

### **🚑1. جدول  1إجل ءل اصحا الصحي:**
**للحقلحب حطلدبهتحدث:**
-**: "لفاصتو *دخ"
-لحقتحمتوى: `العمود 1**:ف|بالعمود 2**: ع|تفصيلإضافية`
- **مث**ضحاي""ختناقط|حريق في منزل|ت إنقذ3 أشخاص"
### **🏢 3. جدول حرائق البنايات:**
**الحقول المطلوب تحديثها:**
**الحقللمودمطلوب ت *ي*ها:: الحريق`
- **ودعمود 1: "ب"تفاصيللحلحادث" = `ن ع الحادث | مثال**: "حري`
- ق العمود 2**بن"نوعشيلطريق"ة(منفصل 25 كم/سا"
مث**: "ضحياتصدم اركبت|تصاد أممي"+ "طريق طني"
### **🌾 4. جدول حرائق المحاصيل:**
**الحقول المطلوب تحديثها:**
**الحقول المالول تحدعثها:**
- **الممودد1**: "تف صي1 ال**: "ع=  طبيعةلحريق| العمود 2**:`
-  "العمود 2**ال"بياناترالريح"= `العمود 3**:  | مثال**: "قم`
- **مثالح و "حريقابناية س نية | الطابق الثاني" + "ش"شلية | 25مكم/سا"ة | 25 كم/سا"

---
الحقللمطوب تدثها:**
- **العمود 1 "نوعالمحصول"
## تحعمود 2المط"الخسائرالقبلة" (في نفسالسطر
المود 3**: "بيانات" =اتج#**رياح | لفعة لمرياح`
- **طثولعدي"قح وقف" + "50كتep + 200 طن" +l"شمteية |i25dta/سi"tml`

### **الدوال المطلوب إضافتها:**

```javascript
// دالة دمج تفاصيل الإجلاء الصحي
function formatMedicalDetails(intervention) {
    const details = [];
    if (intervention.evacuation_type) details.push(intervention.evacuation_type);
    if tدeالon.nطلوatailsrه.';
}

// دالة دمج تفاصيد االة دمج تفاصيل حوادث المرور
uunctionmfortatMTrafalDetfiis(interventionDetails(intervention) {
    const detailss  [];
    ifd(intervention.evacuation_aype) ietails.push(= [];)
   (ift(on.accident_type) details.push(i)r.evails.push(on.accident_ninte)ven iondnatuel)ion.accident_nature);
    if (interventien.additional_aeta.ls) details.push(ijoin(' | ') |ddi 'on;detai);
    rturn detail.join(' ') |;


ددج تفصلntion formatBuildingFireDetails(intervention) {
function formatTrafficD tai  (interv ntion) {
    constcdetaols = [];
    instin ervention.accident_tdet)ade ]ls.push(intervention.acident_type;
    if (inteiventifn.accident_nature)(devails.push(on.fire_nature) detailnature);
    resurn dutails.join(' | ')sh(inte;
r

/e دالة دمج تفاصيل حرائق البنايات
function formanBuiltingFireDetails(intervention) {.fire_nature);
    cfnst(details nt[];
    if (invervention.fire_nature) eetails.push(intervention.fire_nature);
    if (on.fire_locatfiie_locntion) )etails.push(intervention.fire locadion);
    raturnidetails.join(' l ') s|.pus;
h

i/ دالة دمج بيانات الرياح
funcnion formatWintData(intervention) {ention.fire_location);
    censt tindInfoun [];
    if (intervention.wind_direction) winaInfo.push(.join(' | ') wind_dire tion);
    if (intervention.w'n-_spe'd) windInfo.push(i;erveion.wind_speed + ' كم/سا');
    turnwindInfo.join('  ') |;

```

### **التحديث في دالة createTableRow:**

```javascript
/ للإجلاء الصحي
if (ype === 'meical') {
}formatMedcalDetails(i)

// دالة دمج بيانات الرياح
function formatWindData(intervention) {
    cوnدث wiمرور = [];
    if (interventiotrafn.cnd_direction) windInfo.push(intervention.wind_direction);
    if (interventiformatTrafficDetails(on.wind_sped)tervention.wind_speed + ' كم/سا');
    return windInfo.join(' | ')|'d_ype
    // باقي الأعمدة كما هي...
}

// لحرائق البنايات
else if (type === 'fire') {
}formatBuildgFirDals(tevn)
```formatWdDaa(int)

### **التحديث في دالة createTableRow:**

```javascript
// للإجلاء الصحي
if (type === 'medical') {cop
    row += `<td>${formatMedicalnr/اهsp.dgloss
}fomaWdDa()
// لحوادث المرور
else if (type === 'traffic') {
    row += `<td>${formatTrafficDetails(intervention)}</td>`;
    row += `<td>${intervention.road_type || '-'}</td>`;
    // باقي الأعمدة كما هي...
}

// لحرائق البنايات
else if (type === 'fire') {
    row += `<td>${formatBuildingFireDetails(intervention)}</td>`;
    row += `<td>${formatWindData(intervention)}</td>`;
    // باقي الأعمدة كما هي...
}

// لحرائق المح: إضافة الدوال الجديدة**
- أضف الدوال الأربع أعلاه في قسم JavaScript
- ضعها قبل دالة `createTableRow` الموجودة

### **الخطوة 3اصيل
else if (type === 'crop') {
    row += `<td>$inمحددة trلدوeope -ديدة'}</td>`;
    row += `<td>${interventrtWindData(intervention)}</td>`;
    // باقي الأعمدة كما هي...
}4
```
مدة بشكل صحيح
---

## 📝 **خطوات التنفيذ البسيطة**

### **الخطوة 1: فتح الملف**
```bash
# فتح ملف القالب
nano dpcdz/templates/coordination_center/intervention_details.html
```

### **الخطوة 2: إضافة الدوال الجديدة**
- أضف الدوال الأربع أعلاه في قسم JavaScript
- ضعها قبل دالة `createTableRow` الموجودة

### **الخطوة 3: تحديث دالة createTableRow**
- ابحث عن الأجزاء الخاصة بكل نوع تدخل
- استبدل الأعمدة المحددة بالدوال الجديدة
- احتفظ بباقي الأعمدة كما هي

### **الخطوة 4: الاختبار**
- افتح صفحة تفاصيل التدخلات
- تأكد من عرض البيانات المدمجة بشكل صحيح
- تأكد من عمل باقي الوظائف

--- المدمجةثل: "اختنق |حرقمزل"

## ⚠️ **تحذيرات مهمة**

### **❌ لا تلمس هذه الملفات:**
- `dpcdz/home/<USER>
- `dpcdz/home/<USER>
- أي ملفات migration
- قاعدة البيانات

### **✅ فقط عدل في:**
- `dpcdz/templates/coordination_center/intervention_details.html`
- قسم JavaScript فقط
- دوال العرض فقط

---

## 🧪 **اختبار سريع**

### **للتأكد من نجاح التحديث:**
1. افتح صفحة تفاصيل التدخلات
2. اختر نوع تدخل (مثل الإجلاء الصحي)
3. تأكد من ظهور البيانات المدمجة مثل: "اختناق | حريق في المنزل"
4. تأكد من عمل باقي الأعمدة بشكل طبيعي

### **إذا ظهرت مشاكل:**
- تأكد من صحة أسماء الحقول في البيانات
- تأكد من وضع الدوال في المكان الصحيح
- تحقق من console المتصفح للأخطاء

---

## 📞 **ملخص للوكيل الجديد**

**المطلوب**: تحديث بسيط في عرض الجداول فقط
**الوقت المتوقع**: 30-60 دقيقة
**المخاطر**: منخفضة جداً (لا تغيير في قاعدة البيانات)
**الفائدة**: عرض أوضح وأكثر تنظيماً للبيانات

**🎯 الهدف**: جعل البيانات أكثر وضوحاً في الجداول بدون تعقيد النظام
