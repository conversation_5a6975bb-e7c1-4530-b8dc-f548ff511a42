# 🚨 **تقرير للوكيل التالي - مشاكل عاجلة في نظام التدخلات**

**تاريخ التقرير**: 23 يوليو 2025  
**المبلغ**: المستخدم النهائي  
**الأولوية**: عالية جداً 🔴  

---

## 📋 **المشاكل المكتشفة:**

### **🔴 المشكلة 1: عدم ظهور الوسائل المرسلة**
- **الموقع**: `http://127.0.0.1:8000/coordination-center/daily-interventions/`
- **الوصف**: الجهة المتصلة تظهر، لكن الوسائل المرسلة لا تظهر في الجدول
- **السبب المحتمل**: عدم ربط الوسائل بالتدخل أو مشكلة في عرض البيانات

### **🔴 المشكلة 2: بيانات مفقودة في صفحة التفاصيل**
- **الموقع**: `http://127.0.0.1:8000/coordination-center/intervention-details/`
- **الوصف**: لا تظهر الجهة المتصلة أو الوسائل المرسلة
- **السبب المحتمل**: مشكلة في APIs أو في عرض البيانات

### **🔴 المشكلة 3: نموذج التعرف لا يحمل البيانات**
- **الوصف**: عند النقر على زر "التعرف" يطلب ملء نوع التدخل مرة أخرى
- **السبب**: النموذج لا يحمل البيانات المحفوظة من البلاغ الأولي

### **🔴 المشكلة 4: نماذج متخصصة مفقودة**
- **الوصف**: كل نوع تدخل يحتاج نموذج خاص به حسب `zoka.md`
- **المطلوب**: 4 نماذج متخصصة لكل نوع تدخل

---

## 🎯 **المطلوب من الوكيل التالي:**

### **1. إصلاح نظام الوسائل المرسلة** 🚒

#### **أ. فحص ربط الوسائل:**
```python
# في save_initial_report - التأكد من حفظ الوسائل
vehicle_ids = data.get('vehicle_ids', [])
for vehicle_id in vehicle_ids:
    InterventionVehicle.objects.create(
        intervention=intervention,
        vehicle=vehicle,
        is_primary=True
    )
```

#### **ب. إصلاح عرض الوسائل في الجدول:**
```html
<!-- في daily_interventions.html -->
<td>
    {% for vehicle in intervention.intervention_vehicles.all %}
        {{ vehicle.vehicle.equipment_type }} ({{ vehicle.vehicle.serial_number }})
        {% if not forloop.last %}, {% endif %}
    {% endfor %}
</td>
```

#### **ج. تحديث API get_available_vehicles:**
- التأكد من أن API يعمل بشكل صحيح
- ربط الوسائل المختارة بالتدخل
- تحديث حالة الوسائل إلى "في تدخل"

### **2. إصلاح صفحة التفاصيل** 📊

#### **أ. تحديث APIs:**
```python
# في get_interventions_by_type - إضافة الحقول المفقودة
intervention_data = {
    'id': intervention.id,
    'contact_source': intervention.get_contact_source_display(),
    'contact_type': intervention.get_contact_type_display(),
    'phone_number': intervention.phone_number,
    'caller_name': intervention.caller_name,
    'vehicles': [
        {
            'equipment_type': v.vehicle.equipment_type,
            'serial_number': v.vehicle.serial_number,
            'radio_number': v.vehicle.radio_number
        }
        for v in intervention.intervention_vehicles.all()
    ]
}
```

#### **ب. تحديث عرض البيانات في الجداول:**
- إضافة عمود الجهة المتصلة
- إضافة عمود الوسائل المرسلة
- إضافة عمود رقم الهاتف

### **3. إنشاء النماذج المتخصصة** 📝

#### **أ. نماذج عملية التعرف حسب النوع:**

##### **🚑 نموذج الإجلاء الصحي:**
```html
<form id="medical-reconnaissance-form">
    <!-- معلومات أساسية -->
    <input type="time" id="arrival-time" placeholder="ساعة الوصول">
    
    <!-- طبيعة التدخل -->
    <select id="medical-subtype">
        <option value="suffocation">الاختناق</option>
        <option value="poisoning">التسممات</option>
        <option value="burns">الحروق</option>
        <option value="explosion">الانفجارات</option>
        <option value="evacuation">إجلاء المرضى</option>
        <option value="drowning">الغرقى</option>
    </select>
    
    <!-- تفاصيل حسب النوع الفرعي -->
    <div id="suffocation-details" style="display:none">
        <select>
            <option value="natural-gas">بالغاز الطبيعي أو البوتان</option>
            <option value="co-gas">غاز CO</option>
            <option value="respiratory">انسداد المجاري التنفسية</option>
            <option value="closed-spaces">الأماكن المغلقة</option>
        </select>
    </div>
    
    <!-- إحصاء الضحايا -->
    <input type="number" id="injured-count" placeholder="عدد المسعفين">
    <input type="number" id="deaths-count" placeholder="عدد الوفيات">
    
    <!-- طلب الدعم -->
    <select id="support-request">
        <option value="none">شكراً، الوضع تحت السيطرة</option>
        <option value="additional-vehicle">نعم وسيلة إضافية</option>
        <option value="neighboring-unit">نعم وحدة مجاورة</option>
        <option value="specialized-team">نعم فريق متخصص</option>
    </select>
</form>
```

##### **🚗 نموذج حادث المرور:**
```html
<form id="accident-reconnaissance-form">
    <input type="time" id="arrival-time" placeholder="ساعة الوصول">
    
    <select id="accident-subtype">
        <option value="vehicle-hit">ضحايا مصدومة بالمركبات</option>
        <option value="collision">ضحايا تصادم المركبات</option>
        <option value="rollover">ضحايا إنقلاب</option>
        <option value="train-hit">ضحايا مصدومة بالقطار</option>
        <option value="other">حوادث أخرى</option>
    </select>
    
    <!-- تفاصيل حسب النوع -->
    <div id="vehicle-hit-details">
        <select>
            <option value="car">سيارة</option>
            <option value="truck">شاحنة</option>
            <option value="bus">حافلة</option>
            <option value="motorcycle">دراجة نارية</option>
            <option value="other">أخرى</option>
        </select>
    </div>
    
    <input type="number" id="injured-count" placeholder="عدد المسعفين">
    <input type="number" id="deaths-count" placeholder="عدد الوفيات">
    <textarea id="material-damage" placeholder="ملاحظة حول الخسائر المادية"></textarea>
</form>
```

##### **🏢 نموذج حرائق البنايات:**
```html
<form id="fire-reconnaissance-form">
    <input type="time" id="arrival-time" placeholder="ساعة الوصول">
    
    <select id="fire-subtype">
        <option value="residential">حرائق البنايات السكنية</option>
        <option value="industrial">حرائق المؤسسات المصنفة</option>
        <option value="public">حرائق الأماكن المستقبلة للجمهور</option>
        <option value="vehicles">حرائق المركبات</option>
    </select>
    
    <!-- انتشار الحريق -->
    <input type="number" id="fire-points" placeholder="عدد نقاط الاشتعال">
    <input type="text" id="wind-direction" placeholder="جهة الرياح">
    <input type="number" id="wind-speed" placeholder="سرعة الرياح (كم/سا)">
    
    <select id="population-threat">
        <option value="yes">نعم - تهديد السكان</option>
        <option value="no">لا - لا يوجد تهديد</option>
    </select>
    
    <select id="evacuation">
        <option value="yes">نعم - تم إجلاء السكان</option>
        <option value="no">لا - لم يتم الإجلاء</option>
    </select>
</form>
```

##### **🌾 نموذج حرائق المحاصيل:**
```html
<form id="crop-reconnaissance-form">
    <input type="time" id="arrival-time" placeholder="ساعة الوصول">
    
    <select id="crop-type">
        <option value="standing-wheat">قمح واقف</option>
        <option value="harvest">حصيدة</option>
        <option value="barley">شعير</option>
        <option value="straw-bales">حزم تبن</option>
        <option value="forest">غابة / أحراش</option>
        <option value="grain-bags">أكياس شعير / قمح</option>
        <option value="fruit-trees">أشجار مثمرة</option>
        <option value="beehives">خلايا نحل</option>
    </select>
    
    <input type="number" id="fire-sources" placeholder="عدد البؤر">
    <input type="text" id="wind-direction" placeholder="اتجاه الرياح">
    <input type="number" id="wind-speed" placeholder="سرعة الرياح (كم/سا)">
    <input type="number" id="affected-families" placeholder="عدد العائلات المتأثرة">
</form>
```

### **4. إنشاء نماذج إنهاء المهمة** ✅

#### **أ. نموذج إنهاء الإجلاء الصحي:**
```html
<form id="medical-completion-form">
    <input type="time" id="end-time" placeholder="ساعة نهاية التدخل">
    
    <!-- إحصائيات نهائية -->
    <div id="final-injured-section">
        <input type="number" id="final-injured-count" placeholder="عدد المسعفين النهائي">
        <div id="injured-details">
            <!-- يتم إضافة حقول ديناميكية لكل مسعف: الاسم، السن، الجنس -->
        </div>
    </div>
    
    <div id="final-deaths-section">
        <input type="number" id="final-deaths-count" placeholder="عدد الوفيات النهائي">
        <div id="deaths-details">
            <!-- يتم إضافة حقول ديناميكية لكل وفاة: الاسم، السن، الجنس -->
        </div>
    </div>
    
    <textarea id="final-notes" placeholder="ملاحظات ختامية"></textarea>
</form>
```

### **5. تحديث قاعدة البيانات** 🗄️

#### **أ. إضافة حقول جديدة لـ DailyIntervention:**
```python
class DailyIntervention(models.Model):
    # ... الحقول الموجودة
    
    # حقول عملية التعرف المتخصصة
    intervention_subtype = models.CharField(max_length=100, blank=True, null=True)
    subtype_details = models.JSONField(blank=True, null=True)  # لحفظ التفاصيل المتخصصة
    
    # حقول إضافية للحرائق
    fire_points_count = models.IntegerField(blank=True, null=True)
    wind_direction = models.CharField(max_length=50, blank=True, null=True)
    wind_speed = models.IntegerField(blank=True, null=True)
    population_threat = models.BooleanField(default=False)
    evacuation_performed = models.BooleanField(default=False)
    
    # حقول إضافية للمحاصيل
    affected_families_count = models.IntegerField(blank=True, null=True)
    crop_type = models.CharField(max_length=50, blank=True, null=True)
    
    # حقول الإحصائيات النهائية
    final_injured_details = models.JSONField(blank=True, null=True)
    final_deaths_details = models.JSONField(blank=True, null=True)
```

#### **ب. إنشاء نموذج للضحايا:**
```python
class InterventionCasualty(models.Model):
    CASUALTY_TYPES = [
        ('injured', 'مسعف'),
        ('death', 'وفاة'),
    ]
    
    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]
    
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE, related_name='casualties')
    casualty_type = models.CharField(max_length=20, choices=CASUALTY_TYPES)
    full_name = models.CharField(max_length=100)
    age = models.IntegerField()
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES)
    details = models.TextField(blank=True, null=True)  # تفاصيل الإصابة أو سبب الوفاة
    role = models.CharField(max_length=50, blank=True, null=True)  # سائق، راكب، مشاة، إلخ
```

### **6. تحديث الواجهات** 🖥️

#### **أ. تحديث صفحة التدخلات اليومية:**
- إصلاح عرض الوسائل المرسلة
- إضافة تحميل البيانات في نماذج التعرف
- ربط النماذج المتخصصة بنوع التدخل

#### **ب. تحديث صفحة التفاصيل:**
- إضافة عرض الجهة المتصلة والوسائل
- تحديث الجداول لتشمل جميع الأعمدة المطلوبة
- إضافة وظائف التصدير المحسنة

### **7. إنشاء JavaScript للنماذج الديناميكية** ⚙️

```javascript
// دالة لإظهار النموذج المناسب حسب نوع التدخل
function showSpecializedForm(interventionType, stage) {
    const formId = `${interventionType}-${stage}-form`;

    // إخفاء جميع النماذج
    document.querySelectorAll('.specialized-form').forEach(form => {
        form.style.display = 'none';
    });

    // إظهار النموذج المطلوب
    const targetForm = document.getElementById(formId);
    if (targetForm) {
        targetForm.style.display = 'block';

        // تحميل البيانات المحفوظة
        loadInterventionData(window.currentInterventionId, targetForm);
    }
}

// دالة لإضافة حقول ديناميكية للضحايا
function addCasualtyFields(type, count) {
    const container = document.getElementById(`${type}-details`);
    container.innerHTML = '';

    for (let i = 0; i < count; i++) {
        const fieldSet = document.createElement('div');
        fieldSet.className = 'casualty-fieldset';
        fieldSet.innerHTML = `
            <h6>${type === 'injured' ? 'مسعف' : 'وفاة'} ${i + 1}</h6>
            <input type="text" name="${type}_name_${i}" placeholder="الاسم الكامل" required>
            <input type="number" name="${type}_age_${i}" placeholder="السن" required>
            <select name="${type}_gender_${i}" required>
                <option value="">اختر الجنس</option>
                <option value="male">ذكر</option>
                <option value="female">أنثى</option>
            </select>
            <textarea name="${type}_details_${i}" placeholder="تفاصيل إضافية"></textarea>
        `;
        container.appendChild(fieldSet);
    }
}

// دالة لتحميل البيانات المحفوظة في النموذج
async function loadInterventionData(interventionId, form) {
    try {
        const response = await fetch(`/api/interventions/get-details/${interventionId}/`);
        const data = await response.json();

        if (data.success) {
            const intervention = data.intervention;

            // ملء الحقول الأساسية
            const interventionTypeSelect = form.querySelector('[id$="-intervention-type"]');
            if (interventionTypeSelect) {
                interventionTypeSelect.value = intervention.intervention_type;
            }

            const locationInput = form.querySelector('[id$="-location"]');
            if (locationInput) {
                locationInput.value = intervention.location;
            }

            const departureTimeInput = form.querySelector('[id$="-departure-time"]');
            if (departureTimeInput) {
                departureTimeInput.value = intervention.departure_time || '';
            }

            // ملء معلومات الاتصال
            const contactSourceSelect = form.querySelector('[id$="-contact-source"]');
            if (contactSourceSelect) {
                contactSourceSelect.value = intervention.contact_source;
            }

            const phoneNumberInput = form.querySelector('[id$="-phone-number"]');
            if (phoneNumberInput) {
                phoneNumberInput.value = intervention.phone_number || '';
            }

            const callerNameInput = form.querySelector('[id$="-caller-name"]');
            if (callerNameInput) {
                callerNameInput.value = intervention.caller_name || '';
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات التدخل:', error);
    }
}
```

---

## 📋 **أولويات العمل:**

### **🔴 عاجل (اليوم):**
1. إصلاح عرض الوسائل المرسلة في الجدول الرئيسي
2. إصلاح عرض البيانات في صفحة التفاصيل
3. إصلاح تحميل البيانات في نماذج التعرف

### **🟡 مهم (غداً):**
1. إنشاء النماذج المتخصصة لكل نوع تدخل
2. تحديث قاعدة البيانات بالحقول الجديدة
3. إنشاء نماذج إنهاء المهمة المتخصصة

### **🟢 مستقبلي:**
1. تحسين وظائف التصدير
2. إضافة إشعارات للتأخير
3. تحسين واجهة المستخدم

---

## 📁 **الملفات المطلوب تعديلها:**

1. **`dpcdz/home/<USER>
2. **`dpcdz/home/<USER>
3. **`dpcdz/templates/coordination_center/daily_interventions.html`** - إضافة النماذج المتخصصة
4. **`dpcdz/templates/coordination_center/intervention_details.html`** - تحديث عرض البيانات
5. **`dpcdz/home/<USER>

---

## 🧪 **خطوات الاختبار:**

1. **اختبار الوسائل**: إضافة تدخل مع وسائل والتأكد من ظهورها
2. **اختبار النماذج**: فتح نماذج التعرف والتأكد من تحميل البيانات
3. **اختبار التخصص**: اختبار النماذج المختلفة لكل نوع تدخل
4. **اختبار التكامل**: التأكد من حفظ البيانات في الجداول الصحيحة

---

## 🔧 **تفاصيل إضافية للتنفيذ:**

### **أ. تحديث save_initial_report لحفظ الوسائل:**
```python
@csrf_exempt
@require_http_methods(["POST"])
def save_initial_report(request):
    # ... الكود الموجود

    # حفظ الوسائل المختارة
    vehicle_ids = data.get('vehicle_ids', [])
    if vehicle_ids:
        from .models import UnitEquipment, InterventionVehicle

        for vehicle_id in vehicle_ids:
            try:
                vehicle = UnitEquipment.objects.get(id=vehicle_id)
                InterventionVehicle.objects.create(
                    intervention=intervention,
                    vehicle=vehicle,
                    is_primary=True
                )

                # تحديث حالة الوسيلة
                VehicleInterventionStatus.objects.update_or_create(
                    vehicle=vehicle,
                    date=date.today(),
                    defaults={
                        'status': 'in_intervention',
                        'current_intervention': intervention
                    }
                )
            except UnitEquipment.DoesNotExist:
                continue

    return JsonResponse({
        'success': True,
        'intervention_id': intervention.id,
        'intervention_number': intervention.intervention_number,
        'message': 'تم حفظ البلاغ الأولي بنجاح'
    })
```

### **ب. تحديث get_interventions_by_type لإضافة الوسائل:**
```python
def get_interventions_by_type(request):
    # ... الكود الموجود

    for intervention in interventions:
        # جلب الوسائل المرتبطة
        vehicles_data = []
        for iv in intervention.intervention_vehicles.all():
            vehicles_data.append({
                'equipment_type': iv.vehicle.equipment_type,
                'serial_number': iv.vehicle.serial_number,
                'radio_number': getattr(iv.vehicle, 'radio_number', ''),
            })

        interventions_data.append({
            'id': intervention.id,
            'time': departure_time_str,
            'intervention_type': intervention.intervention_type,
            'location': intervention.location,
            'contact_source': intervention.get_contact_source_display(),
            'contact_type': intervention.get_contact_type_display(),
            'phone_number': intervention.phone_number or '',
            'caller_name': intervention.caller_name or '',
            'vehicles': vehicles_data,
            'vehicles_count': len(vehicles_data),
            'status': intervention.status,
            'created_at': intervention.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            # ... باقي الحقول
        })
```

### **ج. إضافة APIs جديدة للنماذج المتخصصة:**
```python
@csrf_exempt
@require_http_methods(["POST"])
def save_reconnaissance_report(request):
    """حفظ تقرير عملية التعرف المتخصص"""
    try:
        data = json.loads(request.body) if request.content_type == 'application/json' else request.POST

        intervention_id = data.get('intervention_id')
        intervention = DailyIntervention.objects.get(id=intervention_id)

        # تحديث البيانات الأساسية
        intervention.arrival_time = data.get('arrival_time')
        intervention.intervention_subtype = data.get('intervention_subtype')
        intervention.injured_count = data.get('injured_count', 0)
        intervention.deaths_count = data.get('deaths_count', 0)
        intervention.status = 'intervention'

        # حفظ التفاصيل المتخصصة في JSON
        subtype_details = {}
        intervention_type = intervention.intervention_type

        if intervention_type == 'medical':
            subtype_details = {
                'medical_subtype': data.get('medical_subtype'),
                'subtype_details': data.get('subtype_details'),
                'support_request': data.get('support_request')
            }
        elif intervention_type == 'accident':
            subtype_details = {
                'accident_subtype': data.get('accident_subtype'),
                'vehicle_details': data.get('vehicle_details'),
                'material_damage': data.get('material_damage')
            }
        elif intervention_type == 'fire':
            intervention.fire_points_count = data.get('fire_points')
            intervention.wind_direction = data.get('wind_direction')
            intervention.wind_speed = data.get('wind_speed')
            intervention.population_threat = data.get('population_threat') == 'yes'
            intervention.evacuation_performed = data.get('evacuation') == 'yes'

            subtype_details = {
                'fire_subtype': data.get('fire_subtype'),
                'location_details': data.get('location_details')
            }
        elif intervention_type == 'crop':
            intervention.crop_type = data.get('crop_type')
            intervention.affected_families_count = data.get('affected_families')

            subtype_details = {
                'fire_sources': data.get('fire_sources'),
                'wind_direction': data.get('wind_direction'),
                'wind_speed': data.get('wind_speed')
            }

        intervention.subtype_details = subtype_details
        intervention.save()

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ تقرير التعرف بنجاح'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })
```

---

**🚨 ملاحظة مهمة للوكيل التالي:**
هذا نظام معقد يتطلب فهم عميق للمتطلبات. ابدأ بإصلاح المشاكل الأساسية أولاً، ثم انتقل للميزات المتقدمة. استخدم `zoka.md` كمرجع أساسي للنماذج والحقول المطلوبة.

**📚 مراجع مهمة:**
- `zoka.md` - المواصفات الكاملة للنماذج المتخصصة
- `Materiel_inv.md` - تفاصيل نظام الوسائل والتزامن
- `intervention_structure.md` - الهيكل العام للنظام

**🎯 الهدف النهائي:**
إنشاء نظام تدخلات متكامل مع نماذج متخصصة لكل نوع تدخل، مع ربط كامل للوسائل وعرض صحيح للبيانات في جميع الصفحات.
