# 🔄 **أمثلة عملية للنماذج الجديدة - نظام التدخلات اليومية**
# تاريخ الإنشاء: 25 يوليو 2025
# الهدف: توضيح النماذج الجديدة المقترحة بأمثلة عملية

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

# ========================================
# النماذج الأساسية
# ========================================

class Unit(models.Model):
    """نموذج الوحدة - موجود مسبقاً"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10)
    
    def __str__(self):
        return self.name

class DailyIntervention(models.Model):
    """النموذج الأساسي للتدخلات اليومية - محدث"""
    
    INTERVENTION_TYPES = [
        ('medical', 'إجلاء صحي'),
        ('traffic', 'حادث مرور'),
        ('building_fire', 'حرائق البنايات والمؤسسات'),
        ('agricultural_fire', 'حرائق المحاصيل الزراعية'),
    ]
    
    STATUS_CHOICES = [
        ('initial_report', 'بلاغ أولي'),
        ('reconnaissance', 'قيد التعرف'),
        ('intervention', 'عملية تدخل'),
        ('completed', 'منتهية'),
        ('escalated', 'مصعدة لكارثة كبرى'),
    ]
    
    CONTACT_TYPES = [
        ('phone', 'هاتف'),
        ('radio', 'راديو'),
        ('direct', 'مباشر'),
        ('other', 'أخرى'),
    ]
    
    CALLER_ENTITIES = [
        ('citizen', 'مواطن'),
        ('authority', 'سلطة'),
        ('police', 'شرطة'),
        ('gendarmerie', 'درك'),
        ('other_unit', 'وحدة أخرى'),
    ]
    
    # الحقول الأساسية المشتركة
    intervention_number = models.CharField(max_length=50, unique=True, verbose_name='رقم التدخل')
    intervention_type = models.CharField(max_length=50, choices=INTERVENTION_TYPES, verbose_name='نوع التدخل')
    
    # بيانات البلاغ
    departure_time = models.TimeField(verbose_name='توقيت الخروج')
    caller_entity = models.CharField(max_length=50, choices=CALLER_ENTITIES, verbose_name='الجهة المتصلة')
    contact_type = models.CharField(max_length=50, choices=CONTACT_TYPES, verbose_name='نوع الاتصال')
    phone_number = models.CharField(max_length=20, null=True, blank=True, verbose_name='رقم الهاتف')
    location = models.CharField(max_length=200, verbose_name='موقع الحادث')
    initial_notes = models.TextField(null=True, blank=True, verbose_name='ملاحظات أولية')
    
    # بيانات التعرف
    arrival_time = models.TimeField(null=True, blank=True, verbose_name='ساعة الوصول')
    injured_count = models.IntegerField(default=0, verbose_name='عدد المسعفين')
    deaths_count = models.IntegerField(default=0, verbose_name='عدد الوفيات')
    
    # بيانات الإنهاء
    end_time = models.TimeField(null=True, blank=True, verbose_name='توقيت الانتهاء')
    final_notes = models.TextField(null=True, blank=True, verbose_name='الملاحظات النهائية')
    
    # الحالة
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initial_report', verbose_name='الحالة')
    
    # بيانات النظام
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name='الوحدة')
    date = models.DateField(default=timezone.now, verbose_name='التاريخ')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='المنشئ')
    
    class Meta:
        verbose_name = 'تدخل يومي'
        verbose_name_plural = 'التدخلات اليومية'
        ordering = ['-date', '-departure_time']
    
    def __str__(self):
        return f"{self.intervention_number} - {self.get_intervention_type_display()}"

# ========================================
# نموذج الضحايا والوفيات الموحد
# ========================================

class InterventionCasualty(models.Model):
    """نموذج موحد للضحايا والوفيات"""
    
    CASUALTY_TYPES = [
        ('injured', 'مسعف'),
        ('fatality', 'وفاة'),
    ]
    
    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]
    
    PERSON_STATUS = [
        ('driver', 'سائق'),
        ('passenger', 'راكب'),
        ('pedestrian', 'مشاة'),
        ('resident', 'مقيم'),
        ('worker', 'عامل'),
        ('visitor', 'زائر'),
        ('other', 'أخرى'),
    ]
    
    # العلاقة مع التدخل
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE, related_name='casualties', verbose_name='التدخل')
    casualty_type = models.CharField(max_length=20, choices=CASUALTY_TYPES, verbose_name='نوع الضحية')
    
    # بيانات شخصية
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    age = models.IntegerField(verbose_name='العمر')
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name='الجنس')
    
    # حالة الشخص (مفيدة للحوادث المرورية)
    person_status = models.CharField(max_length=20, choices=PERSON_STATUS, null=True, blank=True, verbose_name='حالة الشخص')
    
    # ملاحظات إضافية
    notes = models.TextField(null=True, blank=True, verbose_name='ملاحظات')
    
    # بيانات النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')
    
    class Meta:
        verbose_name = 'ضحية'
        verbose_name_plural = 'الضحايا'
        ordering = ['casualty_type', 'full_name']
    
    def __str__(self):
        return f"{self.full_name} - {self.get_casualty_type_display()}"

# ========================================
# النماذج المتخصصة
# ========================================

class MedicalEvacuationDetail(models.Model):
    """نموذج تفاصيل الإجلاء الصحي - محدث"""

    EVACUATION_TYPES = [
        ('suffocation', 'اختناق'),
        ('poisoning', 'تسمم'),
        ('burns', 'حروق'),
        ('explosion', 'انفجار'),
        ('drowning', 'غرق'),
        ('other', 'أخرى'),
    ]

    SUPPORT_REQUESTS = [
        ('none', 'لا يوجد'),
        ('additional_vehicle', 'وسيلة إضافية'),
        ('neighboring_unit', 'وحدة مجاورة'),
        ('specialized_team', 'فريق متخصص'),
    ]

    # العلاقة مع التدخل الأساسي
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='medical_detail', verbose_name='التدخل')

    # تفاصيل التدخل المدمجة (نوع الإجلاء + طبيعة التدخل + تفاصيل إضافية)
    evacuation_type = models.CharField(max_length=50, choices=EVACUATION_TYPES, verbose_name='نوع الإجلاء')
    intervention_nature = models.CharField(max_length=200, null=True, blank=True, verbose_name='طبيعة التدخل')
    additional_details = models.TextField(null=True, blank=True, verbose_name='تفاصيل إضافية')

    # طلب الدعم
    support_request = models.CharField(max_length=50, choices=SUPPORT_REQUESTS, default='none', verbose_name='طلب الدعم')

    # الخسائر المفصلة والواضحة
    detailed_losses = models.TextField(null=True, blank=True, verbose_name='الخسائر المفصلة والواضحة')

    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True, verbose_name='الأملاك المنقذة')

    @property
    def intervention_details_combined(self):
        """دمج تفاصيل التدخل في نص واحد"""
        details = []
        if self.evacuation_type:
            details.append(f"نوع الإجلاء: {self.get_evacuation_type_display()}")
        if self.intervention_nature:
            details.append(f"طبيعة التدخل: {self.intervention_nature}")
        if self.additional_details:
            details.append(f"تفاصيل إضافية: {self.additional_details}")
        return " | ".join(details) if details else "-"
    
    class Meta:
        verbose_name = 'تفاصيل الإجلاء الصحي'
        verbose_name_plural = 'تفاصيل الإجلاء الصحي'
    
    def __str__(self):
        return f"إجلاء صحي - {self.intervention.intervention_number}"

class TrafficAccidentDetail(models.Model):
    """نموذج تفاصيل حوادث المرور - محدث"""

    ACCIDENT_TYPES = [
        ('vehicle_collision', 'ضحايا مصدومة بالمركبات'),
        ('vehicle_crash', 'ضحايا تصادم المركبات'),
        ('vehicle_rollover', 'ضحايا إنقلاب'),
        ('train_collision', 'ضحايا مصدومة بالقطار'),
        ('other', 'حوادث أخرى'),
    ]

    ROAD_TYPES = [
        ('highway', 'طريق سريع'),
        ('national', 'طريق وطني'),
        ('regional', 'طريق إقليمي'),
        ('local', 'طريق محلي'),
        ('urban', 'طريق حضري'),
    ]

    SUPPORT_REQUESTS = [
        ('none', 'لا يوجد'),
        ('additional_vehicle', 'وسيلة إضافية'),
        ('neighboring_unit', 'وحدة مجاورة'),
        ('specialized_team', 'فريق متخصص'),
    ]

    # العلاقة مع التدخل الأساسي
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='traffic_detail', verbose_name='التدخل')

    # تفاصيل الحادث المدمجة (نوع الحادث + طبيعة الحادث + نوع الطريق)
    accident_type = models.CharField(max_length=50, choices=ACCIDENT_TYPES, verbose_name='نوع الحادث')
    road_type = models.CharField(max_length=50, choices=ROAD_TYPES, verbose_name='نوع الطريق')
    accident_nature = models.CharField(max_length=200, null=True, blank=True, verbose_name='طبيعة الحادث')
    additional_details = models.TextField(null=True, blank=True, verbose_name='تفاصيل إضافية')

    # طلب الدعم
    support_request = models.CharField(max_length=50, choices=SUPPORT_REQUESTS, default='none', verbose_name='طلب الدعم')

    # الخسائر المادية المفصلة والواضحة
    detailed_material_damage = models.TextField(null=True, blank=True, verbose_name='الخسائر المادية المفصلة والواضحة')

    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True, verbose_name='الأملاك المنقذة')

    @property
    def accident_details_combined(self):
        """دمج تفاصيل الحادث في نص واحد"""
        details = []
        if self.accident_type:
            details.append(f"نوع الحادث: {self.get_accident_type_display()}")
        if self.accident_nature:
            details.append(f"طبيعة الحادث: {self.accident_nature}")
        if self.road_type:
            details.append(f"نوع الطريق: {self.get_road_type_display()}")
        if self.additional_details:
            details.append(f"تفاصيل إضافية: {self.additional_details}")
        return " | ".join(details) if details else "-"
    
    class Meta:
        verbose_name = 'تفاصيل حادث المرور'
        verbose_name_plural = 'تفاصيل حوادث المرور'
    
    def __str__(self):
        return f"حادث مرور - {self.intervention.intervention_number}"

class BuildingFireDetail(models.Model):
    """نموذج تفاصيل حرائق البنايات - محدث"""
    
    FIRE_NATURES = [
        ('residential', 'حريق بناية مخصصة للسكن'),
        ('classified_institution', 'حريق مؤسسة مصنفة'),
        ('public_place', 'حريق مكان مستقبل للجمهور'),
        ('vehicle', 'حريق مركبة'),
        ('shop_market', 'حريق محل أو سوق'),
    ]
    
    SUPPORT_REQUESTS = [
        ('none', 'لا يوجد'),
        ('additional_vehicle', 'وسيلة إضافية'),
        ('neighboring_unit', 'وحدة مجاورة'),
        ('specialized_team', 'فريق متخصص'),
    ]
    
    # العلاقة مع التدخل الأساسي
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='building_fire_detail', verbose_name='التدخل')
    
    # طبيعة الحريق
    fire_nature = models.CharField(max_length=50, choices=FIRE_NATURES, verbose_name='طبيعة الحريق')
    
    # موقع الحريق (تفاصيل إضافية)
    fire_location = models.CharField(max_length=200, null=True, blank=True, verbose_name='موقع الحريق')
    
    # عدد نقاط الاشتعال
    ignition_points_count = models.IntegerField(default=0, verbose_name='عدد نقاط الاشتعال')
    
    # بيانات الرياح
    wind_direction = models.CharField(max_length=50, null=True, blank=True, verbose_name='اتجاه الرياح')
    wind_speed = models.FloatField(null=True, blank=True, verbose_name='سرعة الرياح (كم/سا)')
    
    # تهديد السكان
    population_threat = models.BooleanField(default=False, verbose_name='تهديد السكان')
    population_evacuated = models.BooleanField(default=False, verbose_name='إجلاء السكان')
    
    # المساعدات للسكان
    assistance_to_residents = models.TextField(null=True, blank=True, verbose_name='المساعدات للسكان')
    
    # الأعوان والعائلات
    intervening_agents_count = models.IntegerField(default=0, verbose_name='عدد الأعوان المتدخلين')
    affected_families_count = models.IntegerField(default=0, verbose_name='عدد العائلات المتضررة')
    
    # طلب الدعم
    support_request = models.CharField(max_length=50, choices=SUPPORT_REQUESTS, default='none', verbose_name='طلب الدعم')
    
    # الخسائر والأملاك المنقذة
    fire_losses = models.TextField(null=True, blank=True, verbose_name='خسائر الحريق')
    saved_properties = models.TextField(null=True, blank=True, verbose_name='الأملاك المنقذة')
    
    class Meta:
        verbose_name = 'تفاصيل حريق البنايات'
        verbose_name_plural = 'تفاصيل حرائق البنايات'
    
    def __str__(self):
        return f"حريق بنايات - {self.intervention.intervention_number}"

class AgriculturalFireDetail(models.Model):
    """نموذج تفاصيل حرائق المحاصيل الزراعية - محدث"""

    CROP_TYPES = [
        ('standing_wheat', 'قمح واقف'),
        ('harvest', 'حصيدة'),
        ('barley', 'شعير'),
        ('straw_bales', 'حزم تبن'),
        ('forest_bushes', 'غابة / أحراش'),
        ('grain_bags', 'أكياس شعير / قمح'),
        ('fruit_trees', 'أشجار مثمرة'),
        ('beehives', 'خلايا نحل'),
    ]
    
    PRESENT_AUTHORITIES = [
        ('gendarmerie', 'درك'),
        ('police', 'شرطة'),
        ('forest_guards', 'حراس غابات'),
        ('local_authorities', 'سلطات محلية'),
        ('other', 'أخرى'),
    ]
    
    SUPPORT_REQUESTS = [
        ('none', 'لا يوجد'),
        ('additional_vehicle', 'وسيلة إضافية'),
        ('neighboring_unit', 'وحدة مجاورة'),
        ('specialized_team', 'فريق متخصص'),
    ]
    
    # العلاقة مع التدخل الأساسي
    intervention = models.OneToOneField(DailyIntervention, on_delete=models.CASCADE, related_name='agricultural_fire_detail', verbose_name='التدخل')

    # نوع المحصول المحترق (منفصل)
    crop_type = models.CharField(max_length=50, choices=CROP_TYPES, verbose_name='نوع المحصول المحترق')

    # الخسائر المقابلة (في نفس السطر مع نوع المحصول)
    corresponding_losses = models.TextField(null=True, blank=True, verbose_name='الخسائر المقابلة (المساحة + العدد + القيمة)')

    # عدد البؤر (الموقد)
    fire_sources_count = models.IntegerField(default=0, verbose_name='عدد البؤر (الموقد)')

    # بيانات الرياح (مدمجة)
    wind_direction = models.CharField(max_length=50, null=True, blank=True, verbose_name='اتجاه الرياح')
    wind_speed = models.FloatField(null=True, blank=True, verbose_name='سرعة الرياح (كم/سا)')

    # تهديد السكان
    population_threat = models.BooleanField(default=False, verbose_name='تهديد للسكان')
    evacuation_location = models.CharField(max_length=200, null=True, blank=True, verbose_name='مكان إجلاء السكان')

    # الأعوان والعائلات
    intervening_agents_count = models.IntegerField(default=0, verbose_name='عدد الأعوان المتدخلين')
    affected_families_count = models.IntegerField(default=0, verbose_name='عدد العائلات المتأثرة')

    # الجهات الحاضرة
    present_authorities = models.JSONField(default=list, blank=True, verbose_name='الجهات الحاضرة')

    # طلب الدعم
    support_request = models.CharField(max_length=50, choices=SUPPORT_REQUESTS, default='none', verbose_name='طلب الدعم')

    # الأملاك المنقذة
    saved_properties = models.TextField(null=True, blank=True, verbose_name='الأملاك المنقذة')

    # تفصيل الضحايا
    casualties_detail = models.TextField(null=True, blank=True, verbose_name='تفصيل الضحايا')

    @property
    def wind_data_combined(self):
        """دمج بيانات الرياح في نص واحد"""
        wind_info = []
        if self.wind_direction:
            wind_info.append(f"الاتجاه: {self.wind_direction}")
        if self.wind_speed:
            wind_info.append(f"السرعة: {self.wind_speed} كم/سا")
        return " | ".join(wind_info) if wind_info else "-"

    @property
    def crop_and_losses_combined(self):
        """دمج نوع المحصول مع الخسائر في سطر واحد"""
        crop_info = f"نوع المحصول: {self.get_crop_type_display()}"
        if self.corresponding_losses:
            crop_info += f" | الخسائر: {self.corresponding_losses}"
        return crop_info
    
    class Meta:
        verbose_name = 'تفاصيل حريق المحاصيل الزراعية'
        verbose_name_plural = 'تفاصيل حرائق المحاصيل الزراعية'
    
    def __str__(self):
        return f"حريق محاصيل - {self.intervention.intervention_number}"

# ========================================
# نماذج مساعدة (إذا لزم الأمر)
# ========================================

class InterventionVehicle(models.Model):
    """نموذج ربط الوسائل بالتدخلات - موجود مسبقاً لكن قد يحتاج تحديث"""
    
    VEHICLE_ROLES = [
        ('primary', 'وسيلة أساسية'),
        ('support', 'وسيلة دعم'),
        ('backup', 'وسيلة احتياطية'),
    ]
    
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE, related_name='vehicles', verbose_name='التدخل')
    # vehicle = models.ForeignKey(UnitEquipment, on_delete=models.CASCADE, verbose_name='الوسيلة')  # يحتاج تعريف UnitEquipment
    vehicle_role = models.CharField(max_length=20, choices=VEHICLE_ROLES, default='primary', verbose_name='دور الوسيلة')
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name='وقت التعيين')
    
    class Meta:
        verbose_name = 'وسيلة التدخل'
        verbose_name_plural = 'وسائل التدخل'
    
    def __str__(self):
        return f"{self.intervention.intervention_number} - {self.get_vehicle_role_display()}"
