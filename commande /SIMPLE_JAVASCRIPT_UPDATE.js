// 📊 **تحديث JavaScript بسيط للجداول - نظام التدخلات اليومية**
// تاريخ الإنشاء: 25 يوليو 2025
// الهدف: تحديث عرض البيانات في الجداول فقط

// ========================================
// الدوال الجديدة المطلوب إضافتها
// ========================================

/**
 * دمج تفاصيل الإجلاء الصحي
 */
function formatMedicalDetails(intervention) {
    const details = [];
    
    // نوع الإجلاء
    if (intervention.evacuation_type) {
        details.push(intervention.evacuation_type);
    }
    
    // طبيعة التدخل
    if (intervention.intervention_nature) {
        details.push(intervention.intervention_nature);
    }
    
    // تفاصيل إضافية (إذا وجدت)
    if (intervention.additional_details) {
        details.push(intervention.additional_details);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج تفاصيل حوادث المرور
 */
function formatTrafficDetails(intervention) {
    const details = [];
    
    // نوع الحادث
    if (intervention.accident_type) {
        details.push(intervention.accident_type);
    }
    
    // طبيعة الحادث
    if (intervention.accident_nature) {
        details.push(intervention.accident_nature);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج تفاصيل حرائق البنايات
 */
function formatBuildingFireDetails(intervention) {
    const details = [];
    
    // طبيعة الحريق
    if (intervention.fire_nature) {
        details.push(intervention.fire_nature);
    }
    
    // موقع الحريق
    if (intervention.fire_location) {
        details.push(intervention.fire_location);
    }
    
    return details.length > 0 ? details.join(' | ') : '-';
}

/**
 * دمج بيانات الرياح (للحرائق)
 */
function formatWindData(intervention) {
    const windInfo = [];
    
    // اتجاه الرياح
    if (intervention.wind_direction) {
        windInfo.push(intervention.wind_direction);
    }
    
    // سرعة الرياح
    if (intervention.wind_speed) {
        windInfo.push(intervention.wind_speed + ' كم/سا');
    }
    
    return windInfo.length > 0 ? windInfo.join(' | ') : '-';
}

// ========================================
// التحديث المطلوب في دالة createTableRow الموجودة
// ========================================

/*
ابحث عن دالة createTableRow في الملف واستبدل الأجزاء التالية:

// للإجلاء الصحي - استبدل هذا السطر:
// <td>${intervention.evacuation_type || '-'}</td>
// بهذا:
<td>${formatMedicalDetails(intervention)}</td>

// لحوادث المرور - استبدل هذين السطرين:
// <td>${intervention.accident_type || '-'}</td>
// <td>${intervention.road_type || '-'}</td>
// بهذين:
<td>${formatTrafficDetails(intervention)}</td>
<td>${intervention.road_type || '-'}</td>

// لحرائق البنايات - استبدل هذين السطرين:
// <td>${intervention.fire_nature || '-'}</td>
// <td>${intervention.wind_direction || '-'}</td>
// بهذين:
<td>${formatBuildingFireDetails(intervention)}</td>
<td>${formatWindData(intervention)}</td>

// لحرائق المحاصيل - أضف هذا السطر بعد نوع المحصول:
<td>${formatWindData(intervention)}</td>
*/

// ========================================
// مثال كامل للتحديث
// ========================================

/*
// هذا مثال على كيف ستبدو الأجزاء المحدثة في دالة createTableRow:

if (type === 'medical') {
    // ... الأعمدة الأساسية ...
    row += `<td>${formatMedicalDetails(intervention)}</td>`; // العمود المحدث
    // ... باقي الأعمدة كما هي ...
    
} else if (type === 'traffic') {
    // ... الأعمدة الأساسية ...
    row += `<td>${formatTrafficDetails(intervention)}</td>`; // العمود المحدث
    row += `<td>${intervention.road_type || '-'}</td>`; // نوع الطريق منفصل
    // ... باقي الأعمدة كما هي ...
    
} else if (type === 'fire') {
    // ... الأعمدة الأساسية ...
    row += `<td>${formatBuildingFireDetails(intervention)}</td>`; // العمود المحدث
    row += `<td>${formatWindData(intervention)}</td>`; // بيانات الرياح المدمجة
    // ... باقي الأعمدة كما هي ...
    
} else if (type === 'crop') {
    // ... الأعمدة الأساسية ...
    row += `<td>${intervention.crop_type || intervention.fire_type || '-'}</td>`; // نوع المحصول
    row += `<td>${intervention.corresponding_losses || intervention.area_losses || '-'}</td>`; // الخسائر
    row += `<td>${formatWindData(intervention)}</td>`; // بيانات الرياح المدمجة
    // ... باقي الأعمدة كما هي ...
}
*/

// ========================================
// ملاحظات للوكيل الجديد
// ========================================

/*
1. أضف الدوال الأربع أعلاه في بداية قسم JavaScript في الملف
2. ابحث عن دالة createTableRow الموجودة
3. استبدل فقط الأعمدة المحددة أعلاه
4. لا تغير أي شيء آخر في الملف
5. احتفظ بجميع الأعمدة الأخرى كما هي

أسماء الحقول المتوقعة من API:
- evacuation_type (نوع الإجلاء)
- intervention_nature (طبيعة التدخل)
- accident_type (نوع الحادث)
- accident_nature (طبيعة الحادث)
- road_type (نوع الطريق)
- fire_nature (طبيعة الحريق)
- fire_location (موقع الحريق)
- wind_direction (اتجاه الرياح)
- wind_speed (سرعة الرياح)
- crop_type أو fire_type (نوع المحصول)
- corresponding_losses أو area_losses (الخسائر)

إذا كانت أسماء الحقول مختلفة في API، عدل الدوال أعلاه لتتطابق مع الأسماء الفعلية.
*/

// ========================================
// اختبار سريع
// ========================================

/*
// لاختبار الدوال، يمكنك استخدام هذا المثال:

const testIntervention = {
    evacuation_type: 'اختناق',
    intervention_nature: 'حريق في المنزل',
    accident_type: 'ضحايا تصادم المركبات',
    accident_nature: 'تصادم أمامي',
    road_type: 'طريق وطني',
    fire_nature: 'حريق بناية سكنية',
    fire_location: 'الطابق الثاني',
    wind_direction: 'شمالية',
    wind_speed: 25
};

console.log('الإجلاء الصحي:', formatMedicalDetails(testIntervention));
console.log('حوادث المرور:', formatTrafficDetails(testIntervention));
console.log('حرائق البنايات:', formatBuildingFireDetails(testIntervention));
console.log('بيانات الرياح:', formatWindData(testIntervention));

// النتائج المتوقعة:
// الإجلاء الصحي: اختناق | حريق في المنزل
// حوادث المرور: ضحايا تصادم المركبات | تصادم أمامي
// حرائق البنايات: حريق بناية سكنية | الطابق الثاني
// بيانات الرياح: شمالية | 25 كم/سا
*/
