# 📋 تقرير مراحل التدخلات اليومية

واجهة شاملة لعرض كل التدخلات اليومية المصنفة حسب المراحل: **البلاغ الأولي، عملية التعرف الميداني، إنهاء المهمة**.  
تُعرض في صفحة واحدة بثلاثة أزرار رئيسية، مع إمكانية تصدير المعلومات.

---

## 🔘 الأزرار الثلاثة الرئيسية

1. ✅ **البلاغات الأولية**
2. 🔍 **مرحلة التعرف الميداني**
3. 🏁 **مرحلة إنهاء المهمة**

> كل زر يقوم بعرض جدول مخصص بمعلومات التدخلات المسجلة في مرحلته، ويحتوي على أزرار تصدير Excel و PDF.

---

## ✅ البلاغ الأولي

يعرض التدخلات التي تم فيها ملء معلومات البلاغ الأولي فقط (دون استكمال باقي المراحل).

| رقم التدخل | التاريخ | الساعة | نوع التدخل | الجهة المتصلة | رقم الهاتف | الوسائل المرسلة | الحالة |
|------------|---------|--------|--------------|----------------|-------------|------------------|---------|
| 00123      | 2025/07/20 | 21:05 | انقلاب جرار | الدرك الوطني | 0660xxxxxx | سيارة إسعاف 1 | قيد التعرف |

📥 **[تحميل جميع البلاغات الأولية Excel]**  
📄 **[تحميل PDF لتدخل معين]**

---

## 🔍 مرحلة التعرف الميداني

يعرض التدخلات التي تم فيها النزول الميداني وتوثيق الحالة والمعطيات من طرف رئيس العدد.

| رقم التدخل | تفاصيل الحادث | عدد الضحايا | طبيعة الإصابات | طلب دعم | الوحدة الداعمة | الوقت |
|------------|----------------|--------------|------------------|-----------|------------------|--------|
| 00123      | شخص متعدد الإصابات، فاقد للوعي | 1 | إصابة خطيرة على الحوض | لا | - | 21:20 |

📥 **[تحميل جميع تقارير التعرف Excel]**  
📄 **[تحميل PDF لتدخل معين]**

---

## 🏁 مرحلة إنهاء المهمة

يعرض نتائج التدخلات المنجزة، ويشمل الحالات الصحية النهائية ووسائل التدخل والملاحظات الختامية.

| رقم التدخل | أسماء الضحايا | أعمارهم | الحالة الصحية | النتائج | وسيلة التدخل | نهاية المهمة |
|------------|----------------|----------|------------------|-----------|------------------|----------------|
| 00123      | اسحاق واصل | 19 سنة | متوفي | تم نقله للمستشفى الجهوي كعرار السبتي | سيارة إسعاف 1 | 22:15 |

📥 **[تحميل جميع تقارير الإنهاء Excel]**  
📄 **[تحميل PDF لتدخل معين]**

---

## 🔄 التكامل والتنقل بين الصفحات

1. عند النقر على تدخل في "البلاغ الأولي" → ينتقل إلى صفحة "عملية التعرف" مع إمكانية استكمال البيانات.
2. في "مرحلة التعرف" → عند إتمام الإجراءات، ينقل مباشرة لمرحلة "إنهاء المهمة".
3. كل مرحلة تُظهر فقط التدخلات التي وصلت لتلك المرحلة (تمّت تعبئة بياناتها).

---

## 🎯 التصعيد وطلب الدعم

- من مرحلة "التعرف"، يمكن الضغط على:
  - 🔼 **زر طلب دعم من وحدة أخرى**
  - 🚨 **زر تصعيد كـ كارثة كبرى (مركز تنسيق ولائي)**
- تُسجل الوحدات الداعمة تلقائيًا وتربط بالخريطة عند العرض.

---

## ⚙️ ملاحظات فنية

- كل الجداول تعتمد على نفس جدول التدخلات في قاعدة البيانات.
- البيانات تتغير ديناميكيا حسب المرحلة الحالية للتدخل.
- أزرار التصدير تعمل عبر Backend API لإنشاء Excel و PDF مباشرة.
- يتم تمرير معرف التدخل عند التنقل لضمان الربط بين المراحل.

---

## 🔗 روابط تنقل

- ⬅️ [العودة إلى التدخلات اليومية](#/daily-interventions)
- 🧭 [عرض خريطة التنسيق الولائي](#/disaster-coordination)
- 👩‍🚒 [عرض العتاد والأفراد](#/equipment-assignment)
