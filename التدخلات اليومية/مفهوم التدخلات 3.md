# 📋 تطوير صفحة "جميع التدخلات" - تقرير التقدم
ساقوم بشرح ما اريده بالتفصيل 
التدخلات اليومية
في البلاغ الاول عند النقر على زر يظهر لملأ البيانات 
في البيانات هناك اشياء تستورد  الوسائل المرسلة
من صفحة http://127.0.0.1:8000/vehicle-readiness/ هو قام بانشاء قائمة من راسه اقرأ فقط الاشياء التي تحتاجها بهذا الخصوص 
قام بانشاء  الوحدة
 الوحدة حسب user profile مدير الوحدة و مدير الولاية هذه تظهر فقط لمير الولاية و admin 
 طلبت منه انشاء صفحة 📋 تقرير مراحل التدخلات اليومية.md
 هذه الصفحة هي مكملة لصفحة التدخلات اليومية  تظهر فقط الجداول و التحميل و الفلترة مع امكانية التنقل الى صفحة  التدخلات اليومية لاكمال عملية التعرف و انتهاء المهمة 
 في صفحة التدخلات هناك جدول مبسط لمتابعة التدخلات و الصفحة جميع التدخلات اليومية هي لعرض الجداول و كل ما تم ملأه في البلاغ الاولي و عملية التعرف و انتهاء المهمة 
افهم انهم صفحتين متكاملتين  و ليس كل صفحة على حدى 
في صفحة جميع التدخلات اليومية في الجداول اذا كان الفلتر فيه جميع انواع التدخلات يظهر الاشياء المتشابهة لكن اذا كان انواع حادث مرور يعتمد  على forms folder 
اذا تحتم الامر و رأيت هذا معقد قم  بانشاء جداول حسب نوع التدخل 
و جدول واحد جمع بينهم في الاشياء المتشابهة 
اظف عمود اجراء لينتقل لصفحة التدخلات اليومية و اكمال ما يريد عملية تعرف انتهاء مهمة 
احفظ ما قمت به في مفهوم التدخلات 4.md 