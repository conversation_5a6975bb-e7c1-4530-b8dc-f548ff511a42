# 📋 مفهوم التدخلات اليومية - نظام الحماية المدنية الجزائرية

## 🎯 نظرة عامة

نظام التدخلات اليومية هو نظام شامل لإدارة ومتابعة جميع التدخلات الطارئة التي تقوم بها وحدات الحماية المدنية في الجزائر. يغطي النظام جميع أنواع التدخلات من البلاغ الأولي حتى إنهاء المهمة، مع إمكانية طلب الدعم والتصعيد للكوارث الكبرى.

---

## 🏗️ هيكل النظام

### 📊 نموذج قاعدة البيانات الرئيسي: `DailyIntervention`

```python
class DailyIntervention(models.Model):
    # معلومات أساسية
    intervention_number = models.CharField(max_length=20, unique=True)  # رقم تلقائي
    intervention_type = models.CharField(max_length=30, choices=INTERVENTION_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initial_report')

    # توقيتات
    departure_time = models.TimeField()  # ساعة الخروج
    arrival_time = models.TimeField(null=True, blank=True)  # ساعة الوصول
    end_time = models.TimeField(null=True, blank=True)  # ساعة الانتهاء

    # معلومات الموقع والاتصال
    location = models.CharField(max_length=200)
    contact_source = models.CharField(max_length=30, choices=CONTACT_SOURCES)
    contact_type = models.CharField(max_length=20, choices=CONTACT_TYPES)
    phone_number = models.CharField(max_length=20, blank=True)
    caller_name = models.CharField(max_length=100, blank=True)

    # إحصائيات التدخل
    injured_count = models.IntegerField(default=0)
    deaths_count = models.IntegerField(default=0)
    final_injured_count = models.IntegerField(default=0)
    final_deaths_count = models.IntegerField(default=0)

    # ملاحظات ووصف
    initial_notes = models.TextField(blank=True)
    material_damage = models.TextField(blank=True)
    final_notes = models.TextField(blank=True)

    # ربط بالوحدة والمستخدم
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    # تواريخ النظام
    date = models.DateField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

---

## 🔄 مراحل التدخل الثلاث

### 1️⃣ البلاغ الأولي (`initial_report`)

**الهدف:** تسجيل سريع للبلاغ الوارد وإرسال الوسائل

**البيانات المطلوبة:**
- ⏰ ساعة ودقيقة الخروج
- 📍 مكان الحادث
- 🔥 نوع التدخل (حريق، إجلاء صحي، حادث مرور، إلخ)
- 🚒 الوسائل المرسلة (يمكن إرسال أكثر من وسيلة)
- 👤 الجهة المتصلة (مواطن، شرطة، درك، إلخ)
- ☎️ نوع الاتصال (هاتفي، راديو، مباشر)
- 📞 رقم الهاتف (اختياري)
- 👨‍💼 اسم المتصل (اختياري)
- 📝 ملاحظات إضافية (اختياري)

**الحالة بعد الحفظ:** `قيد التعرف`

### 2️⃣ عملية التعرف (`reconnaissance`)

**الهدف:** توثيق الوضع الميداني بعد وصول الفريق

**البيانات المطلوبة:**
- 🕐 ساعة الوصول
- 👥 عدد الحاضرين (من التعداد الصباحي)
- 🚑 عدد الضحايا أو المصابين
- ⚰️ عدد الوفيات
- 🏚️ ملاحظات عن الخسائر المادية الأولية
- 🆘 طلب الدعم (إن لزم الأمر)

**خيارات طلب الدعم:**
- ✅ الوضع تحت السيطرة
- 🚒 وسيلة إضافية من نفس الوحدة
- 🏢 وحدة مجاورة (عبر مركز التنسيق الولائي)
- 🚨 فرق متخصصة (غطس، إنقاذ في الأماكن الوعرة)

**الحالة بعد الحفظ:** `عملية تدخل`

### 3️⃣ إنهاء المهمة (`completed`)

**الهدف:** توثيق النتائج النهائية والإحصائيات الدقيقة

**البيانات العامة:**
- 🕙 ساعة انتهاء المهمة
- 👥 أسماء الضحايا (إن وُجدوا)
- 🎂 أعمارهم
- ❤️‍🩹 طبيعة الإصابات (خفيفة، متوسطة، خطيرة)
- ⚰️ العدد النهائي للوفيات
- 📝 ملاحظات ختامية

**بيانات خاصة بالحرائق:**
- 🔥 المساحة المحترقة (هكتارات)
- 📦 عدد حزم التبن المحترقة
- 🏚️ خسائر مادية أخرى
- 🌿 المساحة المنقذة
- 📦 عدد الحزم المنقذة
- 🚜 المعدات المنقذة

**الحالة النهائية:** `منتهية`

---

## 💾 آلية الحفظ في قاعدة البيانات

### 🔧 دالة الحفظ الرئيسية: `save_initial_report`

```python
@csrf_exempt
@require_http_methods(["POST"])
def save_initial_report(request):
    """حفظ البلاغ الأولي"""
    try:
        data = json.loads(request.body)

        # تحويل وقت الخروج
        departure_time_str = data.get('departure_time')
        departure_time_obj = datetime.strptime(departure_time_str, '%H:%M').time()

        # إنشاء التدخل الجديد
        intervention = DailyIntervention.objects.create(
            unit_id=data.get('unit_id'),
            intervention_type=data.get('intervention_type'),
            departure_time=departure_time_obj,
            location=data.get('location'),
            contact_source=data.get('contact_source'),
            contact_type=data.get('contact_type'),
            phone_number=data.get('phone_number', ''),
            caller_name=data.get('caller_name', ''),
            initial_notes=data.get('initial_notes', ''),
            status='initial_report',
            created_by=request.user
        )

        # ربط الوسائل المرسلة
        vehicle_ids = data.get('vehicle_ids', [])
        # ... منطق ربط الوسائل

        return JsonResponse({
            'success': True,
            'intervention_id': intervention.id,
            'intervention_number': intervention.intervention_number
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
```

### 🔄 تحديث حالة التدخل: `update_intervention_status`

```python
@csrf_exempt
@require_http_methods(["POST"])
def update_intervention_status(request):
    """تحديث حالة التدخل"""
    try:
        data = json.loads(request.body)
        intervention_id = data.get('intervention_id')
        new_status = data.get('status')

        intervention = DailyIntervention.objects.get(id=intervention_id)
        intervention.status = new_status

        # إضافة معلومات حسب المرحلة
        if new_status == 'reconnaissance':
            intervention.arrival_time = data.get('arrival_time')
            intervention.injured_count = data.get('injured_count', 0)
            intervention.deaths_count = data.get('deaths_count', 0)
            intervention.material_damage = data.get('material_damage', '')

        elif new_status == 'completed':
            intervention.end_time = data.get('end_time')
            intervention.final_injured_count = data.get('final_injured_count', 0)
            intervention.final_deaths_count = data.get('final_deaths_count', 0)
            intervention.final_notes = data.get('final_notes', '')

        intervention.save()

        return JsonResponse({'success': True})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
```

---

## 🎛️ واجهة المستخدم

### 📱 الصفحة الرئيسية: `daily_interventions.html`

**المكونات الأساسية:**
1. **ثلاثة أزرار رئيسية:**
   - 📢 بلاغ أولي
   - 🧭 عملية التعرف
   - ✅ إنهاء المهمة

2. **جدول التدخلات المباشر:**
   - عرض جميع التدخلات اليومية
   - تحديث فوري للحالات
   - أزرار إجراءات سريعة

3. **نماذج ديناميكية:**
   - نماذج مخفية تظهر عند الحاجة
   - تحقق من صحة البيانات
   - حفظ عبر AJAX

### 🔄 التفاعل مع الواجهة

```javascript
// حفظ البلاغ الأولي
document.getElementById('save-initial-report').addEventListener('click', function() {
    const form = document.getElementById('initialReportForm');
    if (form.checkValidity()) {
        // إرسال البيانات عبر AJAX
        fetch('/save-initial-report/', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addNewInterventionRow();  // إضافة صف جديد للجدول
                alert('تم حفظ البلاغ الأولي بنجاح');
                form.reset();
                hideAllForms();
            }
        });
    }
});
```

---

## 🔗 الربط مع النماذج الأخرى

### 🚒 ربط الوسائل: `InterventionVehicle`
- ربط كل تدخل بالوسائل المستخدمة
- تتبع حالة كل وسيلة (متاحة، في مهمة، صيانة)
- إحصائيات استخدام الوسائل

### 👨‍🚒 ربط الأعوان: `VehicleCrewAssignment`
- ربط الأعوان بالوسائل في كل تدخل
- استيراد البيانات من التعداد الصباحي
- تتبع ساعات العمل والمشاركة

### 📊 تقارير التدخل: `InterventionReport`
- تقرير مفصل لكل تدخل
- إحصائيات تلقائية
- ملخص الوسائل والأعوان

---

## 🚨 طلب الدعم والتصعيد

### 🔄 آلية طلب الدعم
1. **دعم من نفس الوحدة:** إرسال وسيلة إضافية
2. **دعم من وحدة مجاورة:** عبر مركز التنسيق الولائي
3. **فرق متخصصة:** للحالات الخاصة

### ⬆️ التصعيد للكوارث الكبرى
- عند خروج الوضع عن السيطرة
- تحويل تلقائي لنظام الكوارث الكبرى
- تغيير الحالة إلى `escalated`

---

## 📈 الإحصائيات والتقارير

### 📊 تقارير المراحل
- تقرير البلاغات الأولية
- تقرير مرحلة التعرف الميداني
- تقرير إنهاء المهام

### 📥 التصدير
- تصدير Excel لجميع التدخلات
- تصدير PDF لتدخل محدد
- تقارير إحصائية شهرية وسنوية

---

## 🔧 الميزات التقنية

### 🔄 التحديث المباشر
- استخدام AJAX للتحديث الفوري
- عدم إعادة تحميل الصفحة
- تزامن البيانات بين المستخدمين

### 🛡️ الأمان
- التحقق من صحة البيانات
- حماية CSRF
- صلاحيات المستخدمين

### 📱 التوافق
- واجهة متجاوبة
- دعم الأجهزة المحمولة
- تحسين الأداء

---

## 🎯 الخلاصة

نظام التدخلات اليومية هو نظام شامل ومتكامل يغطي جميع جوانب إدارة التدخلات الطارئة في الحماية المدنية الجزائرية. يوفر النظام:

- **سهولة الاستخدام:** واجهة بديهية وسريعة
- **الشمولية:** تغطية جميع أنواع التدخلات
- **المرونة:** إمكانية التكيف مع مختلف الحالات
- **الدقة:** توثيق دقيق لجميع المراحل
- **التكامل:** ربط مع جميع أنظمة الحماية المدنية الأخرى

النظام يضمن متابعة فعالة للتدخلات من البداية حتى النهاية، مع إمكانية طلب الدعم والتصعيد عند الحاجة، مما يساهم في تحسين كفاءة الاستجابة للطوارئ وحماية الأرواح والممتلكات.