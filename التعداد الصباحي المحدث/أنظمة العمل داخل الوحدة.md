# 📋 Morning Check System – Daily Unit Roster & Personnel Assignment

## 🧭 1. الهدف من الصفحة
توفر هذه الصفحة نظامًا لتسجيل التعداد الصباحي وتحديد الفرق العاملة، مع ربط مباشر بتوزيع الأعوان على الوسائل (سيارات الإسعاف، شاحنات الإطفاء...) حسب الجاهزية الفعلية.

---

## 🕘 2. أنظمة العمل داخل الوحدة

### 🔁 أ. نظام 24/48 (الفرق)
- يتم تقسيم الأعوان إلى 3 فرق:
  - **فصيلة A**
  - **فصيلة B**
  - **فصيلة C**
- كل فصيلة تعمل **24 ساعة** ثم ترتاح **48 ساعة**.
- عند اختيار فصيلة قيد العمل اليوم، تصبح تلقائيًا:
  - ✅ أعضاؤها في حالة **"قيد العمل"**
  - 🔄 مرتبطة بـ"توزيع الأعوان على الوسائل" لنفس اليوم
  - ❌ بقية الفرق في حالة راحة ولا يمكن إسنادهم للوسائل

### ⏰ ب. نظام 8 ساعات
- يشمل أفرادًا إداريين أو دعم خاص (أمانة، مخزن، تقنيين...)
- يُسجّل كل فرد يدويًا كـ:
  - ⏱️ **فترة العمل**: صباح، مساء، ليلي
  - 📌 **المهمة**: عمل مكتبي، صيانة، مراسلات...
- لا يرتبط تلقائيًا بتوزيع الأعوان على الوسائل، لكن يمكن استدعاؤه يدويًا عند الحاجة.

---

## 👥 3. أقسام الصفحة

### 🧑‍✈️ الفصيلة العاملة اليوم
- قائمة الأعوان والضباط في الفصيلة المختارة
- حالة كل عون:
  - ✅ حاضر
  - ❌ غائب
  - 🚑 في مهمة
  - 🔄 احتياطي

### 📝 تسجيل الأعوان بنظام 8 ساعات
- جدول منفصل يسجل:
  - الاسم
  - الرتبة
  - الفترة الزمنية
  - المهمة

### 🚨 توزيع الأعوان على الوسائل (ربط مباشر)
- بعد اختيار الفصيلة العاملة:
  - تظهر **الوسائل الجاهزة**
  - يمكن اختيار:
    - السائق
    - رئيس العدد
    - الأعوان (حسب عدد المقاعد)
- **التحقق التلقائي**: إذا كان العون مسجلًا كـ"غائب"، لا يمكن اختياره.
- **الإشعار**: في حال نقص في عدد الأعوان، يظهر تنبيه لمركز التنسيق.

---

## 🔄 4. الربط مع الجاهزية اليومية
- كل وسيلة في "إدارة العتاد" لها حالة: ✅ جاهزة أو 🛠️ معطلة
- لا تظهر الوسائل المعطلة في صفحة التوزيع
- عند ربط الوسائل بالأعوان:
  - يتم تسجيل ذلك في سجل اليوم
  - يُستخدم لاحقًا لحساب عدد التدخلات والأعوان المتاحين

### 🔄 4.1 المزامنة بين الصفحات (جديد)
- **المزامنة التلقائية**: عمود "الجاهزية" في الصفحة الموحدة يتحدث تلقائياً من صفحة التوزيع
- **التحديث الفوري**: عند إضافة/إزالة أعوان في صفحة التوزيع، تتحدث الجاهزية فوراً في الصفحة الموحدة
- **التأكيد اليدوي**: إمكانية تأكيد الجاهزية يدوياً من صفحة التوزيع مع إرسال التحديث للصفحة الموحدة
- **نظام الرسائل**: استخدام `postMessage` و `localStorage` للمزامنة بين النوافذ المفتوحة

---

## 📊 5. التتبع والإحصاء
- يتم تخزين كل يوم كتقرير داخلي (يمكن تصديره)
- يُستخدم ل:
  - تقارير الجاهزية اليومية
  - معرفة العجز البشري أو العتادي
  - المتابعة في حالات الكوارث الكبرى

---

## 🔒 6. صلاحيات الاستخدام
| الدور               | الصلاحيات                                         |
|--------------------|--------------------------------------------------|
| رئيس الوحدة         | تحديد الفصيلة العاملة، تحديث الحضور، توزيع الأعوان |
| مسؤول الموارد        | تسجيل دوام 8 ساعات، تتبع المهام                   |
| مركز التنسيق الولائي | عرض الجاهزية، التنبيهات، مؤشرات النقص              |

---

## 💡 ملاحظات إضافية
- يمكن تفعيل **نظام التنبيه التلقائي** لمركز التنسيق في حالة:
  - نقص أعوان في وسيلة معينة
  - فصيلة ناقصة (غيابات كثيرة)
  - عدم جاهزية الوسائل الكافية

---

## 🔄 5. نظام المزامنة المتقدم (تحديث 19 يوليو 2025)

### 🎯 الهدف
تحقيق مزامنة فورية لعمود "الجاهزية" في جدول إدارة الوسائل بالصفحة الموحدة مع التحديثات من صفحة توزيع الأعوان.

### 🔧 الآلية التقنية

#### **أ. APIs الجديدة:**
- **`/api/update-readiness-from-assignment/`**: تحديث الجاهزية من صفحة التوزيع
- **معاملات الطلب**:
  - `vehicle_id`: معرف الوسيلة
  - `date`: تاريخ التوزيع
  - `action`: نوع التحديث (`calculate` أو `manual_confirm`)

#### **ب. نظام الرسائل:**
- **`postMessage`**: للمزامنة بين النوافذ المفتوحة
- **`localStorage`**: للمزامنة عند إعادة تحميل الصفحات
- **رسائل التزامن**: تحتوي على معرف الوسيلة، نسبة الجاهزية، الحالة، وعدد الأعوان

#### **ج. التحديث التلقائي:**
- **عند إضافة عون**: تحديث الجاهزية إلى 100% إذا كانت الوسيلة فارغة
- **عند إزالة عون**: تحديث الجاهزية إلى 0% إذا أصبحت الوسيلة فارغة
- **التأكيد اليدوي**: تحديث الجاهزية إلى "مؤكد يدوياً" مع 100%

### 📊 حالات الجاهزية

| الحالة | النسبة | الوصف | المصدر |
|--------|--------|--------|--------|
| **جاهز** | 100% | يوجد أعوان معينون | تلقائي |
| **غير جاهز** | 0% | لا يوجد أعوان | تلقائي |
| **مؤكد يدوياً** | 100% | تأكيد من المسؤول | يدوي |

### 🔄 تدفق العمل

1. **في صفحة التوزيع**:
   - المستخدم يضيف/يزيل أعوان
   - يتم استدعاء API تحديث الجاهزية
   - إرسال رسالة تزامن للصفحة الموحدة

2. **في الصفحة الموحدة**:
   - استقبال رسالة التزامن
   - تحديث عمود الجاهزية فوراً
   - إظهار إشعار للمستخدم

3. **عند إعادة التحميل**:
   - فحص `localStorage` للتحديثات الحديثة
   - تطبيق التحديثات إذا كانت خلال آخر 30 ثانية

### 🎨 التحسينات البصرية

#### **ألوان الجاهزية:**
- **أخضر** (≥80%): جاهز تماماً
- **أصفر** (60-79%): جاهز جزئياً
- **أحمر** (<60%): غير جاهز

#### **الأيقونات:**
- **✅ جاهز**: أيقونة دائرة خضراء
- **⚠️ مؤكد يدوياً**: أيقونة يد برتقالية
- **❌ غير جاهز**: أيقونة X حمراء

### 🔗 الربط بين الصفحات

#### **زر الصفحة الموحدة:**
- في صفحة التوزيع: زر "الصفحة الموحدة" لفتح النافذة مع التتبع
- تتبع النافذة المفتوحة لإرسال الرسائل

#### **التزامن الثنائي:**
- من التوزيع → الموحدة: تحديث الجاهزية
- من الموحدة → التوزيع: تحديث حالة الوسائل (موجود مسبقاً)

### 📈 الفوائد المحققة

1. **⚡ تحديث فوري**: لا حاجة لإعادة تحميل الصفحات
2. **🔄 مزامنة دقيقة**: تطابق البيانات بين الصفحات
3. **👥 تعاون محسن**: عدة مستخدمين يمكنهم العمل معاً
4. **📊 بيانات موثوقة**: الجاهزية تعكس الوضع الفعلي
5. **🎯 كفاءة عالية**: تقليل الأخطاء البشرية

### 🧪 اختبار النظام

#### **سيناريو الاختبار:**
1. فتح الصفحة الموحدة: `http://127.0.0.1:8000/coordination-center/unified-morning-check/?unit_id=11`
2. فتح صفحة التوزيع: `http://127.0.0.1:8000/vehicle-crew-assignment/?unit=11&date=2025-07-19`
3. إضافة عون لوسيلة في صفحة التوزيع
4. مراقبة تحديث عمود الجاهزية في الصفحة الموحدة فوراً
5. إزالة العون ومراقبة التحديث مرة أخرى

#### **النتيجة المتوقعة:**
- ✅ تحديث فوري لعمود الجاهزية
- ✅ إشعار بصري للمستخدم
- ✅ تطابق البيانات بين الصفحتين

