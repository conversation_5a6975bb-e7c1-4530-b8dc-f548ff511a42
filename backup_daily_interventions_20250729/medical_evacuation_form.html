<!-- نموذج الإجلاء الصحي المتخصص -->
<div id="medical-evacuation-form" class="intervention-detail-form" style="display: none;">
    <div class="form-section">
        <h3 class="section-title">
            <i class="fas fa-ambulance"></i>
            تفاصيل الإجلاء الصحي
        </h3>
        
        <!-- مرحلة التعرف -->
        <div class="stage-section" id="medical-reconnaissance-stage">
            <h4 class="stage-title">
                <i class="fas fa-search"></i>
                مرحلة التعرف الميداني
            </h4>
            
            <!-- الموقع -->
            <div class="form-group">
                <label for="medical-location-type">📍 الموقع:</label>
                <select id="medical-location-type" name="location_type" class="form-control">
                    <option value="">اختر الموقع</option>
                    <option value="inside_home">داخل المنزل</option>
                    <option value="outside_home">خارج المنزل</option>
                </select>
            </div>
            
            <!-- طبيعة التدخل -->
            <div class="form-group">
                <label for="medical-intervention-nature">🏥 طبيعة التدخل:</label>
                <select id="medical-intervention-nature" name="intervention_nature" class="form-control">
                    <option value="">اختر طبيعة التدخل</option>
                    <option value="suffocation">الاختناق</option>
                    <option value="poisoning">التسممات</option>
                    <option value="burns">الحروق</option>
                    <option value="explosions">الانفجارات</option>
                    <option value="patient_evacuation">إجلاء المرضى</option>
                    <option value="drowning">الغرقى</option>
                </select>
            </div>
            
            <!-- تفاصيل حسب النوع -->
            <div id="medical-nature-details" class="conditional-fields">
                
                <!-- تفاصيل الاختناق -->
                <div id="suffocation-details" class="nature-detail" style="display: none;">
                    <label for="suffocation-type">نوع الاختناق:</label>
                    <select id="suffocation-type" name="suffocation_type" class="form-control">
                        <option value="">اختر نوع الاختناق</option>
                        <option value="natural_gas">بالغاز الطبيعي أو البوتان</option>
                        <option value="co_gas">غاز CO</option>
                        <option value="airway_obstruction">انسداد المجاري التنفسية</option>
                        <option value="closed_spaces">الأماكن المغلقة</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <!-- تفاصيل التسممات -->
                <div id="poisoning-details" class="nature-detail" style="display: none;">
                    <label for="poisoning-type">نوع التسمم:</label>
                    <select id="poisoning-type" name="poisoning_type" class="form-control">
                        <option value="">اختر نوع التسمم</option>
                        <option value="food">مواد غذائية</option>
                        <option value="medication">أدوية</option>
                        <option value="cleaning">منظفات</option>
                        <option value="bites_stings">لسعات أو عضّات</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <!-- تفاصيل الحروق -->
                <div id="burns-details" class="nature-detail" style="display: none;">
                    <label for="burn-type">نوع الحرق:</label>
                    <select id="burn-type" name="burn_type" class="form-control">
                        <option value="">اختر نوع الحرق</option>
                        <option value="flames">ألسنة اللهب</option>
                        <option value="hot_liquids">سوائل ساخنة</option>
                        <option value="chemical_radioactive">مواد كيميائية/مشعة</option>
                        <option value="electrical">صعقات كهربائية</option>
                    </select>
                </div>
                
                <!-- تفاصيل الانفجارات -->
                <div id="explosions-details" class="nature-detail" style="display: none;">
                    <label for="explosion-type">نوع الانفجار:</label>
                    <select id="explosion-type" name="explosion_type" class="form-control">
                        <option value="">اختر نوع الانفجار</option>
                        <option value="butane_natural_gas">غاز البوتان / الغاز الطبيعي</option>
                        <option value="electrical_heating">الأجهزة الكهرومنزلية / أجهزة التدفئة</option>
                    </select>
                </div>
                
                <!-- تفاصيل الغرق -->
                <div id="drowning-details" class="nature-detail" style="display: none;">
                    <label for="drowning-type">نوع الغرق:</label>
                    <select id="drowning-type" name="drowning_type" class="form-control">
                        <option value="">اختر نوع الغرق</option>
                        <option value="water_bodies">مسطحات مائية</option>
                        <option value="dams">سدود</option>
                        <option value="valleys">أودية</option>
                        <option value="beaches">شواطئ</option>
                        <option value="other_places">أماكن أخرى</option>
                    </select>
                </div>
            </div>
            
            <!-- طلب الدعم -->
            <div class="form-group">
                <label for="medical-support-request">🚨 طلب الدعم:</label>
                <select id="medical-support-request" name="support_request" class="form-control">
                    <option value="">اختر نوع الدعم</option>
                    <option value="under_control">شكراً، الوضع تحت السيطرة</option>
                    <option value="additional_vehicle">نعم وسيلة إضافية</option>
                    <option value="neighboring_unit">نعم وحدة مجاورة</option>
                    <option value="specialized_team">نعم فريق متخصص</option>
                </select>
            </div>
            
            <!-- نوع الفريق المتخصص -->
            <div id="specialized-team-section" class="form-group" style="display: none;">
                <label for="specialized-team-type">نوع الفريق المتخصص:</label>
                <select id="specialized-team-type" name="specialized_team_type" class="form-control">
                    <option value="">اختر نوع الفريق</option>
                    <option value="divers">فرقة الغطاسين</option>
                    <option value="rough_terrain">التدخل في الأماكن الوعرة</option>
                    <option value="cynotechnical">فرقة السينوتقنية</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
            
            <!-- ملاحظات عن الخسائر المادية -->
            <div class="form-group">
                <label for="medical-material-damage">💰 ملاحظة عن الخسائر المادية:</label>
                <textarea id="medical-material-damage" name="material_damage_notes" class="form-control" rows="3" placeholder="اختياري - وصف الخسائر المادية إن وجدت"></textarea>
            </div>
        </div>
        
        <!-- مرحلة الإنهاء -->
        <div class="stage-section" id="medical-completion-stage">
            <h4 class="stage-title">
                <i class="fas fa-check-circle"></i>
                مرحلة إنهاء المهمة
            </h4>
            
            <!-- تفاصيل المسعفين -->
            <div class="form-group">
                <label>👨‍⚕️ تفاصيل المسعفين:</label>
                <div id="medical-injured-list" class="casualties-list">
                    <!-- سيتم إضافة المسعفين ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addMedicalCasualty('injured')">
                    <i class="fas fa-plus"></i> إضافة مسعف
                </button>
            </div>
            
            <!-- تفاصيل الوفيات -->
            <div class="form-group">
                <label>⚰️ تفاصيل الوفيات:</label>
                <div id="medical-fatalities-list" class="casualties-list">
                    <!-- سيتم إضافة الوفيات ديناميكياً -->
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="addMedicalCasualty('fatality')">
                    <i class="fas fa-plus"></i> إضافة وفاة
                </button>
            </div>
            
            <!-- عدد التدخلات -->
            <div class="form-group">
                <label for="medical-total-interventions">🔢 عدد التدخلات (الوسائل الأساسية + وسائل الدعم):</label>
                <input type="number" id="medical-total-interventions" name="total_interventions" class="form-control" min="0" value="0">
            </div>
        </div>
        
        <!-- أزرار الحفظ -->
        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="saveMedicalEvacuationDetails()">
                <i class="fas fa-save"></i>
                حفظ تفاصيل الإجلاء الصحي
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideMedicalForm()">
                <i class="fas fa-times"></i>
                إلغاء
            </button>
        </div>
    </div>
</div>

<style>
.intervention-detail-form {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.section-title {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.stage-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.stage-title {
    color: #28a745;
    margin-bottom: 15px;
}

.conditional-fields {
    background: #f1f3f4;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
}

.nature-detail {
    margin-bottom: 15px;
}

.casualties-list {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    min-height: 50px;
    background: #f8f9fa;
}

.casualty-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-actions {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.form-actions .btn {
    margin: 0 10px;
}
</style>
