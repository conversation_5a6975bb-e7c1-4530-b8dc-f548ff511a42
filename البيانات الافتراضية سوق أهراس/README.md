# 🏛️ البيانات الافتراضية لولاية سوق أهراس

## 📋 نظرة عامة

تم إنشاء بيانات افتراضية شاملة لولاية سوق أهراس تشمل:
- **12 وحدة** (رئيسية + متقدمة + ثانوية)
- **500+ عون** بأسماء جزائرية أصيلة
- **50+ وسيلة** متنوعة

## 🏢 الوحدات المشمولة

### الوحدة الرئيسية
- **الوحدة الرئيسية سوق أهراس** (SA-MAIN)
  - 45 عون
  - 5 وسائل

### المراكز المتقدمة
- **المركز المتقدم طريق رقم 16** (SA-ADV16)
- **المركز المتقدم برال صالح** (SA-ADVBS)
  - 35 عون لكل مركز
  - 4 وسائل لكل مركز

### الوحدات الثانوية
- **الوحدة الثانوية سدراتة** (SA-SEDR)
- **الوحدة الثانوية مداوروش** (SA-MEDA)
- **الوحدة الثانوية المشروحة** (SA-MASH)
- **الوحدة الثانوية بئربوحوش** (SA-BIRB)
- **الوحدة الثانوية أم العضائم** (SA-OUMA)
- **الوحدة الثانوية المراهنة** (SA-MARA)
- **الوحدة الثانوية الحدادة** (SA-HADA)
- **الوحدة الثانوية أولاد إدريس** (SA-OUID)
- **الوحدة الثانوية عين الزانة** (SA-AINZ)
  - 40-44 عون لكل وحدة
  - 4-5 وسائل لكل وحدة

## 👥 الأعوان

### الأسماء الجزائرية الأصيلة

#### الأسماء الأولى:
عبد الرزاق، محمد، أحمد، عبد الله، عمر، علي، حسن، حسين، يوسف، إبراهيم، عبد الرحمن، خالد، سعيد، عبد الكريم، مصطفى، عبد العزيز، نور الدين، صلاح الدين، عبد الحميد، عبد المجيد، بلال، طارق، عماد، رضا، فريد، كمال، جمال، رشيد، عبد الوهاب، عبد الباسط، عبد الناصر، عبد الحليم، زكريا، إسماعيل، عثمان، عبد الصمد، عبد الغني، عبد الحق، رمزي، فتحي، مراد، هشام، وليد، سمير، عادل، نبيل، فاروق، عبد السلام، عبد الجليل، عبد الفتاح، عبد الستار، بشير، منير، أمين، ياسين، حكيم، كريم، رحيم، سليم، حليم

#### أسماء العائلات:
مختاري، بوعزة، بن علي، حمدي، العربي، زروقي، قاسمي، بلعباس، شريف، نوري، حجازي، سليماني، بوضياف، مرزوقي، عثماني، جزائري، تبسي، سوقي، أهراسي، سدراتي، مداوروشي، مشروحي، بوحوشي، عضايمي، مراهني، حدادي، إدريسي، زاني، برالي، طريقي، صالحي، متقدمي، رئيسي، ثانوي، وحدوي، حمايدي، عمراني، ريفي، جبلي، سهلي، واديي، تلي، صحراوي، شرقي، غربي، شمالي، جنوبي، وسطي، داخلي، خارجي، حدودي

### التوزيع الهرمي

#### الضباط (20-30%)
- **الرتب**: عقيد، مقدم، رائد، نقيب، ملازم أول، ملازم
- **المناصب**: قائد الوحدة، نائب قائد الوحدة، رئيس المصلحة، مسؤول العمليات، مسؤول الأمن، مسؤول التدريب، مسؤول الصيانة، مسؤول الإمداد
- **نظام العمل**: 8 ساعات (غالباً)

#### ضباط الصف (40%)
- **الرتب**: رقيب أول، رقيب، عريف أول، عريف
- **المناصب**: رئيس عدد، مسؤول فرقة، مدرب، تقني صيانة، مشغل راديو، مسؤول مخزن، مراقب، منسق
- **نظام العمل**: 24 ساعة أو 8 ساعات

#### الجنود (40%)
- **الرتب**: جندي أول، جندي
- **المناصب**: سائق، عون إطفاء، عون إسعاف، عون إنقاذ، عون أمن، كاتب، حارس، طباخ، عامل صيانة، مساعد
- **نظام العمل**: 24 ساعة (غالباً)

### أنظمة العمل

#### نظام 24 ساعة (الفرق)
- **الفرقة الأولى** (shift_1)
- **الفرقة الثانية** (shift_2)
- **الفرقة الثالثة** (shift_3)
- **التوزيع**: متساوي بين الفرق الثلاث

#### نظام 8 ساعات
- **التوقيت**: 8:00 صباحاً - 4:30 مساءً
- **أيام العمل**: الأحد إلى الخميس
- **المستفيدون**: الضباط والإداريون

## 🚗 الوسائل

### أنواع الوسائل المتاحة

#### سيارات الإسعاف
- Mercedes Sprinter
- Iveco Daily
- Renault Master
- Ford Transit

#### شاحنات الإطفاء
- MAN 6000L (كبيرة)
- Iveco 4000L (متوسطة)
- Mercedes 3000L (متوسطة)
- Renault 2500L (صغيرة)

#### مركبات الإنقاذ
- فورجون إنقاذ Iveco
- فورجون تدخل سريع Mercedes
- مركبة قيادة Toyota Land Cruiser
- مركبة استطلاع Nissan Patrol

#### معدات إضافية
- دراجة نارية Honda/Yamaha
- مولد كهربائي 50KW
- مضخة مياه متنقلة
- رافعة إنقاذ 25 طن
- سيارة ورشة متنقلة

### نظام الترقيم
- **الرقم التسلسلي**: SA[رقم الوحدة]-[نوع الوسيلة]-[رقم متسلسل]
- **رقم الراديو**: R[رقم الوحدة][رقم متسلسل]

**مثال**: SA11-MER-01 (Mercedes في الوحدة 11)

## 🛠️ التطبيق

### 1. استخدام Migration

```bash
# تطبيق Migration
python manage.py migrate

# سيتم إضافة البيانات تلقائياً
```

### 2. استخدام Command

```bash
# إضافة البيانات لجميع الوحدات
python manage.py setup_souk_ahras_data

# إضافة البيانات لوحدة محددة
python manage.py setup_souk_ahras_data --unit-id 11

# حذف البيانات الموجودة وإضافة جديدة
python manage.py setup_souk_ahras_data --reset

# حذف وإضافة لوحدة محددة
python manage.py setup_souk_ahras_data --unit-id 11 --reset
```

## 📊 الإحصائيات المتوقعة

### إجمالي البيانات
- **الوحدات**: 12 وحدة
- **الأعوان**: ~500 عون
- **الوسائل**: ~55 وسيلة

### توزيع الأعوان
- **الضباط**: ~100 عون (20%)
- **ضباط الصف**: ~200 عون (40%)
- **الجنود**: ~200 عون (40%)

### توزيع أنظمة العمل
- **نظام 24 ساعة**: ~350 عون (70%)
- **نظام 8 ساعات**: ~150 عون (30%)

### توزيع الجنس
- **ذكور**: ~425 عون (85%)
- **إناث**: ~75 عون (15%)

## 🔧 التخصيص

### إضافة أسماء جديدة
يمكن تعديل قوائم الأسماء في:
- `first_names`: الأسماء الأولى
- `last_names`: أسماء العائلات

### تعديل التوزيع
يمكن تعديل نسب:
- `officer_ratio`: نسبة الضباط
- نسب الرتب والمناصب
- توزيع أنظمة العمل

### إضافة وسائل جديدة
يمكن إضافة أنواع وسائل جديدة في:
- `vehicle_types`: قائمة أنواع الوسائل

## 🎯 الاستخدام المقترح

### للتطوير
- اختبار النظام مع بيانات واقعية
- تجربة الوظائف المختلفة
- اختبار الأداء مع عدد كبير من السجلات

### للعرض التوضيحي
- عرض النظام للمسؤولين
- تدريب المستخدمين
- اختبار قبول المستخدم

### للإنتاج
- نقطة بداية للبيانات الحقيقية
- قالب لإدخال البيانات
- مرجع للتسمية والترقيم

## ⚠️ ملاحظات مهمة

1. **البيانات افتراضية**: جميع البيانات وهمية لأغراض التطوير والاختبار
2. **الأرقام عشوائية**: أرقام الهواتف والتسجيل عشوائية
3. **التواريخ منطقية**: تواريخ الميلاد والالتحاق منطقية حسب الرتبة
4. **النسب واقعية**: توزيع الرتب والجنس يحاكي الواقع

## 📞 الدعم

للمساعدة أو الاستفسارات حول البيانات الافتراضية، راجع:
- ملف Migration: `0030_add_souk_ahras_sample_data.py`
- Command: `setup_souk_ahras_data.py`
- التوثيق الرئيسي: `Memory_check.md`
