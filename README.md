# نظام إدارة الحماية المدنية DPC_DZ

## نظرة عامة

نظام شامل لإدارة عمليات الحماية المدنية في الجزائر، يشمل إدارة الوحدات، الأعوان، الوسائل، والتدخلات.

## ✨ المزايا الجديدة (بعد إعادة الهيكلة)

### 🏗️ هيكل محسن
- **تقسيم Models**: من ملف واحد (2,287 سطر) إلى ملفات منظمة
- **تقسيم Views**: من ملف واحد (10,729 سطر) إلى ملفات متخصصة  
- **طبقة Services**: فصل المنطق التجاري عن العرض
- **Type Hints**: تحسين جودة الكود وسهولة الصيانة

### 🔒 أمان محسن
- **Mixins للصلاحيات**: تحكم دقيق في الوصول
- **تدقيق العمليات**: تسجيل جميع الأنشطة
- **التحقق من البيانات**: طبقة حماية إضافية

### 📊 إدارة متقدمة
- **خدمات متخصصة**: لكل جانب من جوانب النظام
- **إحصائيات ذكية**: تحليل البيانات والأداء
- **إشعارات تلقائية**: تنبيهات للأحداث المهمة

## 📁 الهيكل الجديد

```
dpcdz/home/
├── models/
│   ├── __init__.py          # استيراد جميع النماذج
│   ├── base.py              # النماذج الأساسية
│   ├── choices.py           # الخيارات المشتركة
│   ├── user_models.py       # نماذج المستخدمين
│   ├── unit_models.py       # نماذج الوحدات
│   ├── personnel_models.py  # نماذج الأعوان
│   └── equipment_models.py  # نماذج الوسائل
├── views/
│   ├── __init__.py          # استيراد جميع الـ Views
│   ├── base.py              # Views أساسية و Mixins
│   ├── auth_views.py        # المصادقة وتسجيل الدخول
│   └── dashboard_views.py   # لوحات التحكم
├── services/
│   ├── __init__.py          # استيراد جميع الخدمات
│   ├── base_service.py      # الخدمات الأساسية
│   ├── personnel_service.py # خدمة الأعوان
│   ├── equipment_service.py # خدمة الوسائل
│   └── intervention_service.py # خدمة التدخلات
├── tests/
│   ├── __init__.py
│   └── test_services.py     # اختبارات الخدمات
└── documentation.md         # التوثيق التفصيلي
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- Django 4.0+
- PostgreSQL (اختياري)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd DPC_DZ
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
cd dpcdz
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء مستخدم مدير**
```bash
python manage.py createsuperuser
```

6. **تشغيل الخادم**
```bash
python manage.py runserver
```

## 🔧 الاستخدام

### إنشاء مستخدم جديد
```python
from home.services import PersonnelService

service = PersonnelService(request.user)
personnel = service.create_personnel({
    'unit_id': 1,
    'registration_number': 'SA001',
    'first_name': 'أحمد',
    'last_name': 'محمد',
    'rank': 'عريف',
    'position': 'عون إطفاء'
})
```

### إدارة الوسائل
```python
from home.services import EquipmentService

service = EquipmentService(request.user)
equipment = service.create_equipment({
    'unit_id': 1,
    'serial_number': 'SA-001',
    'equipment_type': 'سيارة إطفاء'
})

# تحديث حالة الوسيلة
service.update_equipment_status(equipment.id, 'maintenance')
```

### إنشاء تدخل
```python
from home.services import InterventionService

service = InterventionService(request.user)
intervention = service.create_intervention({
    'unit_id': 1,
    'intervention_type': 'fire',
    'location': 'شارع الاستقلال',
    'report_time': timezone.now().time(),
    'contact_type': 'phone'
})
```

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
python manage.py test

# تشغيل اختبارات الخدمات فقط
python manage.py test home.tests.test_services

# تشغيل اختبار محدد
python manage.py test home.tests.test_services.PersonnelServiceTestCase.test_create_personnel_success
```

## 📊 الميزات الرئيسية

### إدارة الوحدات
- إنشاء وإدارة وحدات التدخل
- تخصيص الصلاحيات حسب الولاية
- إحصائيات الجاهزية

### إدارة الأعوان
- تسجيل بيانات الأعوان
- إدارة الحضور والغياب
- نظام النقل بين الوحدات
- إحصائيات الأداء

### إدارة الوسائل
- تسجيل الوسائل والمعدات
- متابعة حالة الصيانة
- تعيين الطاقم للوسائل
- حساب نقاط الجاهزية

### إدارة التدخلات
- تسجيل التدخلات اليومية
- متابعة حالة التدخل
- إحصائيات التدخلات
- تقارير مفصلة

### التقارير والإحصائيات
- تقارير يومية وشهرية
- إحصائيات الأداء
- تحليل البيانات
- تصدير البيانات

## 🔐 الأمان والصلاحيات

### أدوار المستخدمين
- **مدير النظام**: صلاحية كاملة
- **مدير الولاية**: إدارة وحدات الولاية
- **مدير الوحدة**: إدارة الوحدة المخصصة
- **منسق الوحدة**: العمليات اليومية

### الحماية
- تشفير كلمات المرور
- جلسات آمنة
- تدقيق العمليات
- التحقق من الصلاحيات

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

## 🔄 التحديثات المستقبلية

- [ ] API REST كامل
- [ ] تطبيق موبايل
- [ ] تحليلات متقدمة
- [ ] تكامل مع أنظمة خارجية
- [ ] إشعارات فورية
- [ ] خرائط تفاعلية

---

**تم تطوير هذا النظام لخدمة الحماية المدنية الجزائرية 🇩🇿**
