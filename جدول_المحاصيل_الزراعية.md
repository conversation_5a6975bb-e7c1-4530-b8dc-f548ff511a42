# 🚨 مشكلة عاجلة: جدول حريق المحاصيل الزراعية - 28 يوليو 2025

## 📋 المشاكل المحددة من المستخدم:

### ❌ **المشاكل الحالية:**

1. **نوع المحصول المحترق** - لا تظهر شيء
2. **عدد البؤر (الموقد)** - صفر رغم الملء
3. **اتجاه الرياح** - صفر رغم الملء  
4. **سرعة الرياح (كم/سا)** - صفر رغم الملء
5. **طلب الدعم** - فارغة
6. **عدد الوفيات، أسماء الوفيات، أعمار الوفيات، جنس الوفيات** - خلط في البيانات
7. **الحالة (سائق/راكب/مشاة)** - **العمود لا يزال موجود ويجب حذفه**
8. **جميع حقول الخسائر والمساحات المنقذة** - أصفار وفارغة

### 🎯 **المشكلة الجذرية المكتشفة:**

بعد التحليل العميق، تبين أن:
1. **النماذج تحفظ البيانات** في `AgriculturalFireDetail` بنجاح ✅
2. **API العرض يجلب البيانات** من قاعدة البيانات بنجاح ✅  
3. **المشكلة في JavaScript العرض** - لا يستخدم البيانات من المصدر الصحيح ❌

**مثال من API الفعلي:**
```json
{
  "agricultural_fire_detail": {
    "fire_type": "قمح واقف",
    "fire_sources_count": 3,
    "wind_direction": "شمال", 
    "wind_speed": 15.0,
    "evacuation_location": "مدرسة القرية",
    "affected_families_count": 7,
    "support_request": "وحدة مجاورة"
  }
}
```

---

## 🎯 **المهام المطلوبة للوكيل التالي:**

### **المرحلة 1: حذف العمود الخاطئ (15 دقيقة)**

#### 1.1 حذف عمود "الحالة (سائق/راكب/مشاة)":

**الملف**: `dpcdz/templates/coordination_center/intervention_details.html`

**المطلوب**:
- ابحث عن جدول حريق المحاصيل (حوالي السطر 850-950)
- احذف العمود الذي يحتوي على "الحالة (سائق/راكب/مشاة)"
- **هذا العمود خاص بحوادث المرور فقط**
- قلل عدد الأعمدة من 46 إلى 45

### **المرحلة 2: إصلاح JavaScript العرض (45 دقيقة)**

#### 2.1 إصلاح دالة العرض:

**الملف**: `dpcdz/templates/coordination_center/intervention_details.html`

**المشكلة**: في دالة `createTableRow` للنوع `crop`، الكود يستخدم `intervention` أولاً بدلاً من `agriculturalDetail`

**الحل المطلوب**:
```javascript
// بدلاً من:
intervention.fire_type || agriculturalDetail.fire_type

// استخدم:
agriculturalDetail.fire_type || intervention.fire_type
```

**الحقول المطلوب إصلاحها**:
- نوع المحصول المحترق: `agriculturalDetail.fire_type`
- عدد البؤر: `agriculturalDetail.fire_sources_count`
- اتجاه الرياح: `agriculturalDetail.wind_direction`
- سرعة الرياح: `agriculturalDetail.wind_speed`
- طلب الدعم: `agriculturalDetail.support_request`
- جميع حقول الخسائر والمساحات المنقذة

### **المرحلة 3: إصلاح بيانات الضحايا والوفيات (30 دقيقة)**

#### 3.1 المشكلة:
بيانات الضحايا والوفيات تظهر في الأعمدة الخاطئة

#### 3.2 الحل:
- تأكد من أن `victimsData` تأتي من `intervention.injured_details`
- تأكد من أن `fatalitiesData` تأتي من `intervention.fatality_details`
- تأكد من أن دالة `formatPersonDetails` تعمل بشكل صحيح

### **المرحلة 4: اختبار شامل (30 دقيقة)**

#### 4.1 إنشاء تدخل جديد:
1. افتح: `http://127.0.0.1:8000/coordination-center/daily-interventions/`
2. أنشئ بلاغ أولي لحريق محاصيل زراعية
3. املأ نموذج التعرف بجميع البيانات
4. املأ نموذج إنهاء المهمة بجميع البيانات
5. تحقق من ظهور البيانات في: `http://127.0.0.1:8000/coordination-center/intervention-details/`

---

## 📚 **كيف تم حل مشاكل الإجلاء الصحي وحوادث المرور:**

### ✅ **الإجلاء الصحي - يعمل بشكل مثالي:**

1. **العرض في الجدول**:
```javascript
// يستخدم medicalDetail أولاً
medicalDetail.patient_condition || intervention.patient_condition
```

2. **الترجمة**:
```javascript
translateValue(medicalDetail.patient_condition) // يترجم القيم للعربية
```

### ✅ **حوادث المرور - تم إصلاحها:**

1. **العرض في الجدول**:
```javascript
// يستخدم accidentDetail أولاً
accidentDetail.accident_nature || intervention.accident_nature
```

2. **بيانات الضحايا**:
```javascript
// يستخدم victims_details من intervention
const victimsData = intervention.victims_details || [];
```

---

## 🎯 **الهدف النهائي:**

**جعل جدول حريق المحاصيل الزراعية يعمل بنفس الطريقة المتقنة التي يعمل بها الإجلاء الصحي وحوادث المرور.**

### **النتيجة المطلوبة:**
- ✅ **45 عمود** (بدون عمود الحالة)
- ✅ **جميع البيانات تظهر** من النماذج المتخصصة
- ✅ **الترجمة تعمل** للقيم العربية
- ✅ **النظام متزامن** بين النماذج والجداول

---

## ⚠️ **تحذيرات مهمة:**

### 🚫 **لا تلمس هذه الأجزاء - تعمل بشكل مثالي:**

1. **الإجلاء الصحي**: النماذج ✅ APIs ✅ الجداول ✅ JavaScript ✅
2. **حوادث المرور**: النماذج ✅ APIs ✅ الجداول ✅ JavaScript ✅

### ✅ **ركز فقط على:**

**حريق المحاصيل الزراعية**:
- حذف العمود الخاطئ
- إصلاح JavaScript العرض
- إصلاح ترتيب أولوية البيانات

---

## 📞 **رسالة للوكيل التالي:**

**عزيزي الوكيل التالي،**

المستخدم ملأ جميع البيانات في النماذج المتخصصة لحريق المحاصيل الزراعية، والبيانات محفوظة في قاعدة البيانات، لكن **JavaScript العرض لا يستخدم البيانات من المصدر الصحيح**.

**المشكلة الأساسية**: الكود يستخدم `intervention` أولاً بدلاً من `agriculturalDetail`، والبيانات موجودة في `agriculturalDetail`.

**ابدأ بحذف العمود الخاطئ أولاً، ثم اتبع الخطوات المذكورة أعلاه.**

**🎯 الهدف: جعل حريق المحاصيل يعمل مثل الإجلاء الصحي تماماً.**

**🔥 ابدأ العمل فوراً - المستخدم ينتظر!**
