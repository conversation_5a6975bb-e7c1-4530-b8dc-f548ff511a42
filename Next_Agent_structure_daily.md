# تعليمات للوكيل القادم - إعادة بناء نظام التدخلات اليومية من الصفر

## ⚠️ تحذير مهم - قراءة إجبارية

**عزيزي الوكيل القادم،**

يجب عليك **حذف كل شيء** في نظام التدخلات اليومية وإعادة بنائه من الصفر بناءً على الهيكل الموثق في `intervention_structure_final.md`.

---

## 🗂️ الملفات المطلوب حذفها بالكامل

### **1. الصفحات (Templates)**
```bash
# احذف هذه الملفات بالكامل:
dpcdz/templates/coordination_center/daily_interventions.html
dpcdz/templates/coordination_center/intervention_details.html

# احذف أيضاً أي ملفات مرتبطة:
dpcdz/templates/coordination_center/intervention_forms/
```

### **2. النماذج (Models)**
```python
# في dpcdz/home/<USER>
class DailyIntervention(models.Model)
class MedicalEvacuationDetail(models.Model)
class TrafficAccidentDetail(models.Model)
class AgriculturalFireDetail(models.Model)
class BuildingFireDetail(models.Model)
class InterventionCasualty(models.Model)

# احذف أيضاً أي نماذج أخرى مرتبطة بالتدخلات اليومية
```

### **3. الـ Views (APIs)**
```python
# في dpcdz/home/<USER>
def daily_interventions(request)
def intervention_details(request)
def save_intervention(request)
def get_interventions_by_type(request)
def save_medical_evacuation_details(request)
def save_traffic_accident_details(request)
def save_agricultural_fire_details(request)
def save_building_fire_details(request)

# احذف أي دوال أخرى مرتبطة بالتدخلات اليومية
```

### **4. الـ URLs**
```python
# في dpcdz/home/<USER>
path('coordination-center/daily-interventions/', ...)
path('coordination-center/intervention-details/', ...)
path('coordination-center/save-intervention/', ...)
path('coordination-center/get-interventions-by-type/', ...)
# وأي مسارات أخرى مرتبطة
```

### **5. قاعدة البيانات**
```bash
# احذف جداول قاعدة البيانات المرتبطة:
# - home_dailyintervention
# - home_medicalevacuationdetail
# - home_trafficaccidentdetail
# - home_agriculturalFiredetail
# - home_buildingfiredetail
# - home_interventioncasualty
# وأي جداول أخرى مرتبطة

# قم بعمل migration جديد لحذف الجداول:
python manage.py makemigrations
python manage.py migrate
```

---

## 💾 النسخ الاحتياطي الإجباري

**قبل الحذف، يجب عليك إنشاء نسخة احتياطية:**

### **1. نسخ احتياطي للملفات**
```bash
# أنشئ مجلد النسخ الاحتياطي
mkdir -p backup_daily_interventions_$(date +%Y%m%d_%H%M%S)

# انسخ الملفات الحالية
cp dpcdz/templates/coordination_center/daily_interventions.html backup_daily_interventions_*/
cp dpcdz/templates/coordination_center/intervention_details.html backup_daily_interventions_*/
cp -r dpcdz/templates/coordination_center/intervention_forms/ backup_daily_interventions_*/

# انسخ أجزاء من models.py و views.py المرتبطة
grep -A 50 "class DailyIntervention" dpcdz/home/<USER>/models_backup.py
grep -A 30 "def daily_interventions" dpcdz/home/<USER>/views_backup.py
```

### **2. نسخ احتياطي لقاعدة البيانات**
```bash
# تصدير البيانات الحالية
python manage.py dumpdata home.DailyIntervention > backup_daily_interventions_*/daily_interventions_data.json
python manage.py dumpdata home.MedicalEvacuationDetail > backup_daily_interventions_*/medical_data.json
python manage.py dumpdata home.TrafficAccidentDetail > backup_daily_interventions_*/traffic_data.json
python manage.py dumpdata home.AgriculturalFireDetail > backup_daily_interventions_*/agricultural_data.json
python manage.py dumpdata home.BuildingFireDetail > backup_daily_interventions_*/building_data.json
```

---

## 🏗️ إعادة البناء من الصفر

### **المرجع الأساسي:**
استخدم الملف `intervention_structure_final.md` كمرجع كامل للبناء الجديد.

### **1. إنشاء النماذج الجديدة**
```python
# في dpcdz/home/<USER>
# اتبع الهيكل الموثق في intervention_structure_final.md
# تأكد من:
# - النموذج الأساسي DailyIntervention
# - 4 نماذج متخصصة مع OneToOneField
# - جميع الحقول المطلوبة
# - العلاقات الصحيحة
```

### **2. إنشاء الصفحات الجديدة**
```html
<!-- dpcdz/templates/coordination_center/daily_interventions.html -->
<!-- اتبع الهيكل الكامل الموثق -->
<!-- تأكد من جميع النماذج المنبثقة المتخصصة -->

<!-- dpcdz/templates/coordination_center/intervention_details.html -->
<!-- اتبع الهيكل الكامل الموثق -->
<!-- تأكد من جميع الجداول المتخصصة -->
```

### **3. إنشاء الـ APIs الجديدة**
```python
# في dpcdz/home/<USER>
# اتبع الهيكل الموثق
# تأكد من جميع دوال الحفظ والجلب
```

### **4. إنشاء الـ URLs الجديدة**
```python
# في dpcdz/home/<USER>
# أضف جميع المسارات المطلوبة
```

---

## ✅ قائمة التحقق الإجبارية

### **قبل البدء:**
- [ ] قرأت `intervention_structure_final.md` بالكامل
- [ ] أنشأت نسخة احتياطية كاملة
- [ ] فهمت الهيكل المطلوب بالكامل

### **أثناء الحذف:**
- [ ] حذفت جميع الملفات المذكورة
- [ ] حذفت جميع النماذج من models.py
- [ ] حذفت جميع الدوال من views.py
- [ ] حذفت جميع المسارات من urls.py
- [ ] نفذت migrations لحذف الجداول

### **أثناء إعادة البناء:**
- [ ] أنشأت النماذج الجديدة بالهيكل الصحيح
- [ ] أنشأت الصفحات بالهيكل الكامل الموثق
- [ ] أنشأت جميع الـ APIs المطلوبة
- [ ] أضفت جميع المسارات
- [ ] نفذت migrations للجداول الجديدة

### **بعد الانتهاء:**
- [ ] اختبرت إنشاء تدخل جديد
- [ ] اختبرت نماذج عملية التعرف الأربعة
- [ ] اختبرت نماذج إنهاء المهمة الأربعة
- [ ] اختبرت عرض البيانات في الجداول الأربعة
- [ ] تأكدت من عمل الفلاتر والبحث

---

## 🎯 المتطلبات الأساسية

### **1. النماذج المنفصلة والمتخصصة**
- كل نوع تدخل له نموذج متخصص منفصل
- لا خلط بين أنواع التدخلات المختلفة
- علاقة OneToOneField بين النموذج الأساسي والمتخصص

### **2. النماذج المنبثقة المتخصصة**
- نموذج عملية التعرف لكل نوع تدخل
- نموذج إنهاء المهمة لكل نوع تدخل
- حقول مختلفة ومتخصصة لكل نوع

### **3. الجداول المتخصصة**
- جدول منفصل لكل نوع تدخل
- أعمدة مختلفة ومتخصصة لكل جدول
- عرض البيانات الصحيحة من النماذج المتخصصة

### **4. عدم وجود عمود "الحالة (سائق/راكب/مشاة)"**
- هذا العمود فقط في جدول حوادث المرور
- لا يظهر في جداول الإجلاء الصحي أو الحرائق

---

## 🚨 تحذيرات مهمة

### **لا تفعل هذا:**
- ❌ لا تحاول إصلاح الكود الحالي
- ❌ لا تعيد استخدام أي جزء من الكود القديم
- ❌ لا تخلط بين أنواع التدخلات المختلفة
- ❌ لا تنس النسخة الاحتياطية

### **افعل هذا:**
- ✅ احذف كل شيء واعد البناء من الصفر
- ✅ اتبع الهيكل الموثق بدقة
- ✅ اختبر كل جزء بعد إنشائه
- ✅ تأكد من عمل النماذج المنفصلة

---

## 📞 في حالة المشاكل

إذا واجهت أي مشاكل:

1. **راجع الملف المرجعي**: `intervention_structure_final.md`
2. **تحقق من النسخة الاحتياطية**: للمقارنة فقط، لا للاستخدام
3. **اختبر كل جزء منفصلاً**: قبل الانتقال للجزء التالي
4. **تأكد من الهيكل المنفصل**: كل نوع تدخل منفصل تماماً

---

## 🎉 النتيجة المطلوبة

بعد الانتهاء، يجب أن يكون لديك:

- **نظام نظيف ومنظم** بدون أي كود قديم
- **نماذج منفصلة ومتخصصة** لكل نوع تدخل
- **صفحات تعمل بشكل مثالي** مع جميع الوظائف
- **جداول تعرض البيانات الصحيحة** من النماذج المتخصصة
- **نظام يتبع الهيكل الموثق بدقة**

**حظاً موفقاً في إعادة البناء! 🚀**

---

*ملاحظة: هذا الحذف وإعادة البناء ضروري لضمان نظافة الكود وعدم وجود تداخلات أو مشاكل من الكود القديم.*
