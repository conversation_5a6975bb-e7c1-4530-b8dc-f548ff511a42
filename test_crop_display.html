<!DOCTYPE html>
<html>
<head>
    <title>اختبار عرض بيانات حريق المحاصيل</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار عرض بيانات حريق المحاصيل</h1>
    <div id="result"></div>

    <script>
        // بيانات تجريبية مماثلة لما يأتي من API
        const intervention = {
            "id": 69,
            "time": "13:39",
            "intervention_type": "agricultural-fire",
            "location": "مكان الحريق",
            "material_damage": "ملاحظة الثانية",
            "end_time": "16:45",
            "final_injured_count": 1,
            "final_deaths_count": 1,
            "final_notes": "ملاحظة الثالثة",
            "injured_details": [
                {
                    "name": "الاسم و اللقب 1",
                    "age": "28",
                    "gender": "male"
                }
            ],
            "fatality_details": [
                {
                    "name": "الاسم و اللقب المتوفي",
                    "age": "26",
                    "gender": "female"
                }
            ],
            "fire_type": "",
            "fire_points_count": 0,
            "wind_direction": "",
            "wind_speed": 0.0,
            "population_threat": "نعم",
            "evacuation_location": "نزل الولاية",
            "affected_families": 3,
            "present_entities": ["الدرك"],
            "support_request": "",
            "area_losses": "0.0 + 0.0 + 0.0 + 0.0 هكتار",
            "count_losses": "0 حزمة تبن + 0 كيس + 0 شجرة + 0 خلية",
            "saved_area": 0.0,
            "saved_straw_bales": 0,
            "saved_properties": null,
            "agricultural_fire_detail": {
                "fire_type": "",
                "fire_sources_count": 0,
                "wind_direction": "",
                "wind_speed": 0.0,
                "population_threat": true,
                "evacuation_location": "نزل الولاية",
                "affected_families_count": 3,
                "support_request": "",
                "saved_area": 0.0,
                "saved_straw_bales": 0,
                "saved_equipment": null,
                "final_notes": null
            }
        };

        // دالة تنسيق بيانات الأشخاص
        function formatPersonDetails(personsData) {
            if (!personsData || personsData.length === 0) {
                return {
                    names: '-',
                    ages: '-',
                    genders: '-'
                };
            }
            
            return {
                names: personsData.map(p => p.name || '-').join('<br>'),
                ages: personsData.map(p => p.age || '-').join('<br>'),
                genders: personsData.map(p => translateValue(p.gender) || '-').join('<br>')
            };
        }

        // دالة الترجمة
        function translateValue(value) {
            const translations = {
                'male': 'ذكر',
                'female': 'أنثى',
                'yes': 'نعم',
                'no': 'لا',
                'نعم': 'نعم',
                'لا': 'لا',
                'gendarmerie': 'درك'
            };
            return translations[value] || value || '-';
        }

        // دالة تنسيق قائمة بسيطة
        function formatSimpleList(listData) {
            if (!listData || listData.length === 0) return '-';
            return listData.map(item => translateValue(item) || item).join('<br>');
        }

        // اختبار منطق العرض
        function testCropDisplay() {
            const agriculturalDetail = intervention.agricultural_fire_detail || {};
            
            // استخدام البيانات من المصادر المتعددة
            const victimsData = intervention.injured_details || intervention.victims_details || [];
            const fatalitiesData = intervention.fatality_details || intervention.fatalities_details || [];
            
            const victimDetails = formatPersonDetails(victimsData);
            const fatalityDetails = formatPersonDetails(fatalitiesData);
            const presentEntities = formatSimpleList(intervention.present_entities || []);

            const results = {
                "نوع المحصول المحترق": translateValue(intervention.fire_type || agriculturalDetail.fire_type || intervention.intervention_subtype) || '-',
                "عدد البؤر": intervention.fire_points_count || agriculturalDetail.fire_sources_count || intervention.fire_sources_count || '-',
                "اتجاه الرياح": translateValue(intervention.wind_direction || agriculturalDetail.wind_direction) || '-',
                "سرعة الرياح": intervention.wind_speed || agriculturalDetail.wind_speed || '-',
                "تهديد للسكان": translateValue(intervention.population_threat || (agriculturalDetail.population_threat !== undefined ? (agriculturalDetail.population_threat ? 'نعم' : 'لا') : '')) || '-',
                "مكان إجلاء السكان": intervention.evacuation_location || agriculturalDetail.evacuation_location || '-',
                "عدد العائلات المتأثرة": intervention.affected_families || intervention.affected_families_count || agriculturalDetail.affected_families_count || '-',
                "الجهات الحاضرة": presentEntities,
                "طلب الدعم": translateValue(intervention.support_request || agriculturalDetail.support_request) || '-',
                "عدد الضحايا": victimsData.length || intervention.final_injured_count || 0,
                "أسماء الضحايا": victimDetails.names,
                "أعمار الضحايا": victimDetails.ages,
                "جنس الضحايا": victimDetails.genders,
                "عدد الوفيات": fatalitiesData.length || intervention.final_deaths_count || 0,
                "أسماء الوفيات": fatalityDetails.names,
                "أعمار الوفيات": fatalityDetails.ages,
                "جنس الوفيات": fatalityDetails.genders,
                "مساحة منقذة": intervention.saved_area || agriculturalDetail.saved_area || '-',
                "حزم التبن المنقذة": intervention.saved_straw_bales || agriculturalDetail.saved_straw_bales || '-',
                "ممتلكات منقذة": intervention.saved_properties || agriculturalDetail.saved_equipment || '-',
                "ملاحظات ختامية": intervention.final_notes || intervention.casualties_detail || agriculturalDetail.final_notes || '-'
            };

            let html = '<table border="1" style="border-collapse: collapse; width: 100%;">';
            html += '<tr><th>الحقل</th><th>القيمة</th></tr>';
            
            for (const [field, value] of Object.entries(results)) {
                html += `<tr><td>${field}</td><td>${value}</td></tr>`;
            }
            
            html += '</table>';
            
            document.getElementById('result').innerHTML = html;
        }

        // تشغيل الاختبار عند تحميل الصفحة
        testCropDisplay();
    </script>
</body>
</html>
